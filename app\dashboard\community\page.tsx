'use client'

import { useState, useEffect } from 'react'
import { DashboardLayout } from '@/components/dashboard-layout'
import { collection, addDoc, query, orderBy, limit, onSnapshot } from 'firebase/firestore'
import { db, auth } from '@/components/firebase/config'
import { User, MessageSquare, ThumbsUp, Send, Dumbbell } from 'lucide-react'

interface Post {
  id: string
  content: string
  author: string
  createdAt: Date
  likes: number
}

export default function Community() {
  const [posts, setPosts] = useState<Post[]>([])
  const [newPost, setNewPost] = useState('')

  useEffect(() => {
    const q = query(collection(db, 'community_posts'), orderBy('createdAt', 'desc'), limit(20))
    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const fetchedPosts: Post[] = []
      querySnapshot.forEach((doc) => {
        fetchedPosts.push({ id: doc.id, ...doc.data() } as Post)
      })
      setPosts(fetchedPosts)
    })

    return () => unsubscribe()
  }, [])

  const handleSubmitPost = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newPost.trim() || !auth.currentUser) return

    try {
      await addDoc(collection(db, 'community_posts'), {
        content: newPost,
        author: auth.currentUser.displayName || 'Anonymous',
        createdAt: new Date(),
        likes: 0
      })
      setNewPost('')
    } catch (error) {
      console.error('Error adding post:', error)
    }
  }

  return (
    <DashboardLayout>
      <div className="flex flex-row ">
        <Dumbbell className="h-10 w-10  text-blue-500 group-hover:text-white" />
      <h1 className="text-2xl font-bold mb-6 ml-5">Intelligentfitness Community</h1>
      </div>
      <div className="space-y-6">
        <form onSubmit={handleSubmitPost} className="space-y-4">
          <textarea
            value={newPost}
            onChange={(e) => setNewPost(e.target.value)}
            placeholder="Share your fitness journey..."
            className="w-full p-3 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            rows={3}
          ></textarea>
          <button
            type="submit"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 flex items-center"
          >
            <Send className="mr-2 h-5 w-5" />
            Post
          </button>
        </form>
        <div className="space-y-6">
          {posts.map((post) => (
            <div key={post.id} className="bg-white p-4 rounded-lg shadow">
              <div className="flex items-center mb-2">
                <User className="h-6 w-6 text-gray-500 mr-2" />
                <span className="font-semibold">{post.author}</span>
              </div>
              <p className="text-gray-700 mb-3">{post.content}</p>
              <div className="flex items-center justify-between text-sm text-gray-500">
                <span>{post.createdAt.toLocaleString()}</span>
                <div className="flex items-center">
                  <ThumbsUp className="h-5 w-5 mr-1" />
                  <span>{post.likes}</span>
                  <MessageSquare className="h-5 w-5 ml-3 mr-1" />
                  <span>0</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </DashboardLayout>
  )
}

