import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { KickStarterModal } from '@/components/DashboardModal/kick-starter-modal';
import TrackingTab from '@/components/workout-tracking/TrackingTab';
import ExerciseTracker from '@/components/workout-tracking/ExerciseTracker';
import { getAuth } from 'firebase/auth';
import { collection, getDocs, query, where, doc, getDoc, setDoc } from 'firebase/firestore';

// Mock Firebase
jest.mock('firebase/auth', () => ({
  getAuth: jest.fn(),
}));

jest.mock('firebase/firestore', () => ({
  getFirestore: jest.fn(),
  doc: jest.fn(),
  getDoc: jest.fn(),
  collection: jest.fn(),
  query: jest.fn(),
  where: jest.fn(),
  getDocs: jest.fn(),
  setDoc: jest.fn(),
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock data
const mockKickStarterData = {
  scheduleId: 'test-schedule-id',
  workoutReference: 'test-workout-reference',
  title: 'Test Workout Plan',
  schedule: {
    title: 'Test Schedule',
    days: [
      {
        day: 1,
        name: 'Day 1: Full Body',
        duration: 60,
        warm_up: [
          {
            exercise: 'Jumping Jacks',
            libraryId: 'warm-up-1',
            duration: 5,
          },
        ],
        resistance_training: [
          {
            exercise: 'Push-ups',
            libraryId: 'resistance-1',
            sets: 3,
            reps: 10,
            notes: 'Keep your core tight',
          },
        ],
        cardio_training: [
          {
            exercise: 'Running',
            libraryId: 'cardio-1',
            duration: 20,
            notes: 'Moderate pace',
          },
        ],
        cool_down: [
          {
            exercise: 'Stretching',
            libraryId: 'cool-down-1',
            duration: 5,
          },
        ],
      },
    ],
    lastUpdated: '2023-01-01',
  },
};

// Mock user
const mockUser = {
  email: '<EMAIL>',
};

// Mock tracking data
const mockTrackingData = {
  'warm-up-1': {
    sessionId: 'session-1',
    workoutReferenceId: 'test-workout-reference',
    planType: 'kick_starter',
    userId: '<EMAIL>',
    workoutDay: 1,
    exerciseName: 'Jumping Jacks',
    libraryId: 'warm-up-1',
    exerciseType: 'warm_up',
    completionStatus: 'complete',
    duration: 5,
    difficultyRating: 3,
    energyLevel: 4,
    dateCompleted: '2023-01-01T12:00:00Z',
    weekNumber: 1,
    createdAt: '2023-01-01T12:00:00Z',
    updatedAt: '2023-01-01T12:00:00Z',
  },
};

describe('Workout Tracking Components', () => {
  beforeEach(() => {
    // Mock auth
    (getAuth as jest.Mock).mockReturnValue({
      currentUser: mockUser,
    });

    // Mock Firestore queries
    (getDocs as jest.Mock).mockResolvedValue({
      empty: false,
      docs: Object.entries(mockTrackingData).map(([key, value]) => ({
        data: () => value,
      })),
    });

    // Mock Firestore document
    (getDoc as jest.Mock).mockResolvedValue({
      exists: () => true,
      data: () => mockKickStarterData.schedule,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('KickStarterModal renders with tracking tab', async () => {
    render(
      <KickStarterModal
        isOpen={true}
        onClose={() => {}}
        data={mockKickStarterData}
      />
    );

    // Check if the Track tab is rendered
    const trackTab = screen.getByText('Track');
    expect(trackTab).toBeInTheDocument();

    // Click on the Track tab
    fireEvent.click(trackTab);

    // Wait for tracking data to load
    await waitFor(() => {
      expect(screen.getByText('Day 1 Progress')).toBeInTheDocument();
    });
  });

  test('TrackingTab displays exercise cards with completion status', async () => {
    render(
      <TrackingTab
        workoutReferenceId="test-workout-reference"
        activeDay={mockKickStarterData.schedule.days[0]}
        planType="kick_starter"
      />
    );

    // Wait for tracking data to load
    await waitFor(() => {
      expect(screen.getByText('Day 1 Progress')).toBeInTheDocument();
    });

    // Check if exercise cards are rendered
    expect(screen.getByText('Jumping Jacks')).toBeInTheDocument();
    expect(screen.getByText('Push-ups')).toBeInTheDocument();
    expect(screen.getByText('Running')).toBeInTheDocument();
    expect(screen.getByText('Stretching')).toBeInTheDocument();

    // Check if Track Progress buttons are rendered
    const trackButtons = screen.getAllByText('Track Progress');
    expect(trackButtons.length).toBe(4);
  });

  test('ExerciseTracker allows tracking exercise progress', async () => {
    render(
      <ExerciseTracker
        userId="<EMAIL>"
        workoutReferenceId="test-workout-reference"
        planType="kick_starter"
        workoutDay={1}
        dayName="Day 1: Full Body"
        exerciseName="Push-ups"
        libraryId="resistance-1"
        exerciseType="resistance_training"
        sets={3}
        reps={10}
        onTrackingComplete={() => {}}
        onCancel={() => {}}
      />
    );

    // Check if the form is rendered
    expect(screen.getByText('Track: Push-ups')).toBeInTheDocument();

    // Check if weight input is rendered
    const weightInput = screen.getByPlaceholderText('Weight (kg/lbs)');
    expect(weightInput).toBeInTheDocument();

    // Check if sets buttons are rendered
    const setButtons = screen.getAllByText(/^[1-3]$/);
    expect(setButtons.length).toBe(3);

    // Check if difficulty rating is rendered
    expect(screen.getByText('Difficulty Rating')).toBeInTheDocument();

    // Check if energy level is rendered
    expect(screen.getByText('Energy Level')).toBeInTheDocument();

    // Check if notes textarea is rendered
    const notesTextarea = screen.getByPlaceholderText('Add any notes about this exercise...');
    expect(notesTextarea).toBeInTheDocument();

    // Check if save button is rendered
    const saveButton = screen.getByText('Save Progress');
    expect(saveButton).toBeInTheDocument();

    // Check if skip button is rendered
    const skipButton = screen.getByText('Skip');
    expect(skipButton).toBeInTheDocument();

    // Fill out the form
    fireEvent.change(weightInput, { target: { value: '50' } });
    fireEvent.click(setButtons[2]); // Complete all 3 sets

    // Click save button
    fireEvent.click(saveButton);

    // Check if setDoc was called with the correct path
    await waitFor(() => {
      expect(setDoc).toHaveBeenCalled();
      expect(doc).toHaveBeenCalledWith(expect.anything(), 'IF_users/<EMAIL>/Profile/tracking_data/expect.anything()');
    });
  });
});
