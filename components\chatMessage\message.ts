import { Timestamp } from "firebase-admin/firestore";
import { db } from "../firebase/config";
import {
  collection,
  addDoc,
  getDocs,
  serverTimestamp,
  onSnapshot,
  query,
  orderBy,
} from "firebase/firestore";

// Interfaces for type safety
interface Message {
  id?: string;
  createdAt: Timestamp;
  text: string;
  userId: string;
  role: "user" | "assistant";
}

/**
 * Adds a message to a specific chat for a user
 * @param userId - The unique identifier of the user
 * @param chatId - The unique identifier of the chat
 * @param text - The message content
 * @param role - Whether the message is from the user or assistant
 * @returns Promise containing the created document ID
 * @throws Error if userId or chatId is empty or if database operation fails
 */
const addMessage = async (
  userId: string,
  chatId: string,
  text: string,
  role: "user" | "assistant"
): Promise<string> => {
  try {
    // Validate inputs to prevent Firestore path errors
    if (!userId || userId.trim() === "") {
      throw new Error("userId is required and cannot be empty");
    }
    if (!chatId || chatId.trim() === "") {
      throw new Error("chatId is required and cannot be empty");
    }
    
    const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
    const message: Omit<Message, "id" | "createdAt"> = {
      text,
      userId,
      role,
    };
    const docRef = await addDoc(messagesRef, {
      ...message,
      createdAt: serverTimestamp(),
    });
    console.log("Message added with ID:", docRef.id);
    return docRef.id; // Return the document ID for further use if needed
  } catch (error) {
    console.error("Error adding message:", error);
    throw new Error(`Failed to add message: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
};

/**
 * Sets up a real-time listener for messages in a chat
 * @param userId - The unique identifier of the user
 * @param chatId - The unique identifier of the chat
 * @param callback - Function to call with updated messages
 * @returns Unsubscribe function to stop listening for updates
 */
const fetchMessages = (
  userId: string,
  chatId: string,
  callback: (messages: Message[]) => void
): (() => void) => {
  // Input validation to prevent Firestore path errors
  if (!userId || userId.trim() === "") {
    console.error("Error: userId is required and cannot be empty");
    callback([]); // Return empty array on error
    return () => {}; // Return no-op unsubscribe function
  }
  
  if (!chatId || chatId.trim() === "") {
    console.error("Error: chatId is required and cannot be empty");
    callback([]); // Return empty array on error
    return () => {}; // Return no-op unsubscribe function
  }

  const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
  const q = query(messagesRef, orderBy("createdAt", "asc"));

  const unsubscribe = onSnapshot(
    q,
    (snapshot) => {
      const messages: Message[] = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Message[];
      console.log("Messages updated:", messages);
      callback(messages);
    },
    (error) => {
      console.error("Error fetching messages:", error);
      callback([]); // Provide empty array on error to avoid breaking the UI
    }
  );

  return unsubscribe;
};

/**
 * Fetches the user's work reference for Pinecone namespace
 * @param userId - The unique identifier of the user
 * @returns Promise containing the workReference string or default fallback
 */
const fetchWorkReference = async (userId: string): Promise<string> => {
  try {
    // Input validation to prevent Firestore path errors
    if (!userId || userId.trim() === "") {
      console.warn("userId is required and cannot be empty");
      return "default";
    }
    
    const workoutPlanRef = collection(db, `IF_users/${userId}/Profile`);
    const snapshot = await getDocs(workoutPlanRef);
    if (snapshot.empty) {
      console.warn(`No profile found for userId: ${userId}`);
      return "default";
    }
    const workReference = snapshot.docs[0]?.data().workReference || "default";
    console.log("Fetched workReference:", workReference);
    return workReference;
  } catch (error) {
    console.error("Error fetching workReference:", error);
    return "default"; // Fallback value
  }
};

export { addMessage, fetchMessages, fetchWorkReference };