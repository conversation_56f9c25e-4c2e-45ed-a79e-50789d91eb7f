'use client'

import { useState } from 'react'
import { DashboardLayout } from '@/components/dashboard-layout'
import { Loader2 } from 'lucide-react'

export default function GenerateWorkout() {
  const [workoutPlan, setWorkoutPlan] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const handleGenerateWorkout = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/generate-workout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ /* user preferences and data */ }),
      })
      const data = await response.json()
      setWorkoutPlan(data.workoutPlan)
    } catch (error) {
      console.error('Error generating workout plan:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <DashboardLayout>
      <h1 className="text-3xl font-bold mb-6">Generate Workout Plan</h1>
      <div className="space-y-6">
        <p className="text-gray-600">Click the button below to generate a personalized workout plan based on your profile and preferences.</p>
        <button
          onClick={handleGenerateWorkout}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center"
        >
          {loading ? (
            <>
              <Loader2 className="animate-spin mr-2 h-5 w-5" />
              Generating...
            </>
          ) : (
            'Generate Workout Plan'
          )}
        </button>
        {workoutPlan && (
          <div className="mt-6">
            <h2 className="text-2xl font-semibold mb-2">Your Personalized Workout Plan</h2>
            <pre className="bg-gray-100 p-4 rounded-md whitespace-pre-wrap">{workoutPlan}</pre>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}

