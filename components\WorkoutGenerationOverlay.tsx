// components/WorkoutGenerationOverlay.tsx
"use client";

import React from 'react';
import { useWorkoutGeneration } from '@/components/WorkoutGenerationContext';
import { X } from 'lucide-react';

export const WorkoutGenerationOverlay: React.FC = () => {
  const { generationState, errorMessage, progress, resetGeneration } = useWorkoutGeneration();

  // Only show if not idle
  if (generationState === 'idle') return null;

  const getStateMessage = () => {
    switch (generationState) {
      case 'initiating':
        return 'Initializing AI personal trainer...';
      case 'generating-profile':
        return 'Analyzing your fitness profile...';
      case 'generating-short term workouts':
        return 'Designing your personalized short term workout plan...';
      case 'generating-short term workouts':
        return 'Designing your personalized long term workout plan...';
      case 'saving':
        return 'Finalizing your fitness program...';
      case 'completed':
        return 'Workout plan generated successfully!';
      case 'error':
        return 'Error generating workout plan';
      default:
        return 'Processing...';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 p-8 rounded-xl max-w-md w-full shadow-2xl">
        {generationState === 'error' ? (
          <>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold text-red-500">Generation Error</h2>
              <button 
                onClick={resetGeneration}
                className="text-gray-400 hover:text-white"
              >
                <X size={24} />
              </button>
            </div>
            <p className="text-white mb-4">{errorMessage || 'An unexpected error occurred while generating your workout plan.'}</p>
            <button
              onClick={resetGeneration}
              className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded transition"
            >
              Close
            </button>
          </>
        ) : (
          <>
            <h2 className="text-xl font-bold text-white mb-4">Generating Your Workout Plan</h2>
            <div className="mb-4">
              <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-500"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
            <p className="text-white">{getStateMessage()}</p>
            <p className="text-gray-400 text-sm mt-4">
              This may take a few minutes. We&apos;re using AI to create your personalized fitness program.
            </p>
          </>
        )}
      </div>
    </div>
  );
};