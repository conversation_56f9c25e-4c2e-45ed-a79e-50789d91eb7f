import { NextResponse } from "next/server"
import { generateWithImagen } from "@/components/tools/imagen"
import { generateImageTool } from "@/components/tools/generate-image"
import { ImageModel } from "@/components/tools/generate-image"

export async function POST(request: Request) {
  try {
    const { prompt, userId, model = 'gpt-image-1', size = '1024x1024', style = 'vivid', quality = 'auto' } = await request.json()

    if (!prompt) {
      return NextResponse.json({ message: "Prompt is required" }, { status: 400 })
    }

    console.log(`Received image generation request with prompt: "${prompt.substring(0, 100)}..." and model: ${model}`)

    // Enhance the prompt for better meal plan images
    // The prompt is the meal plan details from the component
    const enhancedPrompt = `Create a vibrant, professional food photography style image showcasing the following meal plan
    arranged in an appealing grid layout:
    Each meal should be clearly visible with the following details: ${prompt}.
    Each meal should have a small, elegant nutritional tag showing protein content prominently.
    The lighting should be bright and natural, emphasizing the fresh, healthy qualities of each meal.
    The overall aesthetic should appeal to fitness enthusiasts and health-conscious individuals.`

    console.log("Enhanced prompt:", enhancedPrompt.substring(0, 100) + "...")

    // Generate a unique job ID for this request
    const jobId = `meal-plan-${Date.now()}`

    // Try to generate an image using the specified model
    try {
      // If the model is Imagen, use the Imagen-specific implementation
      if (model.includes('imagen')) {
        console.log("Using Google's Imagen model for image generation")
        const result = await generateWithImagen({
          prompt: enhancedPrompt,
          model: 'imagen-3.0-generate-002',
          numberOfImages: 1,
          userId: userId,
          jobId: jobId
        })

        if (result && result.imageUrl) {
          console.log("Successfully generated image with Imagen, URL:", result.imageUrl.substring(0, 50) + "...")
          return NextResponse.json({
            imageUrl: result.imageUrl,
            jobId: result.jobId,
            namespace: result.namespace,
            model: 'imagen-3.0-generate-002'
          })
        } else {
          throw new Error("No image URL returned from Imagen")
        }
      }
      // If the model is OpenAI's gpt-image-1 or another supported model, use the generateImageTool
      else {
        console.log(`Using OpenAI's ${model} model for image generation`)
        const result = await generateImageTool.generateImage({
          prompt: enhancedPrompt,
          model: model as ImageModel,
          size: size,
          style: style,
          quality: quality,
          userId: userId
        })

        if (result && result.imageUrl) {
          console.log(`Successfully generated image with ${model}, URL:`, result.imageUrl.substring(0, 50) + "...")
          return NextResponse.json({
            imageUrl: result.imageUrl,
            jobId: result.jobId || jobId,
            namespace: result.namespace,
            model: model
          })
        } else {
          throw new Error(`No image URL returned from ${model}`)
        }
      }
    } catch (primaryModelError) {
      console.error(`Error generating image with ${model}:`, primaryModelError)

      // Try to fall back to an alternative model if the primary model fails
      try {
        // If the original model was Imagen, try OpenAI's gpt-image-1
        if (model.includes('imagen')) {
          console.log("Falling back to OpenAI's gpt-image-1 model")
          const fallbackResult = await generateImageTool.generateImage({
            prompt: enhancedPrompt,
            model: 'gpt-image-1',
            size: size,
            style: style,
            quality: quality,
            userId: userId
          })

          if (fallbackResult && fallbackResult.imageUrl) {
            console.log("Successfully generated image with gpt-image-1 fallback, URL:", fallbackResult.imageUrl.substring(0, 50) + "...")
            return NextResponse.json({
              imageUrl: fallbackResult.imageUrl,
              jobId: fallbackResult.jobId || jobId,
              namespace: fallbackResult.namespace,
              model: 'gpt-image-1',
              fallback: true
            })
          }
        }
        // If the original model was an OpenAI model, try Imagen
        else {
          console.log("Falling back to Google's Imagen model")
          const fallbackResult = await generateWithImagen({
            prompt: enhancedPrompt,
            model: 'imagen-3.0-generate-002',
            numberOfImages: 1,
            userId: userId,
            jobId: jobId
          })

          if (fallbackResult && fallbackResult.imageUrl) {
            console.log("Successfully generated image with Imagen fallback, URL:", fallbackResult.imageUrl.substring(0, 50) + "...")
            return NextResponse.json({
              imageUrl: fallbackResult.imageUrl,
              jobId: fallbackResult.jobId,
              namespace: fallbackResult.namespace,
              model: 'imagen-3.0-generate-002',
              fallback: true
            })
          }
        }
      } catch (fallbackError) {
        console.error("Fallback model also failed:", fallbackError)
      }

      // If both primary and fallback models fail, use a placeholder image
      console.log("All image generation attempts failed, using placeholder image")
      const imageUrl = "https://via.placeholder.com/800x600?text=Generated+Meal+Plan+Image"
      console.log("Using placeholder image URL:", imageUrl)

      return NextResponse.json({
        imageUrl,
        error: "Image generation failed with all models",
        fallback: "placeholder"
      })
    }
  } catch (error) {
    console.error("Error in generate-image API route:", error)
    let message = "Failed to generate image"

    if (error instanceof Error) {
      message = error.message
    }

    return NextResponse.json({ message }, { status: 500 })
  }
}
