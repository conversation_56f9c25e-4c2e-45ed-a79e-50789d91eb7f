import type { SevenDaySchedule } from "@/types/schedule"

export function generateExpandedSchedule(originalSchedule: SevenDaySchedule): SevenDaySchedule {
  const newSchedule: SevenDaySchedule = {
    schedule: { ...originalSchedule.schedule },
    weeklyGoals: originalSchedule.weeklyGoals || {
      primary: "Improve overall fitness and strength",
      secondary: ["Increase flexibility", "Enhance cardiovascular endurance"]
    },
    recovery: originalSchedule.recovery || {
      restDays: ["Sunday"],
      stretchingRoutine: "10-minute full-body stretch routine",
      coolDown: "5-minute light cardio followed by static stretching"
    },
    nutrition: originalSchedule.nutrition || {
      preworkout: "Light meal rich in complex carbohydrates and protein",
      postworkout: "Protein shake or meal with balanced macronutrients",
      hydration: "Drink water throughout the day, aiming for 2-3 liters daily"
    }
  }

  // Helper function to count training days for a specific week
  const countTrainingDaysInWeek = (schedule: SevenDaySchedule["schedule"], weekNumber: number): number => {
    return Object.keys(schedule).filter(day => day.startsWith(`Week ${weekNumber}`)).length
  }

  // Helper function to get available days in a week
  const getAvailableDays = (weekNumber: number, existingDays: string[]): string[] => {
    const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    const weekDays = existingDays.map(day => day.split(' - ')[1])
    
    return allDays.filter(day => {
      if (weekDays.includes(day)) return false
      const dayIndex = allDays.indexOf(day)
      const hasDayBefore = weekDays.includes(allDays[dayIndex - 1])
      const hasDayAfter = weekDays.includes(allDays[dayIndex + 1])
      return !hasDayBefore && !hasDayAfter
    })
  }

  const allDays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

  // Process each week (1-4)
  for (let week = 1; week <= 4; week++) {
    const existingDays = Object.keys(newSchedule.schedule).filter(day => 
      day.startsWith(`Week ${week}`)
    )
    
    const currentTrainingDays = countTrainingDaysInWeek(newSchedule.schedule, week)
    const daysToAdd = 4 - currentTrainingDays
    
    if (daysToAdd > 0) {
      const availableDays = getAvailableDays(week, existingDays)
      
      for (let i = 0; i < Math.min(daysToAdd, availableDays.length); i++) {
        const newDay = `Week ${week} - ${availableDays[i]}`
        const workoutType = i % 3
        
        newSchedule.schedule[newDay] = {
          focus: workoutType === 0 ? "Strength & Conditioning" :
                workoutType === 1 ? "Mobility & Core" : "HIIT & Cardio",
          location: "Gym",
          duration: "45 minutes",
          intensity: workoutType === 2 ? "High" : "Moderate",
          coreWork: workoutType === 1 ? "Primary" : "Secondary",
          cardio: workoutType === 2 ? "High" : "Moderate",
          exercises: [
            {
              exerciseName: workoutType === 0 ? "Compound Lifts Circuit" :
                          workoutType === 1 ? "Dynamic Mobility Flow" : "HIIT Circuit",
              specs: {
                duration: workoutType === 2 ? "30 seconds work, 15 seconds rest" : "3 sets of 12-15 reps",
                weight: workoutType === 0 ? "Progressive load" : "Bodyweight"
              },
              completed: false,
              coachInstructions: {
                instructions: "Maintain proper form throughout the session",
                mistakes: "Rushing through movements, compromising form",
                modifications: "Adjust intensity and complexity as needed"
              }
            },
            {
              exerciseName: workoutType === 0 ? "Strength Finisher" :
                          workoutType === 1 ? "Core Integration" : "Endurance Builder",
              specs: {
                duration: workoutType === 2 ? "4 rounds" : "3 sets",
                weight: workoutType === 0 ? "Moderate weight" : "Bodyweight"
              },
              completed: false,
              coachInstructions: {
                instructions: "Focus on quality movement patterns",
                mistakes: "Overexertion, poor breathing patterns",
                modifications: "Scale movements to maintain proper form"
              }
            }
          ]
        }
      }
    }

    // Update recovery.restDays based on the schedule
    newSchedule.recovery.restDays = ['Sunday', ...allDays.filter(day => 
      !Object.keys(newSchedule.schedule).some(scheduleDay => 
        scheduleDay.includes(day)
      )
    )]
  }

  return newSchedule
}
