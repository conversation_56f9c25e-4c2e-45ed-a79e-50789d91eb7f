"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Utensils, Apple, Calendar, Settings, Check, Loader2, BookOpen } from "lucide-react"
import { getFirestore, doc, getDoc, setDoc, collection, getDocs, query, orderBy, limit } from "firebase/firestore"
import { v4 as uuidv4 } from 'uuid'
import { getAuth } from "firebase/auth"
import MealPlanGenerator from "@/components/nutrition/MealPlanGenerator"
import SavedMealsTab from "./SavedMealsTab"
import { useWorkoutContext } from "@/contexts/WorkoutContext"

interface MealPlanTabProps {
  userEmail?: string
}

export default function MealPlanTab({ userEmail: propUserEmail }: MealPlanTabProps) {
  const [activeTab, setActiveTab] = useState<"nutritional-advice" | "preferences" | "meal-planner" | "saved-meals">("nutritional-advice")
  const [nutritionAdvice, setNutritionAdvice] = useState<string>("")
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  // Using _userEmail with underscore prefix to indicate it's used indirectly
  const [_userEmail, setUserEmail] = useState<string | null>(propUserEmail || null)

  // Get workout context for current day
  const { currentDay, availableDays, setAvailableDays } = useWorkoutContext()

  // Update available days based on the workout plan
  useEffect(() => {
    if (_userEmail) {
      // Fetch the workout plan to get available days
      const fetchWorkoutDays = async () => {
        try {
          const db = getFirestore()
          const workoutPlanRef = doc(db, "IF_users", _userEmail, "Profile", "workoutplan")
          const workoutPlanSnap = await getDoc(workoutPlanRef)

          if (workoutPlanSnap.exists()) {
            const workoutPlanData = workoutPlanSnap.data()
            const workoutReference = workoutPlanData?.WorkoutReference

            if (workoutReference) {
              const fileRef = doc(db, "users", _userEmail, "files", workoutReference)
              const fileSnap = await getDoc(fileRef)

              if (fileSnap.exists()) {
                const fileData = fileSnap.data()
                const exerciseProgram = fileData?.exerciseProgram

                if (exerciseProgram) {
                  // Extract days from the exercise program
                  const dayRegex = /Day\s+(\d+)/g
                  const days = new Set<string>()
                  let match

                  while ((match = dayRegex.exec(exerciseProgram)) !== null) {
                    days.add(`Day ${match[1]}`)
                  }

                  if (days.size > 0) {
                    const sortedDays = Array.from(days).sort((a, b) => {
                      const dayA = parseInt(a.replace("Day ", ""))
                      const dayB = parseInt(b.replace("Day ", ""))
                      return dayA - dayB
                    })

                    console.log("Found workout days:", sortedDays)
                    setAvailableDays(sortedDays)
                  }
                }
              }
            }
          }
        } catch (error) {
          console.error("Error fetching workout days:", error)
        }
      }

      fetchWorkoutDays()
    }
  }, [_userEmail, setAvailableDays])

  // Meal preferences state
  const [dietaryPreferences, setDietaryPreferences] = useState<string[]>([])
  const [allergies, setAllergies] = useState<string[]>([])
  const [cuisinePreferences, setCuisinePreferences] = useState<string[]>([])
  const [mealComplexity, setMealComplexity] = useState<string>("medium")
  const [cookingTime, setCookingTime] = useState<string>("30-60")
  const [preferenceId, setPreferenceId] = useState<string>("")

  // Preferences save/load state
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const [saveStatus, setSaveStatus] = useState<"idle" | "saving" | "saved" | "error">("idle")
  const [isLoadingPreferences, setIsLoadingPreferences] = useState<boolean>(true)

  // Function to save preferences to Firestore
  const savePreferences = async () => {
    if (!_userEmail) {
      console.error("Cannot save preferences: No user email")
      setSaveStatus("error")
      return
    }

    try {
      setSaveStatus("saving")
      setIsSaving(true)

      const db = getFirestore()
      // Generate a new UUID if we don't have one, or use the existing one for updates
      const prefId = preferenceId || uuidv4()

      // Use the specified path: /foodKhare_users/[userEmail]/Preferences/[UUidv4]
      const preferencesRef = doc(db, "foodKhare_users", _userEmail, "Preferences", prefId)

      const preferencesData = {
        dietaryPreferences,
        allergies,
        cuisinePreferences,
        mealComplexity,
        cookingTime,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      await setDoc(preferencesRef, preferencesData)

      // Store the preference ID for future updates
      setPreferenceId(prefId)

      setSaveStatus("saved")
      console.log("Meal preferences saved successfully with ID:", prefId)

      // Reset to idle after 3 seconds
      setTimeout(() => {
        setSaveStatus("idle")
      }, 3000)
    } catch (error) {
      console.error("Error saving meal preferences:", error)
      setSaveStatus("error")
    } finally {
      setIsSaving(false)
    }
  }

  // Function to load preferences from Firestore
  const loadPreferences = async (userEmail: string) => {
    try {
      setIsLoadingPreferences(true)

      const db = getFirestore()
      // Get the collection reference
      const preferencesCollectionRef = collection(db, "foodKhare_users", userEmail, "Preferences")

      // Query to get the most recent preferences document
      const q = query(preferencesCollectionRef, orderBy("updatedAt", "desc"), limit(1))
      const querySnapshot = await getDocs(q)

      if (!querySnapshot.empty) {
        // Get the most recent document
        const docSnapshot = querySnapshot.docs[0]
        const data = docSnapshot.data()
        const id = docSnapshot.id

        // Set preferences from Firestore
        setDietaryPreferences(data.dietaryPreferences || [])
        setAllergies(data.allergies || [])
        setCuisinePreferences(data.cuisinePreferences || [])
        setMealComplexity(data.mealComplexity || "medium")
        setCookingTime(data.cookingTime || "30-60")

        // Store the preference ID for future updates
        setPreferenceId(id)

        console.log("Meal preferences loaded successfully from ID:", id)
      } else {
        console.log("No saved meal preferences found")
        // Reset the preference ID since we don't have any saved preferences
        setPreferenceId("")
      }
    } catch (error) {
      console.error("Error loading meal preferences:", error)
    } finally {
      setIsLoadingPreferences(false)
    }
  }

  useEffect(() => {
    const auth = getAuth()
    const user = auth.currentUser
    const email = propUserEmail || user?.email || null

    setUserEmail(email)

    if (email) {
      // Load nutrition data
      fetchNutritionData(email)
        .then(data => {
          setNutritionAdvice(data)
          setIsLoading(false)
        })
        .catch(err => {
          console.error("Error fetching nutrition data:", err)
          setError("Failed to load nutrition data. Please try again later.")
          setIsLoading(false)
        })

      // Load preferences separately
      loadPreferences(email)
        .catch(err => {
          console.error("Error loading preferences:", err)
          // Don't set the main error state for this, just log it
        })
    } else {
      setError("User not logged in. Please log in to view nutrition advice.")
      setIsLoading(false)
      setIsLoadingPreferences(false)
    }
  }, [propUserEmail])

  const fetchNutritionData = async (userEmail: string): Promise<string> => {
    console.log("[MealPlanTab] Fetching nutrition data for:", userEmail)
    if (!userEmail) {
      console.warn("[MealPlanTab] User email is required to fetch nutrition data")
      return "No nutrition data available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const docRef = doc(db, "IF_users", userEmail, "Profile", "workoutplan")
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        console.warn("[MealPlanTab] Workout plan document does not exist")
        return "No nutrition advice available"
      }

      const data = docSnap.data()
      const nutritionAdvice = data?.nutritionAdvice

      if (!nutritionAdvice) {
        console.warn("[MealPlanTab] Nutrition advice field is missing")
        return "No nutrition advice available"
      }

      // Parse the nutrition advice string
      // The format appears to be a JSON-like string with specific keys
      console.log("[MealPlanTab] Raw nutrition advice:", nutritionAdvice)

      // Check if the advice is in the format {"advice":"..."}
      if (nutritionAdvice.includes('"advice"')) {
        try {
          // Try to extract the content from the advice field
          const adviceMatch = nutritionAdvice.match(/"advice"\s*:\s*"([^"]+)"/)
          if (adviceMatch && adviceMatch[1]) {
            const adviceContent = adviceMatch[1]
              .replace(/\\"/g, '"') // Replace escaped quotes
              .replace(/\\n/g, '\n') // Replace escaped newlines

            console.log("[MealPlanTab] Parsed advice content:", adviceContent)
            return adviceContent
          }
        } catch (parseError) {
          console.error("[MealPlanTab] Error parsing advice JSON:", parseError)
        }
      }

      // Fallback to the original parsing method if the above doesn't work
      const sections: string[] = nutritionAdvice.split(";").map((section: string) => section.trim())
      let formattedAdvice = ""

      sections.forEach((section: string) => {
        if (!section) return
        const colonIndex = section.indexOf(": ")
        if (colonIndex !== -1) {
          let key = section.substring(0, colonIndex).trim()
          let value = section.substring(colonIndex + 2).trim()
          if (key.startsWith('"')) key = key.substring(1)
          if (key.endsWith('"')) key = key.substring(0, key.length - 1)
          if (value.startsWith('"')) value = value.substring(1)
          if (value.endsWith('"')) value = value.substring(0, value.length - 1)
          const capitalizedKey = key.charAt(0).toUpperCase() + key.slice(1)
          formattedAdvice += `${capitalizedKey}: ${value}\n\n`
        } else {
          formattedAdvice += `${section}\n\n`
        }
      })

      console.log("[MealPlanTab] Nutrition data formatted, length:", formattedAdvice.length)
      return formattedAdvice
    } catch (error) {
      console.error("[MealPlanTab] Error fetching nutrition data:", error)
      return "Error retrieving nutrition data"
    }
  }

  const renderNutritionalAdvice = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
        </div>
      )
    }

    if (error) {
      return (
        <div className="bg-red-500/20 text-red-300 p-4 rounded-lg">
          <p>{error}</p>
        </div>
      )
    }

    if (!nutritionAdvice || nutritionAdvice === "No nutrition advice available" || nutritionAdvice === "Error retrieving nutrition data") {
      return (
        <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5">
          <p className="text-gray-400">No nutrition advice is available for your current plan. Please contact your coach for more information.</p>
        </div>
      )
    }

    // Parse the advice content into sections if it contains specific markers
    const renderAdviceContent = () => {
      // Check if the content has specific sections like "macroBalance", "portionControl", etc.
      if (nutritionAdvice.includes("macroBalance") || nutritionAdvice.includes("portionControl")) {
        // Split by known section markers and render each section
        const sections = [
          { key: "advice", label: "General Advice" },
          { key: "macroBalance", label: "Macro Balance" },
          { key: "portionControl", label: "Portion Control" }
        ]

        return (
          <div className="space-y-4">
            {sections.map((section, index) => {
              // Look for the section in the content
              const regex = new RegExp(`"${section.key}"\\s*:\\s*"([^"]+)"`)
              const match = nutritionAdvice.match(regex)

              if (match && match[1]) {
                const content = match[1]
                  .replace(/\\"/g, '"')
                  .replace(/\\n/g, '\n')

                return (
                  <div key={index} className="mb-4">
                    <h4 className="text-sm font-medium text-purple-400 mb-2">{section.label}</h4>
                    <p className="text-sm text-gray-300 whitespace-pre-line">{content}</p>
                  </div>
                )
              }
              return null
            })}
          </div>
        )
      }

      // If no specific sections found, just render the whole content
      return <div className="text-sm text-gray-300 whitespace-pre-line">{nutritionAdvice}</div>
    }

    return (
      <div className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
        >
          <h3 className="text-base sm:text-lg font-semibold text-white mb-4 flex items-center">
            <Apple className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-green-500" />
            Nutritional Advice
          </h3>
          {renderAdviceContent()}
        </motion.div>
      </div>
    )
  }

  const renderPreferences = () => {
    // If preferences are still loading, show a loading spinner
    if (isLoadingPreferences) {
      return (
        <div className="space-y-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5"
          >
            <div className="flex flex-col items-center justify-center h-64">
              <Loader2 className="w-8 h-8 animate-spin text-purple-500 mb-4" />
              <p className="text-gray-400">Loading your preferences...</p>
            </div>
          </motion.div>
        </div>
      )
    }

    const dietaryOptions = [
      "Vegetarian", "Vegan", "Pescatarian", "Keto", "Paleo",
      "Low-carb", "High-protein", "Gluten-free", "Dairy-free"
    ]

    const allergyOptions = [
      "Nuts", "Dairy", "Eggs", "Soy", "Wheat", "Shellfish",
      "Fish", "Gluten", "Sesame", "Peanuts"
    ]

    const cuisineOptions = [
      "Italian", "Mediterranean", "Asian", "Mexican", "Indian",
      "American", "Middle Eastern", "Greek", "Japanese", "Thai"
    ]

    const handleDietaryChange = (option: string) => {
      setDietaryPreferences(prev =>
        prev.includes(option)
          ? prev.filter(item => item !== option)
          : [...prev, option]
      )
    }

    const handleAllergyChange = (option: string) => {
      setAllergies(prev =>
        prev.includes(option)
          ? prev.filter(item => item !== option)
          : [...prev, option]
      )
    }

    const handleCuisineChange = (option: string) => {
      setCuisinePreferences(prev =>
        prev.includes(option)
          ? prev.filter(item => item !== option)
          : [...prev, option]
      )
    }

    return (
      <div className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
        >
          <h3 className="text-base sm:text-lg font-semibold text-white mb-4 flex items-center">
            <Settings className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-yellow-500" />
            Meal Preferences
          </h3>

          <div className="space-y-6 text-sm text-gray-300">
            {/* Dietary Preferences */}
            <div>
              <h4 className="text-sm font-medium text-purple-400 mb-3">Dietary Preferences</h4>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {dietaryOptions.map(option => (
                  <label key={option} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={dietaryPreferences.includes(option)}
                      onChange={() => handleDietaryChange(option)}
                      className="rounded border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                    />
                    <span>{option}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Allergies & Intolerances */}
            <div>
              <h4 className="text-sm font-medium text-purple-400 mb-3">Allergies & Intolerances</h4>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {allergyOptions.map(option => (
                  <label key={option} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={allergies.includes(option)}
                      onChange={() => handleAllergyChange(option)}
                      className="rounded border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                    />
                    <span>{option}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Cuisine Preferences */}
            <div>
              <h4 className="text-sm font-medium text-purple-400 mb-3">Cuisine Preferences</h4>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {cuisineOptions.map(option => (
                  <label key={option} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={cuisinePreferences.includes(option)}
                      onChange={() => handleCuisineChange(option)}
                      className="rounded border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                    />
                    <span>{option}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Meal Complexity */}
            <div>
              <h4 className="text-sm font-medium text-purple-400 mb-3">Meal Complexity</h4>
              <div className="flex flex-col space-y-2">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="complexity"
                    value="simple"
                    checked={mealComplexity === "simple"}
                    onChange={() => setMealComplexity("simple")}
                    className="rounded-full border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                  />
                  <span>Simple (5 ingredients or less)</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="complexity"
                    value="medium"
                    checked={mealComplexity === "medium"}
                    onChange={() => setMealComplexity("medium")}
                    className="rounded-full border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                  />
                  <span>Medium (5-10 ingredients)</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="complexity"
                    value="complex"
                    checked={mealComplexity === "complex"}
                    onChange={() => setMealComplexity("complex")}
                    className="rounded-full border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                  />
                  <span>Complex (No ingredient limit)</span>
                </label>
              </div>
            </div>

            {/* Cooking Time */}
            <div>
              <h4 className="text-sm font-medium text-purple-400 mb-3">Cooking Time</h4>
              <div className="flex flex-col space-y-2">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="cookingTime"
                    value="<30"
                    checked={cookingTime === "<30"}
                    onChange={() => setCookingTime("<30")}
                    className="rounded-full border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                  />
                  <span>Quick (Less than 30 minutes)</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="cookingTime"
                    value="30-60"
                    checked={cookingTime === "30-60"}
                    onChange={() => setCookingTime("30-60")}
                    className="rounded-full border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                  />
                  <span>Medium (30-60 minutes)</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="cookingTime"
                    value=">60"
                    checked={cookingTime === ">60"}
                    onChange={() => setCookingTime(">60")}
                    className="rounded-full border-gray-600 text-purple-500 focus:ring-purple-500 bg-black/30"
                  />
                  <span>Slow (More than 60 minutes)</span>
                </label>
              </div>
            </div>

            <div className="pt-2">
              <button
                className={`w-full py-2 px-4 bg-gradient-to-r ${
                  saveStatus === "error"
                    ? "from-red-500 to-red-600"
                    : "from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                } text-white rounded-md transition-all duration-200 font-medium flex items-center justify-center`}
                onClick={savePreferences}
                disabled={isSaving || saveStatus === "saving"}
              >
                {saveStatus === "idle" && (
                  <span>Save Preferences</span>
                )}
                {saveStatus === "saving" && (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    <span>Saving...</span>
                  </>
                )}
                {saveStatus === "saved" && (
                  <>
                    <Check className="w-4 h-4 mr-2" />
                    <span>Saved!</span>
                  </>
                )}
                {saveStatus === "error" && (
                  <span>Error Saving</span>
                )}
              </button>

              <button
                className="w-full mt-2 py-2 px-4 bg-black/30 text-white rounded-md hover:bg-black/40 transition-all duration-200 font-medium"
                onClick={() => setActiveTab("meal-planner")}
                disabled={isSaving}
              >
                View Meal Plan
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    )
  }

  const renderMealPlanner = () => {
    return (
      <div className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
        >
          <h3 className="text-base sm:text-lg font-semibold text-white mb-4 flex items-center">
            <Calendar className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-blue-500" />
            Meal Planner
          </h3>
          <div className="text-sm text-gray-300">
            <p>Based on your nutritional advice and preferences, here are your personalized meal suggestions:</p>

            {dietaryPreferences.length > 0 || allergies.length > 0 || cuisinePreferences.length > 0 ? (
              <div className="mt-4 p-3 bg-black/30 rounded-md">
                <p className="text-xs text-purple-400 mb-2">Your preferences:</p>
                {dietaryPreferences.length > 0 && (
                  <p className="text-xs mb-1">
                    <span className="text-gray-400">Diet:</span> {dietaryPreferences.join(", ")}
                  </p>
                )}
                {allergies.length > 0 && (
                  <p className="text-xs mb-1">
                    <span className="text-gray-400">Allergies:</span> {allergies.join(", ")}
                  </p>
                )}
                {cuisinePreferences.length > 0 && (
                  <p className="text-xs mb-1">
                    <span className="text-gray-400">Cuisines:</span> {cuisinePreferences.join(", ")}
                  </p>
                )}
                <p className="text-xs mb-1">
                  <span className="text-gray-400">Complexity:</span> {mealComplexity}
                </p>
                <p className="text-xs">
                  <span className="text-gray-400">Cooking time:</span> {cookingTime === "<30" ? "Less than 30 min" : cookingTime === "30-60" ? "30-60 min" : "More than 60 min"}
                </p>
              </div>
            ) : (
              <div className="mt-4 p-3 bg-black/30 rounded-md">
                <p className="text-sm text-yellow-400">Please set your meal preferences in the Preferences tab to get personalized meal suggestions.</p>
              </div>
            )}
          </div>
        </motion.div>

        {/* Add the MealPlanGenerator component with workout day context */}
        <MealPlanGenerator
          userEmail={_userEmail || ""}
          currentDay={currentDay}
          availableDays={availableDays}
        />
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <div className="border-b border-white/10 px-4 py-3 bg-black/20">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab("nutritional-advice")}
            className={`text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 flex items-center ${
              activeTab === "nutritional-advice"
                ? "border-purple-500 text-purple-400"
                : "border-transparent text-gray-400 hover:text-white hover:border-white/20"
            }`}
          >
            <Apple className="w-4 h-4 mr-2" />
            Nutritional Advice
          </button>
          <button
            onClick={() => setActiveTab("preferences")}
            className={`text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 flex items-center ${
              activeTab === "preferences"
                ? "border-purple-500 text-purple-400"
                : "border-transparent text-gray-400 hover:text-white hover:border-white/20"
            }`}
          >
            <Settings className="w-4 h-4 mr-2" />
            Preferences
          </button>
          <button
            onClick={() => setActiveTab("meal-planner")}
            className={`text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 flex items-center ${
              activeTab === "meal-planner"
                ? "border-purple-500 text-purple-400"
                : "border-transparent text-gray-400 hover:text-white hover:border-white/20"
            }`}
          >
            <Utensils className="w-4 h-4 mr-2" />
            Meal Planner
          </button>
          <button
            onClick={() => setActiveTab("saved-meals")}
            className={`text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 flex items-center ${
              activeTab === "saved-meals"
                ? "border-purple-500 text-purple-400"
                : "border-transparent text-gray-400 hover:text-white hover:border-white/20"
            }`}
          >
            <BookOpen className="w-4 h-4 mr-2" />
            Saved Meals
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 sm:p-6 scrollbar-slick scrollbar-on-hover">
        {activeTab === "nutritional-advice"
          ? renderNutritionalAdvice()
          : activeTab === "preferences"
            ? renderPreferences()
            : activeTab === "meal-planner"
              ? renderMealPlanner()
              : <SavedMealsTab userEmail={_userEmail || ""} />}
      </div>
    </div>
  )
}
