"use client"

import { useEffe<PERSON>, useRef, useState } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Mi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Off, Refresh<PERSON>w, X, Book } from "lucide-react"
import { motion } from "framer-motion"
import { doc, onSnapshot } from "firebase/firestore"
import { db } from "@/components/firebase/config"
import { ProfileSummaryViewModal } from "./ProfileSummaryViewModal"
import { useAudioController } from "./AudioController"
import { PDFHandler } from "./PDFHandler"
import { useWebRTCManager } from "./WebRTCManager"

interface ProfileSummaryDialogProps {
  isOpen: boolean
  onClose: () => void
  userEmail: string
}

interface AssessmentData {
  status: string
  summary: string
  lastUpdated: string
  error: string | null
  lastError?: string
  audioPath?: string
}

const useProfileSummary = () => {
  const generateSummary = async (): Promise<void> => {
    console.log("Generating summary...")
    // Actual summary generation logic would go here
  }

  return {
    generateSummary,
    isLoading: false,
    error: null,
  }
}

export function ProfileSummaryDialog({ isOpen, onClose, userEmail }: ProfileSummaryDialogProps) {
  const dialogRef = useRef<HTMLDivElement>(null)

  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [assessmentData, setAssessmentData] = useState<AssessmentData | null>(null)
  const [viewModalOpen, setViewModalOpen] = useState<boolean>(false)

  const [realtimeMode, setRealtimeMode] = useState<boolean>(false)

  const { generateSummary, isLoading: summaryLoading, error: summaryError } = useProfileSummary()

  // WebRTC Manager Integration
  const {
    connecting,
    connected,
    isSpeaking,
    isListening,
    micActive,
    error: webRTCError,
    initializeRealtimeConnection,
    toggleMicrophone,
    endRealtimeConnection,
    setAudioElement,
  } = useWebRTCManager()

  // Audio Controller Integration
  const handleAudioError = (errorMessage: string | null) => {
    setError(errorMessage)
  }

  const { audioRef, isAudioPlaying, audioLoading, handlePlayAudio, handlePauseAudio, handleRegenerateSummary } =
    useAudioController({
      userEmail,
      assessmentSummary: assessmentData
        ? {
            content: assessmentData.summary,
            audioPath: assessmentData.audioPath,
          }
        : null,
      onError: handleAudioError,
      generateSummary,
    })

  // PDF Handler Integration
  const { handleGeneratePDF, generatingPDF } = PDFHandler({
    userEmail,
    profileText: assessmentData?.summary || null,
    onError: (errorMessage) => setError(errorMessage),
  })

  // Set the audio element for WebRTC
  useEffect(() => {
    if (audioRef.current) {
      setAudioElement(audioRef.current)
    }
  }, [audioRef.current])

  useEffect(() => {
    if (webRTCError) {
      setError(webRTCError)
    }
  }, [webRTCError])

  useEffect(() => {
    if (!isOpen || !userEmail) return

    setLoading(true)
    setError(null)

    const assessmentRef = doc(db, "IF_users", userEmail, "Profile", "assessment")

    const unsubscribe = onSnapshot(
      assessmentRef,
      (docSnapshot) => {
        if (docSnapshot.exists()) {
          const data = docSnapshot.data() as AssessmentData
          setAssessmentData(data)
          setError(null)
        } else {
          setError("Assessment document not found")
          setAssessmentData(null)
        }
        setLoading(false)
      },
      (err) => {
        console.error("Error fetching assessment:", err)
        setError(`Failed to load assessment data: ${err.message}`)
        setAssessmentData(null)
        setLoading(false)
      },
    )

    return () => unsubscribe()
  }, [isOpen, userEmail])

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") onClose()
    }
    if (isOpen) document.addEventListener("keydown", handleEscape)
    return () => document.removeEventListener("keydown", handleEscape)
  }, [isOpen, onClose])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dialogRef.current && !dialogRef.current.contains(event.target as Node)) {
        onClose()
      }
    }
    if (isOpen) document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [isOpen, onClose])

  const handleStartRealtime = async (): Promise<void> => {
    if (audioRef.current) {
      audioRef.current.pause()
    }

    setRealtimeMode(true)
    if (assessmentData?.summary) {
      initializeRealtimeConnection(assessmentData.summary)
    }
  }

  const handleExitRealtime = (): void => {
    endRealtimeConnection()
    setRealtimeMode(false)
  }

  if (!isOpen) return <></>

  return (
    <div className="fixed inset-0 bg-black/95 flex items-center justify-center p-2 z-40">
      {viewModalOpen && (
        <div className="z-50">
          <ProfileSummaryViewModal
            isOpen={viewModalOpen}
            onClose={() => setViewModalOpen(false)}
            originalSummary={assessmentData?.summary || ""}
            transformedSummary=""
          />
        </div>
      )}

      <motion.div
        ref={dialogRef}
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="w-full max-w-lg text-center"
      >
        <h1 className="text-xl font-bold text-gray-400 mb-10 -mt-20 flex flex-col items-center justify-center">
          <Dumbbell className="text-blue-500 h-12 w-12 mb-2" />
          {realtimeMode ? "IntelligentFitness Coach" : "IntelligentFitness Assessment"}
        </h1>

        <div className="relative w-60 h-60 mx-auto">
          <div
            className={`absolute inset-0 rounded-full bg-black border-2 ml-1 mr-2 mb-2 border-white/10 
              ${realtimeMode ? (isSpeaking ? "animate-pulse" : "") : isAudioPlaying ? "animate-pulse" : ""}`}
          >
            <div className="absolute inset-0 rounded-full bg-white/5 backdrop-blur-sm" />
            <div
              className={`absolute inset-[-2px] rounded-full 
    ${
      realtimeMode
        ? connecting
          ? "bg-gradient-to-r from-yellow-500/20 via-yellow-500/20 to-yellow-500/20"
          : connected
            ? isSpeaking
              ? "animate-pulse bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-green-500/20"
              : micActive && isListening
                ? "animate-pulse bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-green-500/20"
                : "bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-green-500/20"
            : "bg-gradient-to-r from-red-500/20 via-red-500/20 to-red-500/20"
        : (isAudioPlaying ? "animate-pulse" : "") +
          " bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-green-500/20"
    }`}
            />

            <div className="absolute inset-0 flex flex-col items-center justify-center p-8">
              {loading || audioLoading || summaryLoading || connecting ? (
                <div className="text-white/70">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500 border-t-transparent mx-auto mb-2" />
                  <p className="text-sm">{connecting ? "Connecting your Personal Coach..." : "Processing..."}</p>
                </div>
              ) : error || summaryError ? (
                <p className="text-red-400 text-sm">{error || summaryError}</p>
              ) : assessmentData?.status === "error" ? (
                <p className="text-red-400 text-sm">{assessmentData.lastError || "An error occurred"}</p>
              ) : assessmentData?.status === "processing" ? (
                <p className="text-white/70 text-sm">Generating summary...</p>
              ) : realtimeMode ? (
                <p className="text-white/70 text-sm">
                  {isSpeaking
                    ? "Assistant speaking..."
                    : isListening
                      ? micActive
                        ? "Listening to you..."
                        : "Click microphone to speak"
                      : connected
                        ? "Ready - Click microphone to speak"
                        : "Disconnected"}
                </p>
              ) : (
                <p className="text-white/70 text-sm">{isAudioPlaying ? "AI Assessment..." : "Click Play for assessment"}</p>
              )}
            </div>
          </div>

          <div className="absolute -right-11 bottom-3/4 -mb-14 translate-y-1/2">
            <div className="relative group">
              {realtimeMode ? (
                <button
                  onClick={toggleMicrophone}
                  className={`w-12 h-12 rounded-full 
                    ${
                      !connected
                        ? "bg-gray-600/20 opacity-50 cursor-not-allowed"
                        : micActive
                          ? "bg-green-600/40 hover:bg-green-600/60 text-green-400"
                          : "bg-purple-600/20 hover:bg-purple-600/40 text-purple-400"
                    }
                    flex items-center justify-center transition-colors`}
                  disabled={!connected}
                >
                  <div className="w-full h-full flex items-center justify-center">
                    {micActive ? <Mic className="h-6 w-6" /> : <MicOff className="h-6 w-6" />}
                  </div>
                </button>
              ) : (
                <button
                  onClick={isAudioPlaying ? handlePauseAudio : handlePlayAudio}
                  className={`w-12 h-12 rounded-full bg-purple-600/20 hover:bg-purple-600/40 text-purple-400 flex items-center justify-center transition-colors
                    ${!assessmentData?.summary || loading || audioLoading || summaryLoading ? "opacity-50 cursor-not-allowed" : ""}`}
                  disabled={!assessmentData?.summary || loading || audioLoading || summaryLoading}
                >
                  <div className="w-full h-full flex items-center justify-center">
                    {audioLoading || summaryLoading ? (
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-purple-400" />
                    ) : isAudioPlaying ? (
                      <Pause className="h-6 w-6" />
                    ) : (
                      <Play className="h-6 w-6" />
                    )}
                  </div>
                </button>
              )}
              <span className="absolute bottom-8 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                {realtimeMode
                  ? micActive
                    ? "Mute Microphone"
                    : "Unmute Microphone"
                  : isAudioPlaying
                    ? "Pause Audio"
                    : "Play Audio"}
              </span>
            </div>
          </div>

          <div className="absolute -right-28 top-1/5 flex flex-col gap-2 mr-11 ml-2 mt-9">
            <div className="relative group">
              {realtimeMode ? (
                <button
                  onClick={
                    connected
                      ? endRealtimeConnection
                      : () => initializeRealtimeConnection(assessmentData?.summary || "")
                  }
                  className={`w-12 h-12 rounded-full 
                    ${
                      connecting
                        ? "opacity-50 cursor-not-allowed bg-yellow-600/20 text-yellow-400"
                        : connected
                          ? "bg-red-600/20 hover:bg-red-600/40 text-red-400"
                          : "bg-green-600/20 hover:bg-green-600/40 text-green-400"
                    }
                    flex items-center justify-center`}
                  disabled={connecting}
                >
                  {connected ? <PhoneOff className="h-6 w-6" /> : <RefreshCw className="h-6 w-6" />}
                </button>
              ) : (
                <button
                  onClick={handleRegenerateSummary}
                  className={`w-12 h-12 rounded-full bg-green-600/20 text-green-400 flex items-center justify-center
                    ${loading || audioLoading || summaryLoading ? "opacity-50 cursor-not-allowed" : "hover:bg-green-600/40"}`}
                  disabled={loading || audioLoading || summaryLoading}
                >
                  <RefreshCw className="h-6 w-6" />
                </button>
              )}
              <span className="absolute bottom-8 left-2/3 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                {realtimeMode ? (connected ? "End Voice Session" : "Start Voice Session") : "Retry Assessment"}
              </span>
            </div>

            <button
              onClick={realtimeMode ? handleExitRealtime : onClose}
              className="w-12 h-12 rounded-full mt-14 bg-red-600/20 hover:bg-red-600/40 text-red-400 flex items-center justify-center"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {(loading ||
          audioLoading ||
          summaryLoading ||
          connecting ||
          error ||
          summaryError ||
          assessmentData?.status === "error" ||
          assessmentData?.status === "processing") && (
          <div className="text-center text-sm mt-4">
            {error || summaryError ? (
              <span className="text-red-400">{error || summaryError}</span>
            ) : assessmentData?.status === "error" ? (
              <span className="text-red-400">{assessmentData.lastError || "An error occurred"}</span>
            ) : assessmentData?.status === "processing" ? (
              "Generating summary..."
            ) : connecting ? (
              "Establishing voice connection..."
            ) : null}
          </div>
        )}

        <div className="flex justify-center gap-6 mt-6">
          {!realtimeMode ? (
            <>
              <button
                className={`bg-green-600 hover:bg-green-700 text-sm text-white px-3 rounded-3xl min-w-[130px] flex items-center justify-center -mr-3
                ${!assessmentData?.summary ? "opacity-50 cursor-not-allowed" : ""}`}
                onClick={() => setViewModalOpen(true)}
                disabled={!assessmentData?.summary}
              >
                <Book className="h-5 w-5 mr-2" />
                Assessment
              </button>
              <button
                className={`bg-purple-600 hover:bg-purple-700 text-white px-3 text-sm rounded-3xl min-w-[130px] flex items-center justify-center -mr-3
                ${!assessmentData?.summary ? "opacity-50 cursor-not-allowed" : ""}`}
                onClick={handleStartRealtime}
                disabled={!assessmentData?.summary}
              >
                <Mic className="h-5 w-5 mr-2" />
                My Trainer
              </button>
              <button
                className={`bg-blue-600 hover:bg-blue-700 text-white px-3 py-3 text-sm rounded-3xl min-w-[130px] flex items-center justify-center
                ${!assessmentData?.summary || generatingPDF ? "opacity-50 cursor-not-allowed" : ""}`}
                onClick={handleGeneratePDF}
                disabled={!assessmentData?.summary || generatingPDF}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                {generatingPDF ? "Downloading..." : "Get PDF"}
              </button>
            </>
          ) : (
            <>
              <button
                className={`bg-red-600 hover:bg-red-700 text-white text-sm px-6 py-3 rounded-3xl min-w-[160px] flex items-center justify-center`}
                onClick={handleExitRealtime}
              >
                <X className="h-5 w-5 mr-2" />
                Exit Voice Mode
              </button>
              {connected ? (
                <button
                  className={`${micActive ? "bg-green-600 hover:bg-green-700" : "bg-blue-600 hover:bg-blue-700"} text-white px-6 text-sm rounded-3xl min-w-[160px] flex items-center justify-center`}
                  onClick={toggleMicrophone}
                >
                  <Mic className="h-5 w-5 mr-2" />
                  {micActive ? "Microphone Active" : "Start Speaking"}
                </button>
              ) : (
                <button
                  className={`bg-green-600 hover:bg-green-700 text-white px-6 py-3 text-sm rounded-3xl min-w-[160px] flex items-center justify-center
                  ${connecting ? "opacity-50 cursor-not-allowed" : ""}`}
                  onClick={() => initializeRealtimeConnection(assessmentData?.summary || "")}
                  disabled={connecting}
                >
                  {connecting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2" />
                      Connecting...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-5 w-5 mr-2" />
                      Connect
                    </>
                  )}
                </button>
              )}
            </>
          )}
        </div>

        {realtimeMode && connected && (
          <div className="mt-4 flex justify-center items-center">
            <div
              className={`h-3 w-3 rounded-full mr-2 ${isSpeaking ? "bg-green-500 animate-pulse" : "bg-gray-500"}`}
            ></div>
            <span className="text-sm text-gray-400">
              {isSpeaking ? "Assistant speaking..." : isListening ? "Listening..." : "Ready"}
            </span>
          </div>
        )}

        <audio ref={audioRef} onPlay={() => {}} onPause={() => {}} onEnded={() => {}} autoPlay={realtimeMode} />
      </motion.div>
    </div>
  )
}

