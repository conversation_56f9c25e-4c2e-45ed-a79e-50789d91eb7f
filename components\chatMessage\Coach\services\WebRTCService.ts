"use client"

interface OpenAISessionResponse {
  client_secret?: {
    value: string
  }
  error?: {
    message?: string
  }
  message?: string
  [key: string]: unknown
}

export class WebRTCService {
  /**
   * Creates a new RTCPeerConnection
   */
  static createPeerConnection(): RTCPeerConnection {
    console.log("[WebRTCService] Creating new peer connection")
    return new RTCPeerConnection({
      iceServers: [
        {
          urls: ["stun:stun1.l.google.com:19302", "stun:stun2.l.google.com:19302", "stun:stun.l.google.com:19302"],
        },
      ],
      iceCandidatePoolSize: 10,
    })
  }

  /**
   * Creates a data channel on the peer connection
   */
  static createDataChannel(peerConnection: RTCPeerConnection): RTCDataChannel {
    console.log("[WebRTCService] Creating data channel")
    return peerConnection.createDataChannel("openai-data", {
      ordered: true,
      maxRetransmits: 10,
    })
  }

  /**
   * Waits for ICE gathering to complete
   */
  static waitForIceGathering(peerConnection: RTCPeerConnection): Promise<void> {
    console.log("[WebRTCService] Waiting for ICE gathering to complete")
    return new Promise((resolve) => {
      if (peerConnection.iceGatheringState === "complete") {
        console.log("[WebRTCService] ICE gathering already complete")
        resolve()
        return
      }

      const checkState = () => {
        if (peerConnection.iceGatheringState === "complete") {
          console.log("[WebRTCService] ICE gathering complete event received")
          peerConnection.removeEventListener("icegatheringstatechange", checkState)
          resolve()
        }
      }

      peerConnection.addEventListener("icegatheringstatechange", checkState)

      setTimeout(() => {
        console.log("[WebRTCService] ICE gathering timeout reached")
        peerConnection.removeEventListener("icegatheringstatechange", checkState)
        resolve()
      }, 8000)
    })
  }

  /**
   * Exchanges SDP with the OpenAI server
   */
  static async exchangeSDP(
    peerConnection: RTCPeerConnection,
    ephemeralKey: string
  ): Promise<string> {
    console.log("[WebRTCService] Creating offer")
    const offer = await peerConnection.createOffer({
      offerToReceiveAudio: true,
      offerToReceiveVideo: false,
    })

    console.log("[WebRTCService] Setting local description")
    await peerConnection.setLocalDescription(offer)

    console.log("[WebRTCService] Waiting for ICE gathering")
    await WebRTCService.waitForIceGathering(peerConnection)

    if (!peerConnection.localDescription?.sdp) {
      throw new Error("Failed to create session description")
    }

    console.log("[WebRTCService] Sending SDP to OpenAI")
    const sdpResponse = await fetch("https://api.openai.com/v1/realtime", {
      method: "POST",
      body: peerConnection.localDescription.sdp,
      headers: {
        Authorization: `Bearer ${ephemeralKey}`,
        "Content-Type": "application/sdp",
      },
    })

    if (!sdpResponse.ok) {
      const errorText = await sdpResponse.text()
      let errorMessage = `SDP exchange failed with status: ${sdpResponse.status}`
      try {
        const errorJson = JSON.parse(errorText) as OpenAISessionResponse
        errorMessage = `SDP exchange failed: ${errorJson.error?.message || errorJson.error || errorJson.message || sdpResponse.status}`
      } catch {
        if (errorText) errorMessage = `SDP exchange failed: ${errorText}`
      }
      throw new Error(errorMessage)
    }

    console.log("[WebRTCService] Received SDP answer from OpenAI")
    return await sdpResponse.text()
  }

  /**
   * Fetches an ephemeral API key for the WebRTC connection
   */
  static async fetchEphemeralKey(idToken: string): Promise<string> {
    console.log("[WebRTCService] Fetching ephemeral API key")
    
    const headers: Record<string, string> = {
      Pragma: "no-cache",
      "Cache-Control": "no-cache",
    }

    if (idToken) headers.Authorization = `Bearer ${idToken}`

    const response = await fetch("/api/realtime-session", {
      method: "GET",
      headers,
    })

    if (!response.ok) {
      let errorMessage = `Server error: ${response.status}`
      try {
        const errorData = await response.json()
        errorMessage = errorData.error || errorMessage
      } catch {
        const errorText = await response.text()
        errorMessage = errorText || errorMessage
      }
      throw new Error(errorMessage)
    }

    const data = await response.json()
    const ephemeralKey = data.ephemeral_key
    
    if (!ephemeralKey) {
      throw new Error("Invalid session data received: missing ephemeral key")
    }

    console.log("[WebRTCService] Ephemeral key received")
    return ephemeralKey
  }

  /**
   * Cleans up the peer connection
   */
  static cleanupConnection(peerConnection: RTCPeerConnection | null): void {
    if (peerConnection) {
      console.log("[WebRTCService] Cleaning up peer connection")

      peerConnection.oniceconnectionstatechange = null
      peerConnection.onicegatheringstatechange = null
      peerConnection.ontrack = null
      peerConnection.onicecandidateerror = null
      peerConnection.onnegotiationneeded = null
      peerConnection.onicecandidate = null
      peerConnection.onsignalingstatechange = null
      peerConnection.onconnectionstatechange = null

      peerConnection.close()
    }
  }
}
