import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { doc, getDoc, setDoc } from "firebase/firestore"
import { auth, db } from "@/components/firebase/config"
import type { User } from "firebase/auth"
import type { StepData, HeightUnit, WeightUnit, Gender } from "@/types/measurements"
import { convertHeight, convertWeight } from "@/utils/unit-conversions"

const INITIAL_FORM_DATA: StepData = {
  basic_info: {
    height: { value: "", unit: "ft" },
    weight: { value: "", unit: "kg" },
    age: "",
    gender: "prefer-not-to-say",
  },
  fitness_goals: { primary: "", secondary: "" },
  workout_preferences: { frequency: "", duration: "", preferred_time: "" },
  health_information: { medical_conditions: "", injuries: "" },
  preferences: {
    training_location: "gym",
    focus_areas: [],
    sport_specific: "",
  },
}

export const useProfileForm = () => {
  const [user, setUser] = useState<User | null>(null)
  const [formData, setFormData] = useState<StepData>(INITIAL_FORM_DATA)
  const [currentStep, setCurrentStep] = useState(0)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  const steps = ["basic-info", "fitness-goals", "workout-preferences", "preferences", "health-information"]

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (currentUser) => {
      if (currentUser) {
        setUser(currentUser)
        await fetchExistingData(currentUser)
      } else {
        router.push("/auth/signin")
      }
      setLoading(false)
    })

    return () => unsubscribe()
  }, [router])

  const fetchExistingData = async (user: User) => {
    try {
      const fitnessDocRef = doc(db, `IF_users/${user.email}/Profile/fitness`)
      const fitnessDocSnap = await getDoc(fitnessDocRef)

      if (fitnessDocSnap.exists()) {
        const data = fitnessDocSnap.data() as StepData
        setFormData((prevData) => ({
          ...prevData,
          basic_info: {
            ...prevData.basic_info,
            ...data.basic_info,
            gender: data.basic_info.gender || "prefer-not-to-say",
          },
          fitness_goals: data.fitness_goals,
          workout_preferences: data.workout_preferences,
          health_information: data.health_information,
          preferences: {
            training_location: data.preferences?.training_location || "gym",
            focus_areas: data.preferences?.focus_areas || [],
            sport_specific: data.preferences?.sport_specific || "",
          },
        }))
      }
    } catch (error) {
      console.error("Error fetching user data:", error)
    }
  }

  const handleInputChange = (section: keyof StepData, field: string, value: string | string[]) => {
    if (section === "preferences" && field === "focus_areas") {
      setFormData((prev) => ({
        ...prev,
        preferences: {
          ...prev.preferences,
          focus_areas: value as string[],
        },
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: field === "gender" ? (value as Gender) : value,
        },
      }))
    }
  }

  const handleUnitChange = (field: "height" | "weight", newUnit: HeightUnit | WeightUnit) => {
    const currentValue = formData.basic_info[field].value
    if (!currentValue) {
      handleInputChange("basic_info", `${field}.unit`, newUnit)
      return
    }

    const currentUnit = formData.basic_info[field].unit
    const numericValue = Number.parseFloat(currentValue)

    if (isNaN(numericValue)) return

    let convertedValue: number
    if (field === "height") {
      convertedValue = convertHeight(numericValue, currentUnit as HeightUnit, newUnit as HeightUnit)
    } else {
      convertedValue = convertWeight(numericValue, currentUnit as WeightUnit, newUnit as WeightUnit)
    }

    setFormData((prev) => ({
      ...prev,
      basic_info: {
        ...prev.basic_info,
        [field]: {
          value: convertedValue.toString(),
          unit: newUnit,
        },
      },
    }))
  }

  const handleSubmit = async () => {
    if (!user) return

    try {
      const fitnessDocRef = doc(db, `IF_users/${user.email}/Profile/fitness`)
      await setDoc(fitnessDocRef, formData, { merge: true })

      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1)
      } else {
        router.push("/dashboard")
      }
    } catch (error) {
      console.error("Error saving profile data:", error)
    }
  }

  const goToPreviousStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    } else {
      router.push("/dashboard")
    }
  }

  return {
    user,
    formData,
    currentStep,
    steps,
    loading,
    handleInputChange,
    handleUnitChange,
    handleSubmit,
    goToPreviousStep,
  }
}

