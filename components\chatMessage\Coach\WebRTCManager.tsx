"use client"
import { useEffect, useRef, useState } from "react"
import { auth } from "@/components/firebase/config"
import { DataService } from "./services/DataService"
import { DialogueMessage } from "./services/MessageService"

// Enable debug mode for detailed WebRTC communication logging
const DEBUG_MODE = true

// Type alias for AudioContext constructor
type AudioContextConstructor = typeof AudioContext

// Define interfaces for OpenAI response
interface OpenAISessionResponse {
  client_secret?: {
    value: string
  }
  error?: {
    message?: string
  }
  message?: string
  [key: string]: unknown
}

// Define tools for function calling
const trackingTool = {
  type: "function",
  name: "get_tracking_data",
  description: "Retrieves each of the user's exercise tracking data including workout history, progress metrics, and performance indicators from memory.",
  parameters: {
    type: "object",
    properties: {},
    required: []
  }
}

const nutritionTool = {
  type: "function",
  name: "get_nutrition_data",
  description: "Retrieves the user's nutrition advice and dietary recommendations from memory.",
  parameters: {
    type: "object",
    properties: {},
    required: []
  }
}

const trackingSummaryTool = {
  type: "function",
  name: "get_tracking_summary",
  description: "Retrieves a GENERAL SUMMARY of the user's exercise progress metrics, and performance from memory.",
  parameters: {
    type: "object",
    properties: {},
    required: []
  }
}

export function useWebRTCManager() {
  // Connection state
  const [connecting, setConnecting] = useState<boolean>(false)
  const [connected, setConnected] = useState<boolean>(false)
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false)
  const [isListening, setIsListening] = useState<boolean>(false)
  const [micActive, setMicActive] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Dialogue state
  const [currentResponse, setCurrentResponse] = useState<string>("")
  const [dialogueHistory, setDialogueHistory] = useState<DialogueMessage[]>([])

  // Technical references
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null)
  const dataChannelRef = useRef<RTCDataChannel | null>(null)
  const mediaStreamRef = useRef<MediaStream | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const isMounted = useRef<boolean>(true)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const isClosingRef = useRef<boolean>(false)

  // Data references for Tracking, Nutrition, and Tracking Summary
  const trackingDataRef = useRef<string | null>(null)
  const nutritionDataRef = useRef<string | null>(null)
  const trackingSummaryRef = useRef<string | null>(null)

  // Reference to track if we're currently processing a response
  const activeResponseRef = useRef<boolean>(false)

  // Define function implementations for tools using in-memory data
  const functions: Record<string, (args: Record<string, unknown>) => Promise<string>> = {
    get_tracking_data: async (_args) => {
      console.log("[WebRTCManager] Function get_tracking_data called")
      const data = trackingDataRef.current
      if (!data) {
        console.warn("[WebRTCManager] Tracking data not available when function called")
        return "Tracking data not available"
      }
      console.log(`[WebRTCManager] Returning tracking data (${data.length} characters)`)
      return data
    },
    get_nutrition_data: async (_args) => {
      console.log("[WebRTCManager] Function get_nutrition_data called")
      const data = nutritionDataRef.current
      if (!data) {
        console.warn("[WebRTCManager] Nutrition data not available when function called")
        return "Nutrition data not available"
      }
      console.log(`[WebRTCManager] Returning nutrition data (${data.length} characters)`)
      return data
    },
    get_tracking_summary: async (_args) => {
      console.log("[WebRTCManager] ===== FUNCTION get_tracking_summary CALLED =====")
      console.log("[WebRTCManager] Function get_tracking_summary called with args:", JSON.stringify(_args))
      const data = trackingSummaryRef.current
      if (!data) {
        console.warn("[WebRTCManager] Tracking summary not available when function called")
        return "Tracking summary not available"
      }
      console.log(`[WebRTCManager] Returning tracking summary (${data.length} characters)`)
      console.log(`[WebRTCManager] Summary preview: ${data.substring(0, 100)}...`)
      console.log("[WebRTCManager] ===== FUNCTION get_tracking_summary COMPLETED =====")
      return data
    },
  }

  useEffect(() => {
    console.log("[WebRTCManager] State values:", {
      currentResponse: currentResponse?.substring(0, 20) + (currentResponse?.length > 20 ? "..." : ""),
      dialogueHistoryLength: dialogueHistory.length,
      connected,
      isSpeaking,
      isListening,
      micActive,
    })
  }, [currentResponse, dialogueHistory, connected, isSpeaking, isListening, micActive])

  useEffect(() => {
    console.log("[WebRTCManager] Component mounted")
    isMounted.current = true
    isClosingRef.current = false

    return () => {
      console.log("[WebRTCManager] Component unmounting - cleaning up resources")
      isMounted.current = false
      if (!isClosingRef.current) {
        isClosingRef.current = true
        endRealtimeConnection()
      }
    }
  }, [])

  // Use the DataService for all data fetching operations
  const fetchAssessmentSummary = (userEmail: string): Promise<string> => {
    return DataService.fetchAssessmentSummary(userEmail)
  }

  const fetchTrackingData = (userEmail: string): Promise<string> => {
    return DataService.fetchTrackingData(userEmail)
  }

  const fetchNutritionData = (userEmail: string): Promise<string> => {
    return DataService.fetchNutritionData(userEmail)
  }

  const fetchTrackingSummary = (userEmail: string): Promise<string> => {
    console.log("[WebRTCManager] Calling DataService.fetchTrackingSummary for user:", userEmail)
    return DataService.fetchTrackingSummary(userEmail)
      .then(result => {
        console.log("[WebRTCManager] Successfully fetched tracking summary, length:", result.length)
        return result
      })
      .catch(error => {
        console.error("[WebRTCManager] Error fetching tracking summary:", error)
        return "Error retrieving tracking summary"
      })
  }

  const fetchUserWorkoutPlan = (userEmail: string): Promise<string> => {
    return DataService.fetchUserWorkoutPlan(userEmail)
  }

  const fetchChatHistory = (userEmail: string, messageLimit = 15): Promise<string> => {
    return DataService.fetchChatHistory(userEmail, messageLimit)
  }

  /**
   * Initializes the WebRTC connection with the OpenAI server
   */
  const initializeRealtimeConnection = async (): Promise<void> => {
    console.log("[WebRTCManager] Initializing connection...")
    if (isClosingRef.current) {
      console.log("[WebRTCManager] Initialization aborted - already in closing state")
      return
    }

    // Reset state
    setConnecting(true)
    setError(null)
    setConnected(false)
    setCurrentResponse("")
    setDialogueHistory([])

    // Clean up any existing connection
    endRealtimeConnection()

    try {
      // Get user authentication
      const currentUser = auth?.currentUser
      let idToken = ""
      let userEmail = ""

      if (currentUser) {
        try {
          idToken = await currentUser.getIdToken()
          userEmail = currentUser.email || ""
          console.log("[WebRTCManager] User authenticated:", userEmail)
        } catch (authError) {
          console.error("[WebRTCManager] Auth error, continuing without token:", authError)
        }
      } else {
        console.log("[WebRTCManager] No authenticated user found")
      }

      // Prepare headers for API requests
      const headers: Record<string, string> = {
        Pragma: "no-cache",
        "Cache-Control": "no-cache",
      }
      if (idToken) headers.Authorization = `Bearer ${idToken}`

      // Start concurrent data fetching
      console.log("[WebRTCManager] Starting concurrent data fetching")
      const [
        ephemeralKey,
        assessmentSummary,
        workoutPlan,
        chatHistory,
        tracking,
        nutrition,
        trackingSummary
      ] = await Promise.all([
        // Fetch ephemeral key
        (async () => {
          const response = await fetch("/api/realtime-session", { method: "GET", headers })
          if (!response.ok) {
            let errorMessage = `Server error: ${response.status}`
            try {
              const errorData = await response.json()
              errorMessage = errorData.error || errorMessage
            } catch {
              const errorText = await response.text()
              errorMessage = errorText || errorMessage
            }
            throw new Error(errorMessage)
          }
          const data = await response.json()
          const key = data.ephemeral_key
          if (!key) throw new Error("Invalid session data received: missing ephemeral key")
          console.log("[WebRTCManager] Ephemeral key received")
          return key
        })(),
        // Fetch user data
        fetchAssessmentSummary(userEmail),
        fetchUserWorkoutPlan(userEmail),
        fetchChatHistory(userEmail),
        fetchTrackingData(userEmail),
        fetchNutritionData(userEmail),
        fetchTrackingSummary(userEmail),
      ])

      console.log("[WebRTCManager] All data loaded successfully")

      // Store tracking, nutrition, and tracking summary data in memory
      console.log("[WebRTCManager] Storing data in memory references")

      trackingDataRef.current = tracking && typeof tracking === 'string' && tracking.length > 0
        ? tracking
        : "No tracking data available - data fetch error"
      console.log(`[WebRTCManager] Tracking data stored (${trackingDataRef.current.length} characters)`)

      nutritionDataRef.current = nutrition && typeof nutrition === 'string' && nutrition.length > 0
        ? nutrition
        : "No nutrition data available - data fetch error"
      console.log(`[WebRTCManager] Nutrition data stored (${nutritionDataRef.current.length} characters)`)

      trackingSummaryRef.current = trackingSummary && typeof trackingSummary === 'string' && trackingSummary.length > 0
        ? trackingSummary
        : "No tracking summary available - data fetch error"
      console.log(`[WebRTCManager] Tracking summary stored (${trackingSummaryRef.current.length} characters)`)
      console.log(`[WebRTCManager] Tracking summary preview: ${trackingSummaryRef.current.substring(0, 100)}...`)

      // Set up WebRTC with the fetched data
      await setupWebRTC(ephemeralKey, assessmentSummary, workoutPlan, chatHistory)
    } catch (err) {
      console.error("[WebRTCManager] Connection initialization error:", err)
      const errorMessage = err instanceof Error ? err.message : "Failed to initialize your Personal Coach"
      setError(errorMessage)
      setConnecting(false)
      setConnected(false)
    }
  }

  const setupWebRTC = async (
    ephemeralKey: string,
    assessmentSummary: string,
    workoutPlan: string,
    chatHistory: string
  ): Promise<void> => {
    console.log("[WebRTCManager] Setting up WebRTC connection")
    try {
      if (!isMounted.current || isClosingRef.current) {
        console.log("[WebRTCManager] Setup aborted - component unmounting")
        return
      }

      const peerConnection = new RTCPeerConnection({
        iceServers: [
          {
            urls: ["stun:stun1.l.google.com:19302", "stun:stun2.l.google.com:19302", "stun:stun.l.google.com:19302"],
          },
        ],
        iceCandidatePoolSize: 10,
      })
      peerConnectionRef.current = peerConnection

      peerConnection.oniceconnectionstatechange = () => {
        const state = peerConnection.iceConnectionState
        if (!isMounted.current) return

        console.log(`[WebRTCManager] ICE connection state changed: ${state}`)

        if (state === "connected" || state === "completed") {
          if (dataChannelRef.current?.readyState === "open") {
            console.log("[WebRTCManager] ICE connection established successfully")
            setConnected(true)
            setConnecting(false)
            setError(null)
          }
        } else if (state === "disconnected") {
          console.log("[WebRTCManager] ICE connection unstable, attempting recovery")
          setError("Connection unstable - attempting to recover")
        } else if (state === "failed") {
          console.error("[WebRTCManager] ICE connection failed")
          setError("Connection to your Personal Trainer failed. Please try again.")
          setConnected(false)
          setConnecting(false)
        } else if (state === "closed") {
          console.log("[WebRTCManager] ICE connection closed")
          setConnected(false)
          setConnecting(false)
        }
      }

      peerConnection.onconnectionstatechange = () => {
        console.log("[WebRTCManager] Connection state:", peerConnection.connectionState)
      }

      peerConnection.onsignalingstatechange = () => {
        console.log("[WebRTCManager] Signaling state:", peerConnection.signalingState)
      }

      peerConnection.onicegatheringstatechange = () => {
        console.log("[WebRTCManager] ICE gathering state:", peerConnection.iceGatheringState)
      }

      peerConnection.onicecandidate = (event) => {
        if (event.candidate) {
          console.log("[WebRTCManager] New ICE candidate type:", event.candidate.type)
        }
      }

      peerConnection.onicecandidateerror = (event) => {
        // Handle ICE candidate errors with specific error codes
        const errorEvent = event as RTCPeerConnectionIceErrorEvent
        const errorCode = errorEvent?.errorCode

        // Log the error with more context
        console.warn("[WebRTCManager] ICE candidate error:", `Error code: ${errorCode || 'unknown'}`)

        // Handle specific error codes
        if (errorCode === 701) {
          // Error 701 typically indicates network connectivity issues
          console.info("[WebRTCManager] This is a common network connectivity error and may not affect functionality")
        }
      }

      peerConnection.onnegotiationneeded = () => {
        console.log("[WebRTCManager] Negotiation needed")
      }

      console.log("[WebRTCManager] Creating data channel")
      const dataChannel = peerConnection.createDataChannel("openai-data", {
        ordered: true,
        maxRetransmits: 10,
      })
      dataChannelRef.current = dataChannel

      console.log("[WebRTCManager] Setting up audio processing")
      await setupAudioProcessing()

      dataChannel.onopen = () => {
        if (!isMounted.current) return
        console.log("[WebRTCManager] Data channel open - connection established")
        setConnected(true)
        setConnecting(false)
        setIsListening(true)
        setIsSpeaking(false)
        setError(null)

        try {
          // Set up session configuration with enhanced logging
          const config = {
            type: "session.update",
            session: {
              instructions: `You are an AI Personal Fitness Trainer.
You have access to the following information:
- Assessment summary: ${assessmentSummary}
- Workout plan: ${workoutPlan}
- Chat history: ${chatHistory}
You can retrieve the following data when needed:
- Progress: use get_tracking_data function
- Nutrition: use get_nutrition_data function
- Progress Summary: use get_tracking_summary function

**Your Context:** You have been provided with the complete fitness assessment results for a client. This includes comprehensive data on their current fitness profile.
**Your Mandate:** For all subsequent questions from the user regarding this specific assessment:
1. **Adopt the Persona:** Respond *consistently* as an experienced, knowledgeable, objective and supportive fitness trainer. Your tone should be professional yet encouraging.
2. **Context is King:** Base your answers *strictly and exclusively* on the data provided or retrieved via functions. Do not introduce external assumptions or generic information not supported by the data.
3. **Interpret, Don't Just Report:** Go beyond simply stating the numbers. *Interpret* what the results mean in practical terms regarding the client's current fitness level, potential strengths, and areas needing focus.
4. **Clarity is Crucial:** Explain any fitness terminology or assessment metrics in simple, easy-to-understand language.
5. **Tracking: Unless the user explicitly asks otherwise, provide a summary of the users workout tracking. Provide a more detailed response only on request.
5. **Identify Implications:** When relevant to the question, highlight the implications of the findings for the client's health, performance, or potential goal achievement (based *only* on the data).
6. **Maintain Scope:** Confine your analysis and explanations to the realm of fitness and wellness. Avoid making medical diagnoses or giving advice that requires medical expertise.
7. **Delivery:** Whilst maintaining a supportive tone, your delivery must not sound as if you are reading a document. Be conversational and use pauses and inflections to make your response more natural.
8. **Greeting the Client:** Always initiate the discussion by saying.., "Hi!"`,
              turn_detection: { type: "server_vad" },
              voice: "alloy",
              modalities: ["text", "audio"],
              tools: [trackingTool, nutritionTool, trackingSummaryTool],
              tool_choice: "auto",
            },
          }

          console.log("[WebRTCManager] Preparing session configuration with tools:", [
            trackingTool.name,
            nutritionTool.name,
            trackingSummaryTool.name
          ])
          console.log("[WebRTCManager] Sending session configuration")
          sendMessage(JSON.stringify(config))
          console.log("[WebRTCManager] Session configuration sent, waiting for confirmation...")

          // We'll rely on the message handler to log when session.update.response or session.ready is received
          // Adding a small delay to ensure the configuration has time to be processed
          setTimeout(() => {
            console.log("[WebRTCManager] Session configuration process completed (timeout)")
          }, 2000)
        } catch (configError) {
          console.error("[WebRTCManager] Config send failed:", configError)
          if (isMounted.current) {
            setError("Failed to initialize your Personal Coach. Please try reconnecting.")
            endRealtimeConnection()
          }
        }
      }

      dataChannel.onclose = () => {
        if (!isMounted.current) return
        console.log("[WebRTCManager] Data channel closed")
        if (!isClosingRef.current && isMounted.current) {
          setConnected(false)
          setIsListening(false)
          setIsSpeaking(false)
          setError("Speak to your Personal Trainer")
        }
      }

      dataChannel.onerror = (event) => {
        if (!isMounted.current) return
        console.error("[WebRTCManager] Data channel error:", event)
        if (isMounted.current) {
          setError("Communication error with the Personal Coach. Please try reconnecting.")
        }
      }

      // Create a wrapper for sending messages with debug logging
      const sendMessage = (message: string) => {
        if (DEBUG_MODE) {
          console.log("[WebRTCManager] Sending message:", message.substring(0, 500) + (message.length > 500 ? "..." : ""))
        }
        dataChannel.send(message)
      }

      dataChannel.onmessage = async (event) => {
        if (!isMounted.current) return
        try {
          // Log raw message in debug mode
          if (DEBUG_MODE) {
            console.log("[WebRTCManager] Raw message received:", event.data.substring(0, 500) + (event.data.length > 500 ? "..." : ""))
          }

          const msg = JSON.parse(event.data)
          console.log(`[WebRTCManager] Message received:`, msg.type)

          if (msg.type === "response.text.delta" && msg.delta) {
            setCurrentResponse((prev) => prev + msg.delta)
          } else if (msg.type === "response.audio_transcript.delta" && msg.delta) {
            setCurrentResponse((prev) => prev + msg.delta)
          } else if (msg.type === "response.created") {
            console.log("[WebRTCManager] Response created")
            activeResponseRef.current = true
          } else if (msg.type === "response.completed") {
            console.log("[WebRTCManager] Response completed")
            setIsSpeaking(false)
            activeResponseRef.current = false
            if (currentResponse.trim()) {
              setDialogueHistory((prev) => [...prev, { role: "assistant", content: currentResponse.trim() }])
              setCurrentResponse("")
            }
          } else if (msg.type === "response.tool_call" ||
                     msg.type === "response.function_call_arguments.done" ||
                     (msg.type === "response.output_item.done" && msg.item?.type === "function_call")) {
            // Extract function name and arguments based on message type
            let function_name = ""
            let function_args = {}

            if (msg.type === "response.tool_call") {
              // Original format
              function_name = msg.name
              function_args = msg.arguments ? JSON.parse(msg.arguments) : {}
              console.log(`[WebRTCManager] Tool call received (response.tool_call): ${function_name}`)
            } else if (msg.type === "response.function_call_arguments.done") {
              // New format from logs
              function_name = msg.name
              function_args = msg.arguments ? JSON.parse(msg.arguments) : {}
              console.log(`[WebRTCManager] Tool call received (response.function_call_arguments.done): ${function_name}`)
            } else if (msg.type === "response.output_item.done" && msg.item?.type === "function_call") {
              // Alternative format
              function_name = msg.item.name
              function_args = msg.item.arguments ? JSON.parse(msg.item.arguments) : {}
              console.log(`[WebRTCManager] Tool call received (response.output_item.done): ${function_name}`)
            }

            if (!function_name) {
              console.warn("[WebRTCManager] Function call received but function name is missing")
              return
            }

            console.log(`[WebRTCManager] Processing function call: ${function_name} with args:`, function_args)

            if (functions[function_name]) {
              try {
                console.log(`[WebRTCManager] Executing function ${function_name}...`)

                // Check if we have data for this function
                if (function_name === "get_tracking_data") {
                  console.log(`[WebRTCManager] Tracking data available: ${trackingDataRef.current ? 'Yes' : 'No'}, Length: ${trackingDataRef.current?.length || 0}`)
                } else if (function_name === "get_nutrition_data") {
                  console.log(`[WebRTCManager] Nutrition data available: ${nutritionDataRef.current ? 'Yes' : 'No'}, Length: ${nutritionDataRef.current?.length || 0}`)
                } else if (function_name === "get_tracking_summary") {
                  console.log(`[WebRTCManager] Tracking summary available: ${trackingSummaryRef.current ? 'Yes' : 'No'}, Length: ${trackingSummaryRef.current?.length || 0}`)
                }

                const result = await functions[function_name](function_args)
                console.log(`[WebRTCManager] Function ${function_name} executed successfully, sending result (${result.length} characters)`)
                console.log(`[WebRTCManager] Result preview: ${result.substring(0, 100)}...`)

                // Send function output
                // Extract call_id from the message
                let call_id = ""
                if (msg.type === "response.function_call_arguments.done") {
                  call_id = msg.call_id || ""
                } else if (msg.type === "response.output_item.done" && msg.item?.call_id) {
                  call_id = msg.item.call_id
                } else if (msg.call_id) {
                  call_id = msg.call_id
                }

                console.log(`[WebRTCManager] Using call_id: ${call_id}`)

                const functionOutputEvent = {
                  type: "conversation.item.create",
                  item: {
                    type: "function_call_output",
                    // Remove 'role' parameter as it's causing errors
                    output: result,
                    call_id: call_id,
                  },
                }

                // Log the event we're sending
                console.log(`[WebRTCManager] Sending function output event: ${JSON.stringify(functionOutputEvent).substring(0, 100)}...`)

                // Use promise to ensure message is sent
                await new Promise<void>((resolve) => {
                  sendMessage(JSON.stringify(functionOutputEvent))
                  // Small delay to ensure processing
                  setTimeout(resolve, 100)
                })

                // Then send response create message if there's no active response
                if (!activeResponseRef.current) {
                  console.log("[WebRTCManager] Sending response.create after function output")
                  activeResponseRef.current = true
                  sendMessage(JSON.stringify({ type: "response.create" }))
                } else {
                  console.log("[WebRTCManager] Skipping response.create - active response already exists")
                }
              } catch (error) {
                console.error(`[WebRTCManager] Error executing function ${function_name}:`, error)
                // Extract call_id from the message
                let call_id = ""
                if (msg.type === "response.function_call_arguments.done") {
                  call_id = msg.call_id || ""
                } else if (msg.type === "response.output_item.done" && msg.item?.call_id) {
                  call_id = msg.item.call_id
                } else if (msg.call_id) {
                  call_id = msg.call_id
                }

                console.log(`[WebRTCManager] Using call_id for error: ${call_id}`)

                // Send error information to the assistant
                sendMessage(JSON.stringify({
                  type: "conversation.item.create",
                  item: {
                    type: "function_call_output",
                    // Remove 'role' parameter as it's causing errors
                    output: `Error retrieving ${function_name.replace('get_', '')}: ${(error as Error).message || 'Unknown error'}`,
                    call_id: call_id,
                  },
                }))
                // Send response create message if there's no active response
                if (!activeResponseRef.current) {
                  console.log("[WebRTCManager] Sending response.create after function error")
                  activeResponseRef.current = true
                  sendMessage(JSON.stringify({ type: "response.create" }))
                } else {
                  console.log("[WebRTCManager] Skipping response.create - active response already exists")
                }
              }
            } else {
              console.warn(`[WebRTCManager] Unknown function: ${function_name}`)
              // Extract call_id from the message
              let call_id = ""
              if (msg.type === "response.function_call_arguments.done") {
                call_id = msg.call_id || ""
              } else if (msg.type === "response.output_item.done" && msg.item?.call_id) {
                call_id = msg.item.call_id
              } else if (msg.call_id) {
                call_id = msg.call_id
              }

              console.log(`[WebRTCManager] Using call_id for unknown function: ${call_id}`)

              // Inform the assistant about unknown function
              sendMessage(JSON.stringify({
                type: "conversation.item.create",
                item: {
                  type: "function_call_output",
                  // Remove 'role' parameter as it's causing errors
                  output: `Function '${function_name}' is not available.`,
                  call_id: call_id,
                },
              }))
              // Send response create message if there's no active response
              if (!activeResponseRef.current) {
                console.log("[WebRTCManager] Sending response.create after unknown function")
                activeResponseRef.current = true
                sendMessage(JSON.stringify({ type: "response.create" }))
              } else {
                console.log("[WebRTCManager] Skipping response.create - active response already exists")
              }
            }
          } else if (msg.type === "audio_start") {
            console.log("[WebRTCManager] Audio start")
            setIsSpeaking(true)
            setIsListening(false)
          } else if (msg.type === "audio_end") {
            console.log("[WebRTCManager] Audio end")
            setIsSpeaking(false)
            setIsListening(true)
          } else if (msg.type === "session.update.response" || msg.type === "session.ready" || msg.type === "session.created" || msg.type === "session.updated") {
            console.log("[WebRTCManager] Session configuration message received:", msg.type)
            // This will be used when we implement session configuration confirmation
          } else if (msg.type === "error" || msg.type === "session.error") {
            // Safely log the error with better formatting and error handling
            try {
              console.error("[WebRTCManager] Server error:",
                JSON.stringify(msg, null, 2) || "Empty error object")
            } catch {
              console.error("[WebRTCManager] Server error (failed to stringify):", msg)
            }

            // Extract error message with fallbacks
            let errorMessage = "An error occurred with your Personal Trainer"
            let errorCode = ""
            try {
              if (msg && typeof msg === 'object') {
                if (msg.message && typeof msg.message === 'string') {
                  errorMessage = msg.message
                } else if (msg.error && typeof msg.error === 'object') {
                  if (msg.error.message) {
                    errorMessage = msg.error.message
                  }
                  if (msg.error.code) {
                    errorCode = msg.error.code
                  }
                } else if (msg.error && typeof msg.error === 'string') {
                  errorMessage = msg.error
                }
              }
            } catch (e) {
              console.error("[WebRTCManager] Error extracting error message:", e)
            }

            console.error("[WebRTCManager] Error message:", errorMessage, "Code:", errorCode)

            // Handle specific error codes with user-friendly messages
            if (errorCode === "conversation_already_has_active_response") {
              console.log("[WebRTCManager] Detected active response error, resetting state")
              // Reset the active response state since the server thinks we have an active response
              activeResponseRef.current = false
              errorMessage = "Please wait for the current response to complete before sending a new message"
            }

            setError(errorMessage)
          } else if (msg.type === "input_audio_buffer.speech_started") {
            console.log("[WebRTCManager] User speech started")
          } else if (msg.type === "input_audio_buffer.speech_stopped") {
            console.log("[WebRTCManager] User speech stopped")
          } else if (msg.type === "input_audio_buffer.committed") {
            console.log("[WebRTCManager] Audio buffer committed")
          } else if (msg.type === "conversation.item.created") {
            console.log("[WebRTCManager] Conversation item created:", msg.item?.type)
          } else if (msg.type === "response.created") {
            console.log("[WebRTCManager] Response created")
          } else if (msg.type === "response.done") {
            console.log("[WebRTCManager] Response done, status:", msg.response?.status)
            activeResponseRef.current = false
          } else if (msg.type === "rate_limits.updated") {
            // Just log this at debug level to avoid cluttering the console
            if (DEBUG_MODE) {
              console.log("[WebRTCManager] Rate limits updated")
            }
          } else if (msg.type === "response.output_item.added") {
            console.log("[WebRTCManager] Output item added:", msg.item?.type)
            // If this is a function call, we'll handle it in the next message
            if (msg.item?.type === "function_call") {
              console.log(`[WebRTCManager] Function call initiated: ${msg.item.name}`)
            }
          } else if (msg.type === "response.function_call_arguments.delta") {
            // Just log this at debug level to avoid cluttering the console
            if (DEBUG_MODE) {
              console.log("[WebRTCManager] Function call arguments delta received")
            }
          } else if (msg.type === "input_audio_transcription.completed" && msg.transcript) {
            console.log("[WebRTCManager] User said:", msg.transcript)
            setDialogueHistory((prev) => [...prev, { role: "user", content: msg.transcript }])
            setCurrentResponse("")
            setIsSpeaking(false)
          } else {
            console.log(`[WebRTCManager] Unhandled message type: ${msg.type}`)
          }
        } catch (error) {
          console.error("[WebRTCManager] Message parsing error:", error)
        }
      }

      peerConnection.ontrack = (event) => {
        if (!isMounted.current || !event.streams[0]) return
        console.log("[WebRTCManager] Track received from peer")

        if (!audioRef.current) {
          const audioElement = document.createElement("audio")
          audioElement.autoplay = true
          document.body.appendChild(audioElement)
          audioRef.current = audioElement
        }

        if (audioRef.current) {
          console.log("[WebRTCManager] Setting up audio stream")
          audioRef.current.srcObject = event.streams[0]
          audioRef.current.play().catch((playError) => {
            console.error("[WebRTCManager] Audio play error:", playError)
          })
        }
      }

      console.log("[WebRTCManager] Creating offer")
      const offer = await peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: false,
      })

      console.log("[WebRTCManager] Setting local description")
      await peerConnection.setLocalDescription(offer)

      console.log("[WebRTCManager] Waiting for ICE gathering")
      await waitForIceGathering(peerConnection)

      if (!peerConnection.localDescription?.sdp) {
        throw new Error("Failed to create session description")
      }

      console.log("[WebRTCManager] Sending SDP to OpenAI")
      const sdpResponse = await fetch("https://api.openai.com/v1/realtime", {
        method: "POST",
        body: peerConnection.localDescription.sdp,
        headers: {
          Authorization: `Bearer ${ephemeralKey}`,
          "Content-Type": "application/sdp",
        },
      })

      if (!sdpResponse.ok) {
        const errorText = await sdpResponse.text()
        let errorMessage = `SDP exchange failed with status: ${sdpResponse.status}`
        try {
          const errorJson = JSON.parse(errorText) as OpenAISessionResponse
          errorMessage = `SDP exchange failed: ${errorJson.error?.message || errorJson.error || errorJson.message || sdpResponse.status}`
        } catch {
          if (errorText) errorMessage = `SDP exchange failed: ${errorText}`
        }
        throw new Error(errorMessage)
      }

      console.log("[WebRTCManager] Received SDP answer from OpenAI")
      const remoteSdp = await sdpResponse.text()

      console.log("[WebRTCManager] Setting remote description")
      await peerConnection.setRemoteDescription({
        type: "answer",
        sdp: remoteSdp,
      })

      console.log("[WebRTCManager] WebRTC setup complete, waiting for connection")
      setTimeout(() => {
        if (isMounted.current && !connected && dataChannelRef.current?.readyState !== "open") {
          console.error("[WebRTCManager] Connection timed out")
          setError("Connection timed out. Please try again.")
          setConnecting(false)
        }
      }, 15000)
    } catch (err) {
      console.error("[WebRTCManager] WebRTC setup error:", err)
      setError(err instanceof Error ? err.message : "Failed to establish voice connection")
      setConnecting(false)
      endRealtimeConnection()
    }
  }

  const waitForIceGathering = (peerConnection: RTCPeerConnection): Promise<void> => {
    console.log("[WebRTCManager] Waiting for ICE gathering to complete")
    return new Promise((resolve) => {
      if (peerConnection.iceGatheringState === "complete") {
        console.log("[WebRTCManager] ICE gathering already complete")
        resolve()
        return
      }

      const checkState = () => {
        if (peerConnection.iceGatheringState === "complete") {
          console.log("[WebRTCManager] ICE gathering complete event received")
          peerConnection.removeEventListener("icegatheringstatechange", checkState)
          resolve()
        }
      }

      peerConnection.addEventListener("icegatheringstatechange", checkState)

      setTimeout(() => {
        console.log("[WebRTCManager] ICE gathering timeout reached")
        peerConnection.removeEventListener("icegatheringstatechange", checkState)
        resolve()
      }, 8000)
    })
  }

  /**
   * Sets up audio processing for the WebRTC connection
   */
  const setupAudioProcessing = async (): Promise<void> => {
    console.log("[WebRTCManager] Setting up audio processing")
    try {
      // Get the appropriate AudioContext for the browser
      const AudioContextClass: AudioContextConstructor | undefined =
        window.AudioContext ||
        ("webkitAudioContext" in window
          ? (window as unknown as { webkitAudioContext: AudioContextConstructor }).webkitAudioContext
          : undefined)

      if (!AudioContextClass) {
        throw new Error("AudioContext not supported in this browser")
      }

      // Create audio context
      const audioContext = new AudioContextClass()
      audioContextRef.current = audioContext
      console.log("[WebRTCManager] AudioContext created")

      // Request microphone access with optimal settings
      console.log("[WebRTCManager] Requesting microphone access")
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          channelCount: 1,
          sampleRate: 24000,
        },
      })

      console.log("[WebRTCManager] Microphone access granted")
      mediaStreamRef.current = stream

      // Add audio tracks to peer connection
      const tracks = stream.getAudioTracks()
      if (tracks.length === 0) {
        throw new Error("No audio tracks found in microphone stream")
      }

      if (!peerConnectionRef.current) {
        throw new Error("Peer connection not initialized")
      }

      console.log(`[WebRTCManager] Adding ${tracks.length} audio tracks to peer connection`)
      for (const track of tracks) {
        // Start with microphone muted
        track.enabled = false
        try {
          peerConnectionRef.current.addTrack(track, stream)
        } catch (error) {
          console.error("[WebRTCManager] Error adding audio track:", error)
          throw new Error("Failed to configure audio")
        }
      }

      console.log("[WebRTCManager] Audio tracks added successfully")
    } catch (err) {
      // Handle different types of audio errors with user-friendly messages
      console.error("[WebRTCManager] Audio setup error:", err)
      let errorMessage = "Microphone access error"

      if (err instanceof DOMException) {
        if (err.name === "NotAllowedError" || err.name === "PermissionDeniedError") {
          errorMessage = "Microphone access denied. Please allow microphone access in your browser settings."
        } else if (err.name === "NotFoundError" || err.name === "DevicesNotFoundError") {
          errorMessage = "No microphone detected. Please connect a microphone and try again."
        } else if (err.name === "NotReadableError" || err.name === "TrackStartError") {
          errorMessage = "Microphone is in use by another application. Please close other applications and try again."
        } else {
          errorMessage = `Microphone error: ${err.message}`
        }
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      console.error("[WebRTCManager] Audio setup failed:", errorMessage)
      setError(errorMessage)
      throw err
    }
  }

  /**
   * Toggles the microphone on/off
   */
  const toggleMicrophone = (): void => {
    console.log("[WebRTCManager] Toggle microphone requested, current state:", micActive)

    // Check if we can toggle the microphone
    if (!mediaStreamRef.current || !connected) {
      console.warn("[WebRTCManager] Cannot toggle microphone - stream not available or not connected")
      return
    }

    try {
      // Toggle the microphone state
      const newState = !micActive
      console.log(`[WebRTCManager] Setting microphone state to: ${newState ? "active" : "muted"}`)

      // Update all audio tracks
      const tracks = mediaStreamRef.current.getAudioTracks()
      for (const track of tracks) {
        track.enabled = newState
        console.log(`[WebRTCManager] Track ${track.id} enabled: ${track.enabled}`)
      }

      // Update the state
      setMicActive(newState)
    } catch (micError) {
      console.error("[WebRTCManager] Error toggling microphone:", micError)
      setError("Failed to control microphone. Please try reconnecting.")
      setMicActive(false)
    }
  }

  /**
   * Sets the current response text
   * @param text - The text to set as the current response
   */
  const setRealtimeDialogue = (text: string): void => {
    setCurrentResponse(text)
  }

  /**
   * Checks if there's an active response from the AI
   * @returns True if there's an active response, false otherwise
   */
  const hasActiveResponse = (): boolean => {
    return activeResponseRef.current
  }

  /**
   * Ends the WebRTC connection and cleans up resources
   */
  const endRealtimeConnection = (): void => {
    console.log("[WebRTCManager] Ending WebRTC connection")

    // Prevent multiple simultaneous cleanup attempts
    if (isClosingRef.current) {
      console.log("[WebRTCManager] Connection already closing, ignoring duplicate call")
      return
    }

    isClosingRef.current = true

    // Close and clean up the data channel
    if (dataChannelRef.current) {
      try {
        // Try to send a close message if the channel is open
        if (dataChannelRef.current.readyState === "open") {
          console.log("[WebRTCManager] Sending session close message")

          try {
            // Send the close message
            dataChannelRef.current.send(JSON.stringify({ type: "session.close" }))
            if (DEBUG_MODE) {
              console.log("[WebRTCManager] Sent session.close message")
            }

            // Force close after a short timeout if still open
            setTimeout(() => {
              if (dataChannelRef.current && dataChannelRef.current.readyState === "open") {
                console.log("[WebRTCManager] Forcibly closing data channel after timeout")
                dataChannelRef.current.close()
              }
            }, 300)
          } catch (e) {
            console.error("[WebRTCManager] Error sending close message:", e)
            dataChannelRef.current.close()
          }
        } else {
          console.log("[WebRTCManager] Data channel not open, closing directly")
          dataChannelRef.current.close()
        }
      } catch (closeError) {
        console.error("[WebRTCManager] Error during data channel close:", closeError)
      }

      dataChannelRef.current = null
      console.log("[WebRTCManager] Data channel reference cleared")
    }

    // Close and clean up the peer connection
    if (peerConnectionRef.current) {
      console.log("[WebRTCManager] Cleaning up peer connection")

      // Remove all event listeners
      peerConnectionRef.current.oniceconnectionstatechange = null
      peerConnectionRef.current.onicegatheringstatechange = null
      peerConnectionRef.current.ontrack = null
      peerConnectionRef.current.onicecandidateerror = null
      peerConnectionRef.current.onnegotiationneeded = null
      peerConnectionRef.current.onicecandidate = null
      peerConnectionRef.current.onsignalingstatechange = null
      peerConnectionRef.current.onconnectionstatechange = null

      // Close the connection
      peerConnectionRef.current.close()
      peerConnectionRef.current = null

      // Stop all media tracks
      if (mediaStreamRef.current) {
        try {
          console.log("[WebRTCManager] Stopping media tracks")
          const tracks = mediaStreamRef.current.getTracks()
          for (const track of tracks) {
            track.stop()
            console.log(`[WebRTCManager] Track ${track.id} stopped`)
          }
        } catch (trackError) {
          console.error("[WebRTCManager] Error stopping media tracks:", trackError)
        }
        mediaStreamRef.current = null
      }

      // Close the audio context
      if (audioContextRef.current) {
        if (audioContextRef.current.state !== "closed") {
          console.log("[WebRTCManager] Closing audio context")
          audioContextRef.current.close().catch((contextError) => {
            console.error("[WebRTCManager] Error closing audio context:", contextError)
          })
        }
        audioContextRef.current = null
      }

      // Clean up the audio element
      if (audioRef.current) {
        try {
          audioRef.current.pause()
          audioRef.current.srcObject = null
        } catch (audioError) {
          console.error("[WebRTCManager] Error cleaning up audio element:", audioError)
        }
      }

      // Reset UI state
      console.log("[WebRTCManager] Resetting connection state")
      setConnected(false)
      setMicActive(false)
      setIsListening(false)
      setIsSpeaking(false)
    }

    // Allow new connections after a short delay
    setTimeout(() => {
      isClosingRef.current = false
      console.log("[WebRTCManager] Connection cleanup complete, allowing new connections")
    }, 500)
  }

  /**
   * Sets the audio element reference for playback
   * @param element - The HTML audio element to use for playback
   */
  const setAudioElement = (element: HTMLAudioElement | null): void => {
    console.log("[WebRTCManager] Audio element reference set")
    audioRef.current = element
  }

  return {
    connecting,
    connected,
    isSpeaking,
    isListening,
    micActive,
    error,
    initializeRealtimeConnection,
    toggleMicrophone,
    endRealtimeConnection,
    setAudioElement,
    currentResponse,
    dialogueHistory,
    setCurrentResponse,
    setDialogueHistory,
    setRealtimeDialogue,
    hasActiveResponse, // Add the new function to check for active responses
  }
}