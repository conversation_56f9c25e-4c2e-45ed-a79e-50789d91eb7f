"use client"

import { useState, useEffect } from "react"
import { X, ZoomIn, ZoomOut, RotateCw } from "lucide-react"

interface ImagePopupProps {
  imageUrl: string;
  altText: string;
  onClose: () => void;
}

export default function ImagePopup({ imageUrl, altText, onClose }: ImagePopupProps) {
  const [zoom, setZoom] = useState<number>(1);
  const [rotation, setRotation] = useState<number>(0);

  const handleZoomIn = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event from bubbling up
    setZoom(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event from bubbling up
    setZoom(prev => Math.max(prev - 0.25, 0.5));
  };

  const handleRotate = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event from bubbling up
    setRotation(prev => (prev + 90) % 360);
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event from bubbling up
    e.preventDefault(); // Prevent any default behavior
    
    // Add a small delay to ensure event doesn't propagate to parent components
    setTimeout(() => {
      onClose(); // Only close the popup, not the entire meal planner
    }, 10);
  };

  // Handle background click to close popup
  const handleBackgroundClick = (e: React.MouseEvent) => {
    // Only close if clicking directly on the background overlay
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Close on escape key
  useEffect(() => {
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleEscKey);
    return () => window.removeEventListener('keydown', handleEscKey);
  }, [onClose]);

  return (
    <div
      className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/80"
      onClick={handleBackgroundClick}
      // Add these to prevent any interaction with elements behind the popup
      style={{ isolation: 'isolate' }}
    >
      <div
        className="relative max-w-4xl max-h-[90vh] w-full"
        onClick={e => e.stopPropagation()}
      >
        <div className="absolute top-4 right-4 z-10 flex space-x-2">
          <button
            onClick={handleZoomIn}
            className="p-2 bg-black/60 rounded-full hover:bg-black/80 transition-colors"
            type="button"
          >
            <ZoomIn className="w-5 h-5 text-white" />
          </button>
          <button
            onClick={handleZoomOut}
            className="p-2 bg-black/60 rounded-full hover:bg-black/80 transition-colors"
            type="button"
          >
            <ZoomOut className="w-5 h-5 text-white" />
          </button>
          <button
            onClick={handleRotate}
            className="p-2 bg-black/60 rounded-full hover:bg-black/80 transition-colors"
            type="button"
          >
            <RotateCw className="w-5 h-5 text-white" />
          </button>
          <button
            onClick={handleClose}
            className="p-2 bg-black/60 rounded-full hover:bg-red-600/80 transition-colors"
            type="button"
            aria-label="Close image popup"
          >
            <X className="w-5 h-5 text-white" />
          </button>
        </div>
        <div className="overflow-hidden rounded-lg flex items-center justify-center h-[80vh]">
          <img
            src={imageUrl}
            alt={altText}
            className="max-w-full max-h-full object-contain transition-all duration-200"
            style={{
              transform: `scale(${zoom}) rotate(${rotation}deg)`,
              transformOrigin: 'center center'
            }}
            onError={(e) => {
              e.currentTarget.src = "https://via.placeholder.com/800x400?text=Image+Not+Available";
            }}
          />
        </div>
      </div>
    </div>
  );
}
