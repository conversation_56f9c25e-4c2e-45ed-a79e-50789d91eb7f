import { type NextRequest, NextResponse } from "next/server"
import { generateSummaryWithDeepSeek } from "@/components/tools/groq-ai"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { prompt, userEmail } = body

    if (!prompt || !userEmail) {
      return NextResponse.json({ error: "Prompt and user email are required" }, { status: 400 })
    }

    // Call the deepSeek model via groq-ai to generate the summary
    const summary = await generateSummaryWithDeepSeek(prompt)

    return NextResponse.json({ success: true, summary })
  } catch (error) {
    console.error("Error generating summary:", error)
    return NextResponse.json({ error: "Failed to generate summary" }, { status: 500 })
  }
}
