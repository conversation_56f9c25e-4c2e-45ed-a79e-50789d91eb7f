// components/YouTubeEmbed.tsx
"use client"

import { useState, useEffect, useRef } from 'react';
import { Play, RefreshCw, AlertCircle } from 'lucide-react';

interface YouTubeEmbedProps {
  videoId: string;
  title?: string;
  autoplay?: boolean;
  className?: string;
  width?: string | number;
  height?: string | number;
  onLoad?: () => void;
  onError?: (error: Error) => void;
}

/**
 * Extract the video ID from a YouTube URL
 * Supports formats like:
 * - https://www.youtube.com/watch?v=VIDEO_ID
 * - https://youtu.be/VIDEO_ID
 * - https://www.youtube.com/embed/VIDEO_ID
 *
 * @param url YouTube URL
 * @returns Video ID or null if not found
 */
export function extractYouTubeVideoId(url: string): string | null {
  if (!url) return null;

  // Match patterns for different YouTube URL formats
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^?&]+)/i,
    /^([a-zA-Z0-9_-]{11})$/  // Direct video ID (11 characters)
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
}

/**
 * YouTube embed component with error handling and loading state
 */
export default function YouTubeEmbed({
  videoId,
  title = "YouTube video player",
  autoplay = false,
  className = "",
  width = "100%",
  height = "100%",
  onLoad,
  onError
}: YouTubeEmbedProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [extractedVideoId, setExtractedVideoId] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [_showDirectLink, setShowDirectLink] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    // Reset states when videoId changes
    setIsLoading(true);
    setError(null);
    setShowDirectLink(false);

    // Handle both direct video IDs and full URLs
    const extracted = videoId.includes('youtube.com') || videoId.includes('youtu.be')
      ? extractYouTubeVideoId(videoId)
      : videoId;

    setExtractedVideoId(extracted);

    if (!extracted) {
      const error = new Error(`Invalid YouTube video ID or URL: ${videoId}`);
      setError(error);
      if (onError) onError(error);
    }
  }, [videoId, onError]);

  // Reset retry count when video ID changes
  useEffect(() => {
    setRetryCount(0);
  }, [extractedVideoId]);

  const handleIframeLoad = () => {
    setIsLoading(false);
    if (onLoad) onLoad();
  };

  const handleIframeError = () => {
    const error = new Error(`Failed to load YouTube video: ${videoId}`);
    setError(error);
    if (onError) onError(error);
  };

  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    setRetryCount(prev => prev + 1);

    // Force iframe reload by recreating it
    if (iframeRef.current) {
      const src = iframeRef.current.src;
      iframeRef.current.src = '';
      setTimeout(() => {
        if (iframeRef.current) {
          iframeRef.current.src = src;
        }
      }, 100);
    }
  };

  const handleOpenInYouTube = () => {
    if (extractedVideoId) {
      window.open(`https://www.youtube.com/watch?v=${extractedVideoId}`, '_blank');
    }
  };

  if (error) {
    return (
      <div className={`bg-slate-800 flex flex-col items-center justify-center ${className}`} style={{ width, height }}>
        <div className="text-red-500 text-center p-4 mb-2">
          <AlertCircle className="w-8 h-8 mx-auto mb-2" />
          <p className="font-medium">Video unavailable</p>
          <p className="text-xs opacity-75 mt-1 max-w-xs">
            {retryCount > 0
              ? 'This video may be restricted from embedding or is unavailable.'
              : 'There was an error loading this video.'}
          </p>
        </div>

        <div className="flex gap-3">
          {retryCount < 3 && (
            <button
              onClick={handleRetry}
              className="flex items-center gap-1 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-md text-sm"
            >
              <RefreshCw className="w-3.5 h-3.5" />
              Retry
            </button>
          )}

          <button
            onClick={handleOpenInYouTube}
            className="flex items-center gap-1 px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm"
          >
            <Play className="w-3.5 h-3.5" />
            Watch on YouTube
          </button>

          {onError && (
            <button
              onClick={() => {
                if (onError) onError(new Error('User dismissed video'));
              }}
              className="flex items-center gap-1 px-3 py-1.5 bg-slate-600 hover:bg-slate-700 text-white rounded-md text-sm"
            >
              Show Image
            </button>
          )}
        </div>
      </div>
    );
  }

  if (!extractedVideoId) {
    return (
      <div className={`bg-slate-800 flex items-center justify-center ${className}`} style={{ width, height }}>
        <div className="w-16 h-16 rounded-full bg-slate-700" />
      </div>
    );
  }

  // Add origin parameter to help with embedding issues
  const origin = typeof window !== 'undefined' ? window.location.origin : '';
  const embedUrl = `https://www.youtube.com/embed/${extractedVideoId}?rel=0&modestbranding=1&origin=${encodeURIComponent(origin)}${autoplay ? '&autoplay=1' : ''}`;

  return (
    <div className={`relative ${className}`} style={{ width, height }}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-slate-800">
          <div className="w-12 h-12 rounded-full border-4 border-blue-400 border-t-transparent animate-spin" />
        </div>
      )}

      <iframe
        ref={iframeRef}
        className="absolute inset-0 w-full h-full"
        src={embedUrl}
        title={title}
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        onLoad={handleIframeLoad}
        onError={handleIframeError}
      />
    </div>
  );
}