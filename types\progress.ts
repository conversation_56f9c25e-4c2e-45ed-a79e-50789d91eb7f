// types/progress.ts

/**
 * ProgressStep represents the various stages in the
 * workout plan generation and saving process.
 */
export type ProgressStep =
  | "Initiating"
  | "Generating Training Plan"
  | "Generating Short Term Plan"
  | "Generating Long Term Plan"
  | "Engaging form technique coach"
  | "Generating Nutritional suggestions"
  | "Saving Data"
  | "Completed"
  | "Error";

/**
 * ProgressSteps is an ordered array of all possible progress steps.
 * This can be useful for rendering progress indicators like steppers or progress bars.
 */
export const ProgressSteps: ProgressStep[] = [
  "Initiating",
  "Generating Training Plan",
  "Generating Short Term Plan",
  "Generating Long Term Plan",
  "Engaging form technique coach",
  "Generating Nutritional suggestions",
  "Saving Data",
  "Completed",
  "Error",
];

/**
 * ProgressStatus provides a mapping from each ProgressStep to its numerical representation.
 * This can be useful for determining the current step index in progress meters.
 */
export const ProgressStatus: Record<ProgressStep, number> = {
  "Initiating": 1,
  "Generating Training Plan": 2,
  "Generating Short Term Plan": 3,
  "Generating Long Term Plan": 4,
  "Engaging form technique coach": 5,
  "Generating Nutritional suggestions": 6,
  "Saving Data": 7,
  "Completed": 8,
  "Error": 9,
};

/**
 * getProgressIndex returns the numerical index of the current progress step.
 * @param step - The current ProgressStep
 * @returns The numerical index representing the step's position
 */
export const getProgressIndex = (step: ProgressStep): number => {
  return ProgressStatus[step];
};

/**
 * isErrorStep determines if the current step represents an error state.
 * @param step - The current ProgressStep
 * @returns True if the step is "Error", otherwise false
 */
export const isErrorStep = (step: ProgressStep): boolean => {
  return step === "Error";
};

/**
 * isCompletedStep determines if the current step represents a completed state.
 * @param step - The current ProgressStep
 * @returns True if the step is "Completed", otherwise false
 */
export const isCompletedStep = (step: ProgressStep): boolean => {
  return step === "Completed";
};


