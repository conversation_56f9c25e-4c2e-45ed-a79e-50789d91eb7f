import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { TextLoader } from "langchain/document_loaders/fs/text";
import { DocxLoader } from "@langchain/community/document_loaders/fs/docx";
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";

// Define strict types for document chunks and metadata
interface DocumentChunk {
  pageContent: string;
  metadata: DocumentMetadata;
}

interface DocumentMetadata {
  type: string;
  doc_id?: string;
  chunk_id?: string;
  file_name?: string;
  file_type?: string;
  position?: number;
  total_chunks?: number;
  is_summary?: boolean;
  processed_at?: string;
  [key: string]: unknown;
}

interface CSVMetadata extends DocumentMetadata {
  totalRows: number;
  headers: string[];
  rowRange?: {
    start: number;
    end: number;
  };
  hasComments?: boolean;
}

// Interface for LangChain document type
interface LangChainDocument {
  pageContent: string;
  metadata: Record<string, unknown>;
}

// Type-safe array cleaning function
function cleanArray<T>(arr: T[]): T[] {
  return arr.map(item => {
    if (Array.isArray(item)) {
      return cleanArray(item) as T;
    }
    return item ?? '' as T;
  });
}

// Type-safe object cleaning function
function cleanObject<T extends Record<string, unknown>>(obj: T): T {
  if (Array.isArray(obj)) {
    return cleanArray(obj) as unknown as T;
  }
  
  if (obj && typeof obj === 'object') {
    const cleaned: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value === undefined || value === null) {
        cleaned[key] = '';
      } else if (Array.isArray(value)) {
        cleaned[key] = cleanArray(value);
      } else if (typeof value === 'object') {
        cleaned[key] = cleanObject(value as Record<string, unknown>);
      } else {
        cleaned[key] = value;
      }
    }
    return cleaned as T;
  }
  
  return obj;
}

// Process CSV files using improved type safety
async function processCSV(file: File): Promise<DocumentChunk[]> {
  try {
    const text = await file.text();
    const lines = text.split(/\r?\n/).filter(line => line.trim());
    
    if (lines.length === 0) {
      throw new Error('CSV file is empty');
    }

    let headerIndex = 0;
    while (headerIndex < lines.length && 
           (lines[headerIndex].startsWith('#') || 
            lines[headerIndex].startsWith('//') || 
            lines[headerIndex].startsWith(';'))) {
      headerIndex++;
    }

    if (headerIndex >= lines.length) {
      throw new Error('No valid data found in CSV file');
    }

    const headers = parseCSVLine(lines[headerIndex])
      .map(header => String(header || '').trim())
      .map((header, index) => header || `Column${index + 1}`);

    const rows = lines.slice(headerIndex + 1)
      .filter(line => !line.startsWith('#'))
      .map(line => parseCSVLine(line));

    const chunks: DocumentChunk[] = [];
    const ROWS_PER_CHUNK = 50;
    const comments = lines.slice(0, headerIndex).join('\n');

    // Add summary chunk with proper typing
    const summaryMetadata: CSVMetadata = {
      type: 'csv_summary',
      totalRows: rows.length,
      headers: headers,
      hasComments: comments.length > 0,
      is_summary: true
    };

    const summaryContent = `CSV Summary:\n` +
      (comments ? `File Comments:\n${comments}\n\n` : '') +
      `Total Rows: ${rows.length}\n` +
      `Columns: ${headers.join(', ')}\n\n` +
      `Sample Data (First 5 Rows):\n` +
      rows.slice(0, 5).map((row, idx) => 
        `Row ${idx + 1}:\n` + 
        headers.map((header, i) => `  ${header}: ${String(row[i] || '')}`).join('\n')
      ).join('\n\n');

    chunks.push({
      pageContent: summaryContent,
      metadata: summaryMetadata
    });

    // Process remaining rows in chunks with proper typing
    for (let i = 0; i < rows.length; i += ROWS_PER_CHUNK) {
      const chunkRows = rows.slice(i, Math.min(i + ROWS_PER_CHUNK, rows.length));
      let chunkContent = '';

      chunkRows.forEach((row, rowIdx) => {
        chunkContent += `Row ${i + rowIdx + 1}:\n`;
        headers.forEach((header, colIdx) => {
          chunkContent += `${header}: ${String(row[colIdx] || '')}\n`;
        });
        chunkContent += '\n';
      });

      const chunkMetadata: CSVMetadata = {
        type: 'csv_chunk',
        rowRange: {
          start: i + 1,
          end: Math.min(i + ROWS_PER_CHUNK, rows.length)
        },
        totalRows: rows.length,
        headers: headers
      };

      chunks.push({
        pageContent: chunkContent.trim(),
        metadata: chunkMetadata
      });
    }

    return chunks;
  } catch (error) {
    console.error('CSV processing error:', error);
    throw error;
  }
}

function parseCSVLine(line: string): string[] {
  const fields: string[] = [];
  let field = '';
  let inQuotes = false;
  let i = 0;
  
  while (i < line.length && line[i].trim() === '') i++;
  
  while (i < line.length) {
    const char = line[i];
    const nextChar = line[i + 1];
    
    if (char === '"') {
      if (!inQuotes && field.trim() === '') {
        inQuotes = true;
      } else if (inQuotes && nextChar === '"') {
        field += '"';
        i++;
      } else if (inQuotes) {
        inQuotes = false;
      } else {
        field += char;
      }
    } else if (char === ',' && !inQuotes) {
      fields.push(field.trim());
      field = '';
    } else {
      field += char;
    }
    
    i++;
  }
  
  fields.push(field.trim());
  return fields;
}

async function processText(file: File): Promise<DocumentChunk[]> {
  try {
    const blob = new Blob([await file.arrayBuffer()], { type: 'text/plain' });
    const loader = new TextLoader(blob);
    const docs = await loader.load();
    
    if (docs.length === 0) {
      throw new Error('No content found in text file');
    }

    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 200,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });

    const chunks = await splitter.splitDocuments(docs);
    
    return chunks.map(chunk => ({
      pageContent: chunk.pageContent,
      metadata: cleanObject({
        ...chunk.metadata,
        type: 'text_chunk'
      })
    }));
  } catch (error) {
    console.error('Text processing error:', error);
    throw error;
  }
}

// Main document processing function with improved type safety
export async function processDocument(
  file: File,
  docId: string,
  fileType: string,
  fileName: string,
  _userId: string, // Prefixed with underscore to indicate intentionally unused
  category: string,
  additionalParameter: string,
  CHUNK_SIZE: number,
  CHUNK_OVERLAP: number
): Promise<DocumentChunk[]> {
  try {
    let chunks: DocumentChunk[] = [];
    
    switch (fileType.toLowerCase()) {
      case "text/csv":
        chunks = await processCSV(file);
        break;

      case "text/plain":
      case "application/rtf":
        chunks = await processText(file);
        break;

      case "application/msword":
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
          const docxBlob = new Blob([await file.arrayBuffer()], { type: file.type });
          const docxLoader = new DocxLoader(docxBlob);
          const docContent = await docxLoader.load();
          const docxChunks = await new RecursiveCharacterTextSplitter({
            chunkSize: CHUNK_SIZE,
            chunkOverlap: CHUNK_OVERLAP,
            separators: ["\n\n", "\n", ". ", " ", ""]
          }).splitDocuments(docContent);
          chunks = docxChunks.map((chunk: LangChainDocument) => ({
            pageContent: chunk.pageContent,
            metadata: {
              ...chunk.metadata,
              type: 'docx_chunk'
            }
          }));
          break;
  
        case "application/pdf":
          const pdfBlob = new Blob([await file.arrayBuffer()], { type: file.type });
          const pdfLoader = new PDFLoader(pdfBlob);
          const pdfContent = await pdfLoader.load();
          const pdfChunks = await new RecursiveCharacterTextSplitter({
            chunkSize: CHUNK_SIZE,
            chunkOverlap: CHUNK_OVERLAP,
            separators: ["\n\n", "\n", ". ", " ", ""]
          }).splitDocuments(pdfContent);
          chunks = pdfChunks.map(chunk => ({
            pageContent: chunk.pageContent,
            metadata: {
              ...chunk.metadata,
              type: 'pdf_chunk'
            }
          }));
          break;

      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }

    if (!chunks.length) {
      throw new Error('No content was extracted from the document');
    }

    // Add common metadata to all chunks with proper typing
    return chunks.map((chunk, index) => ({
      pageContent: chunk.pageContent,
      metadata: cleanObject({
        ...chunk.metadata,
        doc_id: docId,
        chunk_id: `${docId}_${index + 1}`,
        file_name: fileName,
        file_type: fileType,
        category: category,
        additional_param: additionalParameter,
        position: index + 1,
        total_chunks: chunks.length,
        is_summary: chunk.metadata.is_summary || index === 0,
        processed_at: new Date().toISOString()
      })
    }));

  } catch (error) {
    console.error('Document processing error:', error);
    throw error;
  }
}