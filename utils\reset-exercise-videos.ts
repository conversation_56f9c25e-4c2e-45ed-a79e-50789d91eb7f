// utils/reset-exercise-videos.ts
import { db } from "@/components/firebase/config";
import { collection, getDocs, doc, writeBatch, query, where, DocumentData } from "firebase/firestore";

// Define the progress interface
interface ResetProgress {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
}

// Define the Exercise interface
interface Exercise extends DocumentData {
  id: string;
  name: string;
  urlVideo?: string | null;
  embedVideo?: string | null;
  videoId?: string | null;
  videoTitle?: string | null;
  videoChannel?: string | null;
  videoThumbnail?: string | null;
  updatedAt?: Date;
  [key: string]: any; // Allow for other properties
}

/**
 * Reset video-related fields in the Fitness Library collection
 * This will remove all video URLs, IDs, and related metadata from exercises
 */
export async function resetExerciseVideos(
  progressCallback?: (progress: ResetProgress) => void,
  batchSize: number = 20
): Promise<{
  success: boolean;
  message: string;
  stats: ResetProgress;
}> {
  // Initialize progress tracking
  const progress: ResetProgress = {
    total: 0,
    processed: 0,
    succeeded: 0,
    failed: 0
  };

  try {
    console.log("Starting exercise video reset process...");

    // Create a query to find all exercises with videos
    // We'll look for exercises that have either urlVideo or videoId fields
    const exerciseQuery = query(
      collection(db, "Fitness Library"),
      where("urlVideo", "!=", null)
    );

    const querySnapshot = await getDocs(exerciseQuery);
    const exercises: Exercise[] = [];

    querySnapshot.forEach((doc) => {
      exercises.push({
        id: doc.id,
        ...doc.data(),
        name: ""
      });
    });

    // Update progress
    progress.total = exercises.length;
    if (progressCallback) progressCallback({ ...progress });

    console.log(`Found ${exercises.length} exercises with videos to reset`);

    // Process exercises in batches
    for (let i = 0; i < exercises.length; i += batchSize) {
      const batch = exercises.slice(i, i + batchSize);
      const batchWriteRef = writeBatch(db);

      console.log(`Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(exercises.length / batchSize)}`);

      // Process each exercise in the batch
      for (const exercise of batch) {
        try {
          const exerciseRef = doc(db, "Fitness Library", exercise.id);

          // Remove all video-related fields
          batchWriteRef.update(exerciseRef, {
            urlVideo: null,
            embedVideo: null,
            videoId: null,
            videoTitle: null,
            videoChannel: null,
            videoThumbnail: null,
            updatedAt: new Date()
          });

          // Update progress
          progress.processed++;
          progress.succeeded++;

        } catch (error) {
          console.error(`Error resetting video for exercise ${exercise.id}:`, error);
          progress.processed++;
          progress.failed++;
        }

        // Update progress after each exercise
        if (progressCallback) progressCallback({ ...progress });
      }

      // Commit the batch write
      try {
        await batchWriteRef.commit();
        console.log(`Committed batch updates to Firestore`);
      } catch (error) {
        console.error(`Error committing batch:`, error);
        // Mark all exercises in this batch as failed
        progress.succeeded -= batch.length;
        progress.failed += batch.length;
        if (progressCallback) progressCallback({ ...progress });
      }

      // Add a delay between batches
      if (i + batchSize < exercises.length) {
        console.log(`Waiting for 1 second before processing next batch...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return {
      success: true,
      message: `Successfully reset ${progress.succeeded} of ${progress.total} exercise videos. Failed: ${progress.failed}`,
      stats: progress
    };

  } catch (error) {
    console.error("Error in resetExerciseVideos:", error);
    return {
      success: false,
      message: `Error resetting exercise videos: ${error instanceof Error ? error.message : 'Unknown error'}`,
      stats: progress
    };
  }
}

// Alternative function that uses a second query to find exercises with videoId
// This is needed because Firestore doesn't support OR queries directly
export async function resetAllExerciseVideos(
  progressCallback?: (progress: ResetProgress) => void,
  batchSize: number = 20
): Promise<{
  success: boolean;
  message: string;
  stats: ResetProgress;
}> {
  // Initialize progress tracking
  const progress: ResetProgress = {
    total: 0,
    processed: 0,
    succeeded: 0,
    failed: 0
  };

  try {
    console.log("Starting complete exercise video reset process...");

    // We need to run two separate queries since Firestore doesn't support OR queries
    const urlVideoQuery = query(
      collection(db, "Fitness Library"),
      where("urlVideo", "!=", null)
    );

    const videoIdQuery = query(
      collection(db, "Fitness Library"),
      where("videoId", "!=", null)
    );

    // Get exercises with urlVideo
    const urlVideoSnapshot = await getDocs(urlVideoQuery);
    const exercisesWithUrl = new Map<string, Exercise>();

    urlVideoSnapshot.forEach((doc) => {
      exercisesWithUrl.set(doc.id, {
        id: doc.id,
        ...doc.data()
      } as Exercise);
    });

    // Get exercises with videoId
    const videoIdSnapshot = await getDocs(videoIdQuery);
    const allExercises = new Map<string, Exercise>(exercisesWithUrl);

    videoIdSnapshot.forEach((doc) => {
      if (!allExercises.has(doc.id)) {
        allExercises.set(doc.id, {
          id: doc.id,
          ...doc.data()
        } as Exercise);
      }
    });

    // Convert to array
    const exercises: Exercise[] = Array.from(allExercises.values());

    // Update progress
    progress.total = exercises.length;
    if (progressCallback) progressCallback({ ...progress });

    console.log(`Found ${exercises.length} exercises with videos to reset`);

    // Process exercises in batches
    for (let i = 0; i < exercises.length; i += batchSize) {
      const batch = exercises.slice(i, i + batchSize);
      const batchWriteRef = writeBatch(db);

      console.log(`Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(exercises.length / batchSize)}`);

      // Process each exercise in the batch
      for (const exercise of batch) {
        try {
          const exerciseRef = doc(db, "Fitness Library", exercise.id);

          // Remove all video-related fields
          batchWriteRef.update(exerciseRef, {
            urlVideo: null,
            embedVideo: null,
            videoId: null,
            videoTitle: null,
            videoChannel: null,
            videoThumbnail: null,
            updatedAt: new Date()
          });

          // Update progress
          progress.processed++;
          progress.succeeded++;

        } catch (error) {
          console.error(`Error resetting video for exercise ${exercise.id}:`, error);
          progress.processed++;
          progress.failed++;
        }

        // Update progress after each exercise
        if (progressCallback) progressCallback({ ...progress });
      }

      // Commit the batch write
      try {
        await batchWriteRef.commit();
        console.log(`Committed batch updates to Firestore`);
      } catch (error) {
        console.error(`Error committing batch:`, error);
        // Mark all exercises in this batch as failed
        progress.succeeded -= batch.length;
        progress.failed += batch.length;
        if (progressCallback) progressCallback({ ...progress });
      }

      // Add a delay between batches
      if (i + batchSize < exercises.length) {
        console.log(`Waiting for 1 second before processing next batch...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return {
      success: true,
      message: `Successfully reset ${progress.succeeded} of ${progress.total} exercise videos. Failed: ${progress.failed}`,
      stats: progress
    };

  } catch (error) {
    console.error("Error in resetAllExerciseVideos:", error);
    return {
      success: false,
      message: `Error resetting exercise videos: ${error instanceof Error ? error.message : 'Unknown error'}`,
      stats: progress
    };
  }
}
