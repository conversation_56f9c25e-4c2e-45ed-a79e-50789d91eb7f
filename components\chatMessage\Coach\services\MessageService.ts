"use client"
// Using RefObject instead of the deprecated MutableRefObject
import { RefObject } from 'react'

// Define message types
export interface DialogueMessage {
  role: string
  content: string
}

// Define interfaces for WebRTC message types
export interface WebRTCMessage {
  type: string
  delta?: string
  transcript?: string
  response?: {
    status?: string
    [key: string]: unknown
  }
  item?: {
    type?: string
    name?: string
    arguments?: string
    call_id?: string
    [key: string]: unknown
  }
  [key: string]: unknown
}

export interface FunctionCallMessage extends WebRTCMessage {
  type: string
  name?: string
  arguments?: string
  call_id?: string
  item?: {
    type?: string
    name?: string
    arguments?: string
    call_id?: string
  }
}

export interface ErrorMessage extends WebRTCMessage {
  message?: string
  error?: string | {
    message?: string
    code?: string | number
  }
}

export class MessageService {
  /**
   * Processes an incoming message from the data channel
   */
  static processMessage(
    message: string,
    setCurrentResponse: (text: string | ((prev: string) => string)) => void,
    setDialogueHistory: (updater: (prev: DialogueMessage[]) => DialogueMessage[]) => void,
    setIsSpeaking: (speaking: boolean) => void,
    setIsListening: (listening: boolean) => void,
    activeResponseRef: RefObject<boolean>,
    functions: Record<string, (args: Record<string, unknown>) => Promise<string>>,
    sendMessage: (message: string) => void,
    DEBUG_MODE = false
  ): void {
    try {
      // Log raw message in debug mode
      if (DEBUG_MODE) {
        console.log("[MessageService] Raw message received:", message.substring(0, 500) + (message.length > 500 ? "..." : ""))
      }

      const msg = JSON.parse(message) as WebRTCMessage
      console.log(`[MessageService] Message received:`, msg.type)

      if (msg.type === "response.text.delta" && msg.delta) {
        setCurrentResponse((prev) => prev + msg.delta)
      } else if (msg.type === "response.audio_transcript.delta" && msg.delta) {
        setCurrentResponse((prev) => prev + msg.delta)
      } else if (msg.type === "response.created") {
        console.log("[MessageService] Response created")
        activeResponseRef.current = true
      } else if (msg.type === "response.completed") {
        console.log("[MessageService] Response completed")
        setIsSpeaking(false)
        activeResponseRef.current = false
        setCurrentResponse((currentResponse) => {
          if (currentResponse.trim()) {
            setDialogueHistory((prev) => [...prev, { role: "assistant", content: currentResponse.trim() }])
            return ""
          }
          return currentResponse
        })
      } else if (msg.type === "response.tool_call" ||
                 msg.type === "response.function_call_arguments.done" ||
                 (msg.type === "response.output_item.done" && msg.item?.type === "function_call")) {
        MessageService.handleFunctionCall(msg, functions, activeResponseRef, sendMessage)
      } else if (msg.type === "audio_start") {
        console.log("[MessageService] Audio start")
        setIsSpeaking(true)
        setIsListening(false)
      } else if (msg.type === "audio_end") {
        console.log("[MessageService] Audio end")
        setIsSpeaking(false)
        setIsListening(true)
      } else if (msg.type === "session.update.response" || msg.type === "session.ready" || msg.type === "session.created" || msg.type === "session.updated") {
        console.log("[MessageService] Session configuration message received:", msg.type)
      } else if (msg.type === "error" || msg.type === "session.error") {
        MessageService.handleErrorMessage(msg)
      } else if (msg.type === "input_audio_buffer.speech_started") {
        console.log("[MessageService] User speech started")
      } else if (msg.type === "input_audio_buffer.speech_stopped") {
        console.log("[MessageService] User speech stopped")
      } else if (msg.type === "input_audio_buffer.committed") {
        console.log("[MessageService] Audio buffer committed")
      } else if (msg.type === "conversation.item.created") {
        console.log("[MessageService] Conversation item created:", msg.item?.type)
      } else if (msg.type === "response.created") {
        console.log("[MessageService] Response created")
      } else if (msg.type === "response.done") {
        console.log("[MessageService] Response done, status:", msg.response?.status)
        activeResponseRef.current = false
      } else if (msg.type === "rate_limits.updated") {
        // Just log this at debug level to avoid cluttering the console
        if (DEBUG_MODE) {
          console.log("[MessageService] Rate limits updated")
        }
      } else if (msg.type === "response.output_item.added") {
        console.log("[MessageService] Output item added:", msg.item?.type)
        // If this is a function call, we'll handle it in the next message
        if (msg.item?.type === "function_call") {
          console.log(`[MessageService] Function call initiated: ${msg.item.name}`)
        }
      } else if (msg.type === "response.function_call_arguments.delta") {
        // Just log this at debug level to avoid cluttering the console
        if (DEBUG_MODE) {
          console.log("[MessageService] Function call arguments delta received")
        }
      } else if (msg.type === "input_audio_transcription.completed" && msg.transcript) {
        console.log("[MessageService] User said:", msg.transcript)
        // Ensure transcript is a string before adding to dialogue history
        const transcript = typeof msg.transcript === 'string' ? msg.transcript : String(msg.transcript)
        setDialogueHistory((prev) => [...prev, { role: "user", content: transcript }])
        setCurrentResponse("")
        setIsSpeaking(false)
      } else {
        console.log(`[MessageService] Unhandled message type: ${msg.type}`)
      }
    } catch (error) {
      console.error("[MessageService] Message parsing error:", error)
    }
  }

  /**
   * Handles a function call from the AI
   */
  private static async handleFunctionCall(
    msg: FunctionCallMessage,
    functions: Record<string, (args: Record<string, unknown>) => Promise<string>>,
    activeResponseRef: RefObject<boolean>,
    sendMessage: (message: string) => void
  ): Promise<void> {
    // Extract function name and arguments based on message type
    let function_name = ""
    let function_args = {}

    if (msg.type === "response.tool_call") {
      // Original format
      function_name = msg.name || ""
      function_args = msg.arguments ? JSON.parse(msg.arguments) : {}
      console.log(`[MessageService] Tool call received (response.tool_call): ${function_name}`)
    } else if (msg.type === "response.function_call_arguments.done") {
      // New format from logs
      function_name = msg.name || ""
      function_args = msg.arguments ? JSON.parse(msg.arguments) : {}
      console.log(`[MessageService] Tool call received (response.function_call_arguments.done): ${function_name}`)
    } else if (msg.type === "response.output_item.done" && msg.item?.type === "function_call") {
      // Alternative format
      function_name = msg.item.name || ""
      function_args = msg.item.arguments ? JSON.parse(msg.item.arguments) : {}
      console.log(`[MessageService] Tool call received (response.output_item.done): ${function_name}`)
    }

    if (!function_name) {
      console.warn("[MessageService] Function call received but function name is missing")
      return
    }

    console.log(`[MessageService] Processing function call: ${function_name} with args:`, function_args)

    // Extract call_id from the message
    let call_id = ""
    if (msg.type === "response.function_call_arguments.done") {
      call_id = msg.call_id || ""
    } else if (msg.type === "response.output_item.done" && msg.item?.call_id) {
      call_id = msg.item.call_id
    } else if (msg.call_id) {
      call_id = msg.call_id
    }

    if (functions[function_name]) {
      try {
        console.log(`[MessageService] Executing function ${function_name}...`)
        const result = await functions[function_name](function_args)
        console.log(`[MessageService] Function ${function_name} executed successfully, sending result (${result.length} characters)`)
        console.log(`[MessageService] Result preview: ${result.substring(0, 100)}...`)

        // Send function output
        const functionOutputEvent = {
          type: "conversation.item.create",
          item: {
            type: "function_call_output",
            output: result,
            call_id: call_id,
          },
        }

        // Log the event we're sending
        console.log(`[MessageService] Sending function output event: ${JSON.stringify(functionOutputEvent).substring(0, 100)}...`)

        // Send the message
        sendMessage(JSON.stringify(functionOutputEvent))

        // Then send response create message if there's no active response
        if (!activeResponseRef.current) {
          console.log("[MessageService] Sending response.create after function output")
          activeResponseRef.current = true
          sendMessage(JSON.stringify({ type: "response.create" }))
        } else {
          console.log("[MessageService] Skipping response.create - active response already exists")
        }
      } catch (error) {
        console.error(`[MessageService] Error executing function ${function_name}:`, error)

        // Send error information to the assistant
        sendMessage(JSON.stringify({
          type: "conversation.item.create",
          item: {
            type: "function_call_output",
            output: `Error retrieving ${function_name.replace('get_', '')}: ${(error as Error).message || 'Unknown error'}`,
            call_id: call_id,
          },
        }))

        // Send response create message if there's no active response
        if (!activeResponseRef.current) {
          console.log("[MessageService] Sending response.create after function error")
          activeResponseRef.current = true
          sendMessage(JSON.stringify({ type: "response.create" }))
        } else {
          console.log("[MessageService] Skipping response.create - active response already exists")
        }
      }
    } else {
      console.warn(`[MessageService] Unknown function: ${function_name}`)

      // Inform the assistant about unknown function
      sendMessage(JSON.stringify({
        type: "conversation.item.create",
        item: {
          type: "function_call_output",
          output: `Function '${function_name}' is not available.`,
          call_id: call_id,
        },
      }))

      // Send response create message if there's no active response
      if (!activeResponseRef.current) {
        console.log("[MessageService] Sending response.create after unknown function")
        activeResponseRef.current = true
        sendMessage(JSON.stringify({ type: "response.create" }))
      } else {
        console.log("[MessageService] Skipping response.create - active response already exists")
      }
    }
  }

  /**
   * Handles error messages from the server
   * Logs the error details but doesn't return anything
   */
  private static handleErrorMessage(msg: ErrorMessage): void {
    // Safely log the error with better formatting and error handling
    try {
      console.error("[MessageService] Server error:",
        JSON.stringify(msg, null, 2) || "Empty error object")
    } catch {
      console.error("[MessageService] Server error (failed to stringify):", msg)
    }

    // Extract error message with fallbacks
    let errorMessage = "An error occurred with your Personal Trainer"
    let errorCode = ""
    try {
      if (msg && typeof msg === 'object') {
        if (msg.message && typeof msg.message === 'string') {
          errorMessage = msg.message
        } else if (msg.error && typeof msg.error === 'object') {
          if (msg.error.message) {
            errorMessage = String(msg.error.message)
          }
          if (msg.error.code) {
            errorCode = String(msg.error.code)
          }
        } else if (msg.error && typeof msg.error === 'string') {
          errorMessage = msg.error
        }
      }
    } catch (e) {
      console.error("[MessageService] Error extracting error message:", e)
    }

    console.error("[MessageService] Error message:", errorMessage, "Code:", errorCode)
    // This function doesn't need to return anything
    // The error message is logged but not used by the caller
  }

  /**
   * Creates a session configuration message
   */
  static createSessionConfig(
    assessmentSummary: string,
    workoutPlan: string,
    chatHistory: string
  ): string {
    const config = {
      type: "session.update",
      session: {
        instructions: `You are an AI Personal Fitness Trainer.
You have access to the following information:
- Assessment summary: ${assessmentSummary}
- Workout plan: ${workoutPlan}
- Chat history: ${chatHistory}
You can retrieve the following data when needed:
- Progress: use get_tracking_data function
- Nutrition: use get_nutrition_data function

**Your Context:** You have been provided with the complete fitness assessment results for a client. This includes comprehensive data on their current fitness profile.
**Your Mandate:** For all subsequent questions from the user regarding this specific assessment:
1. **Adopt the Persona:** Respond *consistently* as an experienced, knowledgeable, objective and supportive fitness trainer. Your tone should be professional yet encouraging.
2. **Context is King:** Base your answers *strictly and exclusively* on the data provided or retrieved via functions. Do not introduce external assumptions or generic information not supported by the data.
3. **Interpret, Don't Just Report:** Go beyond simply stating the numbers. *Interpret* what the results mean in practical terms regarding the client's current fitness level, potential strengths, and areas needing focus.
4. **Clarity is Crucial:** Explain any fitness terminology or assessment metrics in simple, easy-to-understand language.
5. **Tracking: Unless the user explicitly asks otherwise, provide a summary of the users workout tracking. Provide a more detailed response only on request.
5. **Identify Implications:** When relevant to the question, highlight the implications of the findings for the client's health, performance, or potential goal achievement (based *only* on the data).
6. **Maintain Scope:** Confine your analysis and explanations to the realm of fitness and wellness. Avoid making medical diagnoses or giving advice that requires medical expertise.
8. **Greeting the Client:** Always initiate the discussion by saying.., "Hi!"`,
        turn_detection: { type: "server_vad" },
        voice: "alloy",
        modalities: ["text", "audio"],
        tools: [
          {
            type: "function",
            name: "get_tracking_data",
            description: "Retrieves the user's exercise tracking data including workout history, progress metrics, and performance indicators from memory.",
            parameters: {
              type: "object",
              properties: {},
              required: []
            }
          },
          {
            type: "function",
            name: "get_nutrition_data",
            description: "Retrieves the user's nutrition advice and dietary recommendations from memory.",
            parameters: {
              type: "object",
              properties: {},
              required: []
            }
          }
        ],
        tool_choice: "auto",
      },
    }

    return JSON.stringify(config)
  }
}