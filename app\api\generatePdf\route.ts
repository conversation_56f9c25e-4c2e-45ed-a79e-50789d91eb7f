// File: /app/api/generatePdf/route.ts

import { db } from "@/components/firebase/config"
import { storage } from "@/components/firebase/config"
import { doc, getDoc, setDoc } from "firebase/firestore"
import { ref, uploadBytes } from "firebase/storage"
import { jsPDF } from "jspdf"
import { v4 as uuidv4 } from "uuid"
import { <PERSON><PERSON><PERSON> } from "node:buffer"
import { NextRequest, NextResponse } from "next/server"

interface RequestBody {
  userEmail: string
}

export async function POST(request: NextRequest) {
  try {
    // 1) Parse request
    const { userEmail } = (await request.json()) as RequestBody
    if (!userEmail) {
      return NextResponse.json(
        { error: "Missing userEmail" },
        { status: 400 },
      )
    }

    // 2) Get Firestore doc (which should contain the summary)
    const assessmentDocRef = doc(db, `IF_users/${userEmail}/Profile/assessment`)
    const docSnap = await getDoc(assessmentDocRef)
    if (!docSnap.exists()) {
      return NextResponse.json(
        { error: "No assessment found for given user" },
        { status: 404 },
      )
    }

    const data = docSnap.data()
    const { summary } = data
    if (!summary) {
      return NextResponse.json(
        { error: "No summary found, cannot generate PDF" },
        { status: 400 },
      )
    }

    // 3) Generate PDF with jsPDF
    const pdfDoc = new jsPDF()
    const timestamp = new Date().toISOString().split("T")[0]
    const newDocumentId = uuidv4()
    const fileName = `ProfileSummary_${newDocumentId}.pdf`

    // (3a) Add some text to the PDF
    pdfDoc.setFontSize(16)
    pdfDoc.setFont("helvetica", "bold")
    pdfDoc.text("IntelligentFitness.ai Profile Summary", 20, 20)

    pdfDoc.setFontSize(12)
    pdfDoc.setFont("helvetica", "normal")
    pdfDoc.text(`Profile Analysis for: ${userEmail}`, 20, 40)
    pdfDoc.text(`Generation Date: ${timestamp}`, 20, 50)

    // Add summary text
    const textLines = pdfDoc.splitTextToSize(summary, 170)
    pdfDoc.text(textLines, 20, 70)

    // (3b) Convert to ArrayBuffer -> Node Buffer
    const pdfArrayBuffer = pdfDoc.output("arraybuffer")
    const pdfBuffer = Buffer.from(pdfArrayBuffer)

    // 4) Upload PDF to Firebase Storage
    const storageRef = ref(storage, `users/${userEmail}/fitness/${fileName}`)
    await uploadBytes(storageRef, pdfBuffer, { contentType: "application/pdf" })

    // 5) Mark HasPdf = true in Firestore
    await setDoc(
      assessmentDocRef,
      {
        HasPdf: true,
        pdfPath: `users/${userEmail}/fitness/${fileName}`,
        lastPdfGenerated: timestamp,
      },
      { merge: true },
    )

    // 6) Return success
    return NextResponse.json({
      message: "PDF generated and uploaded",
      pdfPath: `users/${userEmail}/fitness/${fileName}`,
    })
  } catch (error) {
    console.error("Error generating PDF:", error)
    return NextResponse.json(
      { error: "Failed to generate PDF", details: String(error) },
      { status: 500 },
    )
  }
}
