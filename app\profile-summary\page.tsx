"use client";

import { useEffect, useState } from "react";
import { ProfileSummaryDialog } from "@/components/ProfileSummaryDialog"; // Adjust the import path as needed
import { auth } from "@/components/firebase/config";
import { onAuthStateChanged } from "firebase/auth";

export default function ProfileSummaryPage() {
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Directly assign the unsubscribe function
    const unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      setUserEmail(user?.email ?? null);
      setLoading(false);
    });

    // Cleanup subscription
    return () => unsubscribeAuth();
  }, []);

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100">
        Loading...
      </div>
    );
  }

  if (!userEmail) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-100">
        Please log in to view your profile summary.
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <ProfileSummaryDialog
        isOpen={true} // Always open since this is a dedicated page
        onClose={() => window.history.back()} // Navigate back when closed
        userEmail={userEmail}
        
      />
    </div>
  );
}