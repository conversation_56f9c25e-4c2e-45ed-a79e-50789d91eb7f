// ChatTab.tsx - Integrated with enhanced message processing, chat history context, and pagination
import { useState, useEffect, useRef, useCallback } from "react";
import { MessageSquare, Menu, Send, ArrowUp } from "lucide-react";
import { getAuth } from "firebase/auth";
import {
  getFirestore,
  collection,
  addDoc,
  query,
  orderBy,
  limit,
  getDocs,
  startAfter,
  Timestamp,
  QueryDocumentSnapshot,
  DocumentData
} from "firebase/firestore";
import { addMessage, fetchMessages } from "@/components/chatMessage/message";
import { processMessage } from "@/components/chatMessage/processMessages"; // Import our enhanced component
import { ChatMessages } from "./ChatMessages";
import { ChatHistory } from "./ChatHistory";
import { ChatMessage, ChatHistoryItem } from "@/types/chat";
import { useWebRTCManager } from "./Coach/WebRTCManager"; // Import WebRTCManager to check active response state

// Constants for pagination
const MESSAGES_PER_PAGE = 8; // Reduced to 8 messages per page to minimize loading

function ChatTab({ chatId: propChatId }: { chatId?: string }) {
  const auth = getAuth();
  const user = auth.currentUser;
  const userId = user?.email || "";

  // Initialize WebRTCManager to check for active responses
  const webRTCManager = useWebRTCManager();

  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isHistoryVisible, setIsHistoryVisible] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [chatId, setChatId] = useState<string | null>(propChatId || null);
  const [isLoadingChat, setIsLoadingChat] = useState<boolean>(true);
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);

  // Pagination state
  const [hasMoreMessages, setHasMoreMessages] = useState<boolean>(false);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [oldestMessageSnapshot, setOldestMessageSnapshot] = useState<QueryDocumentSnapshot<DocumentData> | null>(null);
  const [initialMessagesLoaded, setInitialMessagesLoaded] = useState<boolean>(false);

  const isSendingMessage = useRef(false);
  const isMounted = useRef(true);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const isStreamingMessage = useRef(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Check if a chat has messages
  const checkChatHasMessages = async (chatId: string): Promise<boolean> => {
    if (!userId || !chatId) return false;

    try {
      const db = getFirestore();
      const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
      const messagesQuery = query(messagesRef, limit(1));
      const messagesSnapshot = await getDocs(messagesQuery);

      return !messagesSnapshot.empty;
    } catch (error) {
      console.error(`Error checking messages for chat ${chatId}:`, error);
      return false;
    }
  };

  // Find the latest chat with messages
  const findLatestChatWithMessages = async (chats: ChatHistoryItem[]): Promise<string | null> => {
    // First try to find a chat that we've already confirmed has messages
    const chatWithMessages = chats.find(chat => chat.hasMessages);
    if (chatWithMessages) return chatWithMessages.id;

    // Otherwise check each chat until we find one with messages
    for (const chat of chats) {
      const hasMessages = await checkChatHasMessages(chat.id);
      if (hasMessages) {
        // Update the chat in our state to mark it as having messages
        setChatHistory(prevChats =>
          prevChats.map(c =>
            c.id === chat.id ? { ...c, hasMessages: true } : c
          )
        );
        return chat.id;
      }
    }

    // If no chats have messages, return the most recent chat
    return chats.length > 0 ? chats[0].id : null;
  };

  // Fetch existing chats or create a new one if none exists
  useEffect(() => {
    const fetchOrCreateChat = async () => {
      if (!userId) {
        setError("User ID is required to fetch chats. Please log in.");
        setIsLoadingChat(false);
        return;
      }

      try {
        setIsLoadingChat(true);
        const db = getFirestore();
        const chatsRef = collection(db, `users/${userId}/chats`);
        const chatsQuery = query(chatsRef, orderBy("createdAt", "desc"), limit(10));
        const chatsSnapshot = await getDocs(chatsQuery);

        // Transform chat data to include hasMessages flag (initially false)
        const fetchedChatHistory: ChatHistoryItem[] = chatsSnapshot.docs.map(doc => ({
          id: doc.id,
          title: doc.data().title || "Untitled Chat",
          createdAt: doc.data().createdAt || new Date().toISOString(),
          hasMessages: false // We'll set this correctly later
        }));

        setChatHistory(fetchedChatHistory);

        // If a specific chat ID was provided, use that
        if (propChatId && propChatId.trim() !== "") {
          setChatId(propChatId);
        }
        // If no chat ID was provided but chats exist, find the latest with messages
        else if (!chatsSnapshot.empty) {
          const latestChatWithMessagesId = await findLatestChatWithMessages(fetchedChatHistory);
          if (latestChatWithMessagesId) {
            setChatId(latestChatWithMessagesId);
            console.log("Using latest chat with messages, ID:", latestChatWithMessagesId);
          } else {
            // Fallback to most recent chat if none have messages
            const latestChatId = fetchedChatHistory[0].id;
            setChatId(latestChatId);
            console.log("No chats with messages found. Using most recent chat ID:", latestChatId);
          }
        }
        // If no chats exist, create a new one
        else {
          const newChatRef = await addDoc(chatsRef, {
            createdAt: new Date().toISOString(),
            title: "New Chat",
            userId,
          });
          setChatId(newChatRef.id);
          console.log("Created new chat with ID:", newChatRef.id);
        }
      } catch (err) {
        console.error("Error fetching or creating chat:", err);
        setError("Failed to load or create chat. Please try again.");
      } finally {
        setIsLoadingChat(false);
      }
    };

    fetchOrCreateChat();
  }, [propChatId, userId]);

  // Validate chat ID and user ID
  const isChatValid = useCallback(() => {
    if (!userId || userId.trim() === "") {
      setError("User ID is required. Please ensure you're logged in.");
      return false;
    }

    if (!chatId || chatId.trim() === "") {
      setError("Chat ID is required and cannot be empty.");
      return false;
    }

    setError(null);
    return true;
  }, [userId, chatId]);

  // Enhanced deduplication to prevent duplicates regardless of timestamp differences
  const deduplicateMessages = useCallback((messages: ChatMessage[]): ChatMessage[] => {
    const uniqueMessages = new Map<string, ChatMessage>();

    // First pass: collect messages by content+role key
    messages.forEach((message) => {
      // Use only role and content for deduplication, ignoring timestamp
      const key = `${message.role}:${message.content}`;

      // If we haven't seen this message before, add it
      if (!uniqueMessages.has(key)) {
        uniqueMessages.set(key, message);
      }
      // If we have seen it, keep the one with a valid timestamp (not "Unknown time")
      else {
        const existingMessage = uniqueMessages.get(key)!;
        const existingHasUnknownTime = existingMessage.timestamp.includes("Unknown");
        const newHasUnknownTime = message.timestamp.includes("Unknown");

        // Prioritize messages with valid timestamps
        if (existingHasUnknownTime && !newHasUnknownTime) {
          uniqueMessages.set(key, message);
        }
      }
    });

    return Array.from(uniqueMessages.values());
  }, []);

  // Update hasMessages status when messages are loaded
  const updateChatHasMessages = useCallback((messagesExist: boolean) => {
    if (!chatId) return;

    setChatHistory(prevHistory =>
      prevHistory.map(chat =>
        chat.id === chatId ? { ...chat, hasMessages: messagesExist } : chat
      )
    );
  }, [chatId]);

  // Set up real-time message listener for most recent messages
  useEffect(() => {
    isMounted.current = true;
    setInitialMessagesLoaded(false);

    if (!chatId || !isChatValid()) {
      return;
    }

    console.log(`Setting up message listener for chat ${chatId}`);

    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    try {
      // Initially fetch only the most recent MESSAGES_PER_PAGE messages
      const db = getFirestore();
      const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
      const recentMessagesQuery = query(
        messagesRef,
        orderBy("createdAt", "desc"),
        limit(MESSAGES_PER_PAGE)
      );

      // First, get the most recent messages to initialize the view
      getDocs(recentMessagesQuery).then((snapshot) => {
        if (!isMounted.current) return;

        // Check if there are potentially more messages to load
        setHasMoreMessages(snapshot.size >= MESSAGES_PER_PAGE);

        // If there are messages, store the oldest one for pagination
        if (snapshot.docs.length > 0) {
          setOldestMessageSnapshot(snapshot.docs[snapshot.docs.length - 1]);
        }

        // Convert messages to our format
        const formattedMessages = snapshot.docs.map((doc) => {
          const data = doc.data();
          let timestampStr = "Unknown time";

          if (data.createdAt) {
            // Firebase Timestamp object (explicitly check with instanceof)
            if (data.createdAt instanceof Timestamp) {
              timestampStr = data.createdAt.toDate().toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              });
            }
            // For backwards compatibility, check for toDate function
            else if (typeof data.createdAt.toDate === "function") {
              timestampStr = data.createdAt.toDate().toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              });
            }
            // JavaScript Date object
            else if (data.createdAt instanceof Date) {
              timestampStr = data.createdAt.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              });
            }
            // ISO string or other string timestamp
            else if (typeof data.createdAt === "string") {
              const parsedDate = new Date(data.createdAt);
              if (!isNaN(parsedDate.getTime())) {
                timestampStr = parsedDate.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                });
              }
            }
          }

          return {
            id: doc.id,
            role: data.role,
            content: data.text || "",
            timestamp: timestampStr,
          };
        });

        // Sort messages chronologically (oldest to newest)
        const sortedMessages = formattedMessages.reverse();

        // Apply deduplication before setting initial messages
        const uniqueMessages = deduplicateMessages(sortedMessages);

        // Update UI with the deduplicated batch of messages
        setChatMessages(uniqueMessages);
        updateChatHasMessages(uniqueMessages.length > 0);
        setInitialMessagesLoaded(true);
      }).catch((error) => {
        console.error("Error fetching initial messages:", error);
        setError("Failed to load initial messages");
      });

      // Set up real-time listener for new messages
      const unsubscribe = fetchMessages(userId, chatId, (messages) => {
        if (!isMounted.current || !initialMessagesLoaded) return;

        // Only process this if we're not currently streaming a message
        if (!isStreamingMessage.current) {
          const formattedMessages = messages.map((msg) => {
            let timestampStr = "Unknown time";

            if (msg.createdAt) {
              // Firebase Timestamp object (explicitly check with instanceof)
              if (msg.createdAt instanceof Timestamp) {
                timestampStr = msg.createdAt.toDate().toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                });
              }
              // For backwards compatibility, check for toDate function
              else if (typeof msg.createdAt.toDate === "function") {
                timestampStr = msg.createdAt.toDate().toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                });
              }
              // JavaScript Date object
              else if (msg.createdAt instanceof Date) {
                timestampStr = msg.createdAt.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                });
              }
              // ISO string or other string timestamp
              else if (typeof msg.createdAt === "string") {
                const parsedDate = new Date(msg.createdAt);
                if (!isNaN(parsedDate.getTime())) {
                  timestampStr = parsedDate.toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  });
                }
              }
            }

            return {
              id: msg.id,
              role: msg.role,
              content: msg.text || "",
              timestamp: timestampStr,
            };
          });

          setChatMessages((prevMessages) => {
            // More robust duplicate prevention by content and role, not just ID
            const existingContentMap = new Map(
              prevMessages.map(msg => [`${msg.role}:${msg.content}`, msg])
            );

            // Filter out messages that already exist by content+role, not just ID
            const newMessages = formattedMessages.filter(msg => {
              const contentKey = `${msg.role}:${msg.content}`;
              const existing = existingContentMap.get(contentKey);

              // Keep message if:
              // 1. No existing message with same content+role, OR
              // 2. Existing message has Unknown time but this one has valid time
              if (!existing) return true;

              const existingHasUnknownTime = existing.timestamp.includes("Unknown");
              const newHasUnknownTime = msg.timestamp.includes("Unknown");

              return existingHasUnknownTime && !newHasUnknownTime;
            });

            if (newMessages.length === 0) {
              return prevMessages;
            }

            // Apply our enhanced deduplication
            return deduplicateMessages([...prevMessages, ...newMessages]);
          });
        }
      });

      unsubscribeRef.current = unsubscribe;
    } catch (err) {
      console.error("Error setting up message listener:", err);
      setError(err instanceof Error ? err.message : "Failed to load chat messages");
    }

    return () => {
      console.log("Cleaning up message listener");
      isMounted.current = false;
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [chatId, userId, deduplicateMessages, isChatValid, updateChatHasMessages]);

  // Function to load older messages
  const loadMoreMessages = async () => {
    if (!chatId || !userId || !oldestMessageSnapshot || isLoadingMore) {
      return;
    }

    try {
      setIsLoadingMore(true);
      const db = getFirestore();
      const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);

      // Query for older messages, starting after the oldest message we currently have
      const olderMessagesQuery = query(
        messagesRef,
        orderBy("createdAt", "desc"),
        startAfter(oldestMessageSnapshot),
        limit(MESSAGES_PER_PAGE)
      );

      const snapshot = await getDocs(olderMessagesQuery);

      // Check if there are potentially more messages to load after this batch
      setHasMoreMessages(snapshot.size >= MESSAGES_PER_PAGE);

      // If we got more messages, update our oldest message reference
      if (snapshot.docs.length > 0) {
        setOldestMessageSnapshot(snapshot.docs[snapshot.docs.length - 1]);
      }

      // Get current scroll position before adding new messages
      const container = messagesContainerRef.current;
      const oldScrollHeight = container?.scrollHeight || 0;

      // Convert messages to our format and add them to the state
      if (snapshot.docs.length > 0) {
        const olderMessages = snapshot.docs.map((doc) => {
          const data = doc.data();
          let timestampStr = "Unknown time";

          if (data.createdAt) {
            // Firebase Timestamp object (explicitly check with instanceof)
            if (data.createdAt instanceof Timestamp) {
              timestampStr = data.createdAt.toDate().toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              });
            }
            // For backwards compatibility, check for toDate function
            else if (typeof data.createdAt.toDate === "function") {
              timestampStr = data.createdAt.toDate().toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              });
            }
            // JavaScript Date object
            else if (data.createdAt instanceof Date) {
              timestampStr = data.createdAt.toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              });
            }
            // ISO string or other string timestamp
            else if (typeof data.createdAt === "string") {
              const parsedDate = new Date(data.createdAt);
              if (!isNaN(parsedDate.getTime())) {
                timestampStr = parsedDate.toLocaleTimeString([], {
                  hour: "2-digit",
                  minute: "2-digit",
                });
              }
            }
          }

          return {
            id: doc.id,
            role: data.role,
            content: data.text || "",
            timestamp: timestampStr,
          };
        });

        // Add older messages to the beginning of the list (they're in reverse chronological order)
        setChatMessages((prev) => {
          // More robust duplicate prevention by content and role, not just ID
          const existingContentMap = new Map(
            prev.map(msg => [`${msg.role}:${msg.content}`, msg])
          );

          // Filter out messages that already exist by content+role, not just ID
          const uniqueOlderMessages = olderMessages.filter(msg => {
            const contentKey = `${msg.role}:${msg.content}`;
            const existing = existingContentMap.get(contentKey);

            // Keep message if:
            // 1. No existing message with same content+role, OR
            // 2. Existing message has Unknown time but this one has valid time
            if (!existing) return true;

            const existingHasUnknownTime = existing.timestamp.includes("Unknown");
            const newHasUnknownTime = msg.timestamp.includes("Unknown");

            return existingHasUnknownTime && !newHasUnknownTime;
          }).reverse();

          if (uniqueOlderMessages.length === 0) {
            return prev;
          }

          // Apply our enhanced deduplication to the combined message list
          return deduplicateMessages([...uniqueOlderMessages, ...prev]);
        });

        // After state update, maintain scroll position
        setTimeout(() => {
          if (container) {
            const newScrollHeight = container.scrollHeight;
            const scrollDiff = newScrollHeight - oldScrollHeight;
            container.scrollTop = scrollDiff;
          }
        }, 0);
      }
    } catch (error) {
      console.error("Error loading more messages:", error);
      setError("Failed to load older messages");
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Handle follow-up clicks from list items
  const handleFollowUpClick = (text: string) => {
    // Set the input text to the follow-up question
    setChatInput(text);

    // Check if there's an active response from WebRTCManager
    if (webRTCManager.hasActiveResponse()) {
      setError("Please wait for the current response to complete before sending a follow-up question.");
      return;
    }

    // Optional: Auto-send the follow-up question
    if (text.trim() && !isLoading && chatId) {
      // Use setTimeout to ensure state update completes first
      setTimeout(() => {
        sendFollowUpMessage(text);
      }, 0);
    }
  };

  // Specialized version of sendMessage for follow-ups
  const sendFollowUpMessage = async (messageText: string) => {
    setError(null);

    if (isSendingMessage.current || !messageText.trim() || !isChatValid()) {
      return;
    }

    if (!user) {
      setError("User not logged in. Please log in to continue.");
      return;
    }

    // Double-check if there's an active response from WebRTCManager
    if (webRTCManager.hasActiveResponse()) {
      setError("Please wait for the current response to complete before sending a new message.");
      return;
    }

    try {
      // Set flags to indicate processing has begun
      isSendingMessage.current = true;
      setIsLoading(true);
      isStreamingMessage.current = false;

      // Add user message to UI and state
      const tempMessageId = `temp-${Date.now()}`;
      const userMessage: ChatMessage = {
        id: tempMessageId,
        role: "user",
        content: messageText,
        timestamp: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };

      // First remove any existing messages with the same content to prevent duplicates
      setChatMessages((prev) => {
        // Filter out any existing messages with same content
        const filteredMessages = prev.filter(msg =>
          !(msg.role === "user" && msg.content === messageText)
        );

        // Then add our new message and apply deduplication as a safety measure
        return deduplicateMessages([...filteredMessages, userMessage]);
      });
      setChatInput(""); // Clear input after sending

      // Persist user message
      await addMessage(userId, chatId!, messageText, "user");

      // Update chat history to reflect that this chat now has messages
      setChatHistory(prevHistory =>
        prevHistory.map(chat =>
          chat.id === chatId ? { ...chat, hasMessages: true } : chat
        )
      );

      // Set up the initial empty assistant message for streaming
      const assistantTempId = `assistant-temp-${Date.now()}`;
      const initialAssistantMessage: ChatMessage = {
        id: assistantTempId,
        role: "assistant",
        content: "",
        timestamp: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };

      setChatMessages((prev) => deduplicateMessages([...prev, initialAssistantMessage]));

      // Process the message using our enhanced message processor
      await processMessage({
        userId,
        chatId: chatId!,
        messageText,
        isMounted,
        callbacks: {
          // Handle streaming updates - called when first chunk arrives
          onChunkReceived: (streamedContent: string) => {
            // Only set isStreamingMessage to true when we actually have content
            if (!isStreamingMessage.current && streamedContent.trim().length > 0) {
              isStreamingMessage.current = true;

              // Keep loading indicator visible for a short period after first content
              setTimeout(() => {
                if (isMounted.current) {
                  setIsLoading(false);
                }
              }, 150); // Small delay to ensure content is rendered before hiding loader
            }

            setChatMessages((prev) => {
              const updatedMessages = [...prev];
              const assistantMessageIndex = updatedMessages.findIndex(
                (msg) => msg.id === assistantTempId
              );

              if (assistantMessageIndex !== -1) {
                updatedMessages[assistantMessageIndex] = {
                  ...updatedMessages[assistantMessageIndex],
                  content: streamedContent,
                };
              }

              return updatedMessages;
            });
            scrollToBottom();
          },

          // Handle errors
          onError: (errorMessage: Error | string) => {
            const errorMsg = typeof errorMessage === 'string'
              ? errorMessage
              : errorMessage.message;
            setError(errorMsg);
            setIsLoading(false);
          },

          // Handle completion - save the final message
          onComplete: async (finalContent: string) => {
            await addMessage(userId, chatId!, finalContent, "assistant");
            // Ensure loading is false when complete
            setIsLoading(false);
            isStreamingMessage.current = false;
          }
        }
      });

    } catch (error) {
      console.error("Error in sendFollowUpMessage:", error);
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
      setError(errorMessage);

      if (isMounted.current) {
        setChatMessages((prev) => {
          const errorMsg: ChatMessage = {
            id: `error-${Date.now()}`,
            role: "assistant",
            content: `**Sorry, something went wrong:** ${errorMessage}`,
            timestamp: new Date().toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            }),
          };
          return deduplicateMessages([...prev, errorMsg]);
        });
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
      isSendingMessage.current = false;
      isStreamingMessage.current = false;
    }
  };

  // Integrated sendMessage function that uses our enhanced processMessage component
  const sendMessage = async () => {
    setError(null);

    if (isSendingMessage.current || !chatInput.trim() || !isChatValid()) {
      return;
    }

    if (!user) {
      setError("User not logged in. Please log in to continue.");
      return;
    }

    // Check if there's an active response from WebRTCManager
    if (webRTCManager.hasActiveResponse()) {
      setError("Please wait for the current response to complete before sending a new message.");
      return;
    }

    try {
      // Set flags to indicate processing has begun
      isSendingMessage.current = true;
      setIsLoading(true);  // This activates the typing indicator
      isStreamingMessage.current = false;  // Ensure streaming flag starts as false

      // Add user message to UI and state
      const tempMessageId = `temp-${Date.now()}`;
      const userMessage: ChatMessage = {
        id: tempMessageId,
        role: "user",
        content: chatInput,
        timestamp: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };

      setChatMessages((prev) => deduplicateMessages([...prev, userMessage]));
      const messageText = chatInput;
      setChatInput("");

      // Persist user message
      await addMessage(userId, chatId!, messageText, "user");

      // Update chat history to reflect that this chat now has messages
      setChatHistory(prevHistory =>
        prevHistory.map(chat =>
          chat.id === chatId ? { ...chat, hasMessages: true } : chat
        )
      );

      // Set up the initial empty assistant message for streaming
      const assistantTempId = `assistant-temp-${Date.now()}`;
      const initialAssistantMessage: ChatMessage = {
        id: assistantTempId,
        role: "assistant",
        content: "",
        timestamp: new Date().toLocaleTimeString([], {
          hour: "2-digit",
          minute: "2-digit",
        }),
      };

      setChatMessages((prev) => deduplicateMessages([...prev, initialAssistantMessage]));

      // Process the message using our enhanced message processor with chat history context
      await processMessage({
        userId,
        chatId: chatId!,
        messageText,
        isMounted,
        callbacks: {
          // Handle streaming updates - called when first chunk arrives
          onChunkReceived: (streamedContent: string) => {
            // Only set isStreamingMessage to true when we actually have content
            // This creates a smoother transition from loading to content
            if (!isStreamingMessage.current && streamedContent.trim().length > 0) {
              isStreamingMessage.current = true;

              // Keep loading indicator visible for a short period after first content
              // to ensure the transition is smooth
              setTimeout(() => {
                if (isMounted.current) {
                  setIsLoading(false);
                }
              }, 150); // Small delay to ensure content is rendered before hiding loader
            }

            setChatMessages((prev) => {
              const updatedMessages = [...prev];
              const assistantMessageIndex = updatedMessages.findIndex(
                (msg) => msg.id === assistantTempId
              );

              if (assistantMessageIndex !== -1) {
                updatedMessages[assistantMessageIndex] = {
                  ...updatedMessages[assistantMessageIndex],
                  content: streamedContent,
                };
              }

              return updatedMessages;
            });
            scrollToBottom();
          },

          // Handle errors
          onError: (errorMessage: Error | string) => {
            const errorMsg = typeof errorMessage === 'string'
              ? errorMessage
              : errorMessage.message;
            setError(errorMsg);
            setIsLoading(false);
          },

          // Handle completion - save the final message
          onComplete: async (finalContent: string) => {
            await addMessage(userId, chatId!, finalContent, "assistant");
            // Ensure loading is false when complete
            setIsLoading(false);
            isStreamingMessage.current = false;
          }
        }
      });

    } catch (error) {
      console.error("Error in sendMessage:", error);
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
      setError(errorMessage);

      if (isMounted.current) {
        setChatMessages((prev) => {
          const errorMsg: ChatMessage = {
            id: `error-${Date.now()}`,
            role: "assistant",
            content: `**Sorry, something went wrong:** ${errorMessage}`,
            timestamp: new Date().toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            }),
          };
          return deduplicateMessages([...prev, errorMsg]);
        });
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);  // Ensure loading is set to false regardless of outcome
      }
      isSendingMessage.current = false;
      isStreamingMessage.current = false;  // Reset streaming flag
    }
  }

  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  useEffect(() => {
    if (!isStreamingMessage.current) {
      scrollToBottom();
    }
  }, [chatMessages]);

  // Function to switch to a different chat
  const switchChat = (selectedChatId: string) => {
    if (chatId === selectedChatId) return;

    // Clean up current chat listener
    if (unsubscribeRef.current) {
      unsubscribeRef.current();
      unsubscribeRef.current = null;
    }

    setChatId(selectedChatId);
    setChatMessages([]); // Clear messages while loading
    setHasMoreMessages(false); // Reset pagination state
    setOldestMessageSnapshot(null);
    setInitialMessagesLoaded(false);
    setIsHistoryVisible(false); // Hide sidebar on mobile after selection
  };

  const toggleHistoryVisibility = () => {
    setIsHistoryVisible(!isHistoryVisible);
  };

  // LoadingSpinner components
  const LoadingSpinner = () => (
    <div className="flex items-center justify-center h-full">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
    </div>
  );

  const LoadMoreSpinner = () => (
    <div className="flex items-center justify-center py-2">
      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
    </div>
  );

  if (isLoadingChat) {
    return (
      <div className="flex items-center justify-center h-full text-white">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="flex h-full relative">
      {/* Chat History Sidebar Component */}
      <ChatHistory
        isVisible={isHistoryVisible}
        onClose={() => setIsHistoryVisible(false)}
        chatHistory={chatHistory}
        currentChatId={chatId}
        onChatSelect={switchChat}
      />

      <div className="w-full flex flex-col h-full">
        <div className="flex items-center justify-between border-b border-white/10 p-4">
          <h3 className="text-base sm:text-lg font-semibold text-white flex items-center">
            <MessageSquare className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-green-600" />
            Jayden - AI Fitness Assistant
          </h3>
          <button
            onClick={toggleHistoryVisibility}
            className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-full transition-all duration-200"
          >
            <Menu className="w-5 h-5" />
          </button>
        </div>

        <div
          className="flex-1 overflow-y-auto p-4 space-y-4"
          ref={messagesContainerRef}
        >
          {/* Load More Messages Button */}
          {hasMoreMessages && initialMessagesLoaded && (
            <div className="flex justify-center mb-4">
              <button
                onClick={loadMoreMessages}
                disabled={isLoadingMore}
                className={`px-4 py-2 rounded-full flex items-center gap-2 ${
                  isLoadingMore
                    ? "bg-gray-700 text-gray-400"
                    : "bg-purple-500/20 text-purple-400 hover:bg-purple-500/30"
                } transition-all duration-200`}
              >
                {isLoadingMore ? (
                  <LoadMoreSpinner />
                ) : (
                  <>
                    <ArrowUp className="w-4 h-4" />
                    <span>Load older messages</span>
                  </>
                )}
              </button>
            </div>
          )}

          {/* Messages List */}
          <ChatMessages
            chatMessages={chatMessages}
            error={error}
            isLoading={isLoading}
            isStreaming={isStreamingMessage.current}
            messagesEndRef={messagesEndRef}
            onFollowUpClick={handleFollowUpClick}
          />
        </div>

        <div className="p-4 border-t border-white/10">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={chatInput}
              onChange={(e) => setChatInput(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && !isLoading && !webRTCManager.hasActiveResponse() && sendMessage()}
              placeholder={chatId ? "Ask your AI Fitness Coach..." : "Chat ID is required to send messages"}
              className="flex-1 px-4 py-3 bg-black/30 text-white rounded-full border border-white/10 focus:outline-none focus:ring-2 focus:ring-purple-500 placeholder-gray-500"
              disabled={isLoading || !chatId || webRTCManager.hasActiveResponse()}
            />
            <button
              onClick={sendMessage}
              disabled={!chatInput.trim() || isLoading || !chatId || webRTCManager.hasActiveResponse()}
              className={`p-3 rounded-full ${
                chatInput.trim() && !isLoading && chatId && !webRTCManager.hasActiveResponse()
                  ? "bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-lg shadow-purple-500/20 hover:shadow-purple-500/40"
                  : "bg-gray-700 text-gray-400"
              } transition-all duration-200`}
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ChatTab;