// lib/validation/semanticCheck.ts

import { processWithGroq } from "@/components/tools/groq-ai";

/**
 * Performs a semantic check to verify if the content matches the expected format.
 * @param content - The content to validate.
 * @returns A boolean indicating if the content is semantically valid.
 */
export async function performSemanticCheck(content: string): Promise<boolean> {
  const prompt = `Does the following data match the format of recipe? Please respond with "true" or "false".

${content}`;

  try {
    // Process with Groq
    const responseText = await processWithGroq({
      prompt,
      model: process.env.GROQ_MODEL || "llama-3.3-70b-versatile",
      modelOptions: {
        temperature: 0
      }
    });

    // Log the response
    console.log("Groq response:", responseText);

    // Perform a case-insensitive check for "true" in the response
    return responseText.toLowerCase().includes("true");
  } catch (error) {
    console.error(`Semantic check failed for ${content}:`, error);
    return false;
  }
}
