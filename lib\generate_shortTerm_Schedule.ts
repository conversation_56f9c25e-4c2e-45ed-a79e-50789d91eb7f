import { doc, getDoc, setDoc, Firestore } from "firebase/firestore";
import { v4 as uuidv4 } from "uuid";

// Core interfaces for exercise and schedule management
interface ExerciseSpecs {
  sets: string;
  reps: string;
  weight: string;
  duration: string;
  intensity: string;
}

interface CoachInstructions {
  instructions: string;
  mistakes: string;
  modifications: string;
}

interface ScheduleExercise {
  exerciseName: string;
  libraryId: string;
  completed: boolean;
  coachInstructions: CoachInstructions;
  specs: ExerciseSpecs;
  location: string;
}

interface DaySchedule {
  exercises: ScheduleExercise[];
  location: string;
  duration: string;
  name: string;
  timeFrame: string;
  workoutFrequency: string;
  focus: string;
  coreWork: string;
  strengthTraining: string;
  cardio: string;
  flexibility: string;
  completed: boolean;
}

interface Schedule {
  [key: string]: DaySchedule;
}

interface ScheduleData {
  scheduleId: string;
  schedule: Schedule;
  lastUpdated: string;
  workoutReference: string;
  title: string;
  programMetadata?: {
    totalWeeks: number;
    programGoals: string;
  };
}

// Short term training program interfaces
interface ShortTermProgram {
  programMetadata: {
    totalWeeks: number;
    programGoals: string;
  };
  weeklyPlans: {
    weekNumber: number;
    focus: string;
    workoutDays: {
      day: string;
      exercises: {
        name: string;
        libraryId?: string; // libraryId is now expected in the input
        location: string;
        specs: string;
        weight?: string;
        instructions: string;
        mistakes: string;
        modifications: string;
      }[];
    }[];
  }[];
}

// Utility functions for data sanitization and validation
function sanitizeString(value: string | number | undefined): string {
  if (value === undefined || value === null) return "";
  const stringValue = String(value);
  return stringValue.replace(/[\r\n\t]/g, " ").trim();
}

function sanitizeNumber(value: string | number | undefined): string {
  if (value === undefined || value === null) return "";
  const numValue = typeof value === 'number' ? value : parseFloat(String(value));
  return isNaN(numValue) ? "" : String(numValue);
}

// JSON parsing with validation for ShortTermProgram
function safeParseJSON(jsonString: string): ShortTermProgram | null {
  try {
    const cleanedString = jsonString
      .replace(/^```json\n/, '')
      .replace(/\n```$/, '')
      .trim();
    
    const parsedData = JSON.parse(cleanedString);
    
    if (
      !parsedData.programMetadata ||
      !parsedData.weeklyPlans ||
      !Array.isArray(parsedData.weeklyPlans)
    ) {
      console.error('Invalid short-term program structure');
      return null;
    }
    
    return parsedData as ShortTermProgram;
  } catch (error) {
    console.error("Error parsing JSON:", error);
    return null;
  }
}

// Exercise creation with validation
function createExercise(
  name: string,
  libraryId: string | undefined,
  specs: Partial<ExerciseSpecs> = {},
  instructions: string = "",
  mistakes: string = "None specified",
  modifications: string = "None specified"
): ScheduleExercise {
  if (!name || typeof name !== 'string') {
    throw new Error('Exercise name is required and must be a string');
  }

  const sanitizedName = sanitizeString(name);
  const sanitizedSpecs: ExerciseSpecs = {
    sets: sanitizeNumber(specs.sets),
    reps: sanitizeNumber(specs.reps),
    weight: sanitizeString(specs.weight),
    duration: sanitizeString(specs.duration),
    intensity: sanitizeString(specs.intensity)
  };

  return {
    exerciseName: sanitizedName,
    libraryId: sanitizeString(libraryId),
    completed: false,
    coachInstructions: {
      instructions: sanitizeString(instructions),
      mistakes: sanitizeString(mistakes),
      modifications: sanitizeString(modifications),
    },
    specs: sanitizedSpecs,
    location: ""
  };
}

// Main schedule generation function
export async function generateAndStoreSchedule(
  db: Firestore,
  userEmail: string,
  _documentId: string
): Promise<void> {
  if (!db || !userEmail) {
    throw new Error("Database instance and user email are required");
  }

  try {
    const workoutPlanRef = doc(db, "IF_users", userEmail, "Profile", "workoutplan");
    const workoutPlanSnap = await getDoc(workoutPlanRef);

    if (!workoutPlanSnap.exists()) {
      throw new Error("No workout plan found for user");
    }

    const workoutPlanData = workoutPlanSnap.data();
    const workoutReference = workoutPlanData?.WorkoutReference;

    if (!workoutReference) {
      throw new Error("Invalid workout reference in plan");
    }

    const fileRef = doc(db, "users", userEmail, "files", workoutReference);
    const fileSnap = await getDoc(fileRef);

    if (!fileSnap.exists()) {
      throw new Error("Exercise program file not found");
    }

    const data = fileSnap.data();
    if (!data?.shortTermPlanning || typeof data.shortTermPlanning !== 'string') {
      throw new Error("Invalid exercise program data format");
    }

    const shortTermProgram = safeParseJSON(data.shortTermPlanning);
    if (!shortTermProgram) {
      throw new Error("Invalid short-term training program structure");
    }

    const schedule: Schedule = {};

    // Iterate over weekly plans and workout days
    for (const weeklyPlan of shortTermProgram.weeklyPlans) {
      for (const workoutDay of weeklyPlan.workoutDays) {
        const dayKey = `Week ${weeklyPlan.weekNumber} - ${sanitizeString(workoutDay.day)}`;
        const daySchedule: DaySchedule = {
          exercises: [],
          location: "",
          duration: "",
          name: sanitizeString(workoutDay.day),
          timeFrame: "",
          workoutFrequency: "",
          focus: sanitizeString(weeklyPlan.focus),
          coreWork: "",
          strengthTraining: "",
          cardio: "",
          flexibility: "",
          completed: false
        };

        // Process each exercise in the workout day
        for (const exercise of workoutDay.exercises) {
          const scheduleExercise = createExercise(
            exercise.name,
            exercise.libraryId,
            {
              sets: "",
              reps: "",
              weight: sanitizeString(exercise.weight),
              duration: sanitizeString(exercise.specs),
              intensity: ""
            },
            sanitizeString(exercise.instructions),
            sanitizeString(exercise.mistakes),
            sanitizeString(exercise.modifications)
          );
          scheduleExercise.location = sanitizeString(exercise.location);
          daySchedule.exercises.push(scheduleExercise);
        }

        schedule[dayKey] = daySchedule;
      }
    }

    const scheduleRef = doc(db, "IF_users", userEmail, "Profile", "shortTerm");
    const scheduleData: ScheduleData = {
      scheduleId: uuidv4(),
      schedule,
      lastUpdated: new Date().toISOString(),
      workoutReference,
      title: "Short Term Training Program",
      programMetadata: {
        totalWeeks: shortTermProgram.programMetadata.totalWeeks,
        programGoals: sanitizeString(shortTermProgram.programMetadata.programGoals)
      }
    };

    await setDoc(scheduleRef, scheduleData);
    console.log("Workout schedule successfully stored in Firestore.");
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error("Error in schedule generation:", error);
      throw new Error(`Failed to generate schedule: ${error.message}`);
    } else {
      console.error("Unknown error in schedule generation:", error);
      throw new Error("Failed to generate schedule due to an unknown error.");
    }
  }
}