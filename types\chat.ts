// types/chat.ts

/**
 * Represents a message in the chat interface
 */
export interface ChatMessage {
    id?: string;
    role: "user" | "assistant";
    content: string;
    timestamp: string;
  }
  
  /**
   * Represents a chat history item in the sidebar
   */
  export interface ChatHistoryItem {
    id: string;
    title: string;
    createdAt: string;
    hasMessages: boolean;
  }
  
  /**
   * Callbacks for message processing
   */
  export interface MessageProcessingCallbacks {
    /**
     * Called when a new chunk of content is received during streaming
     */
    onChunkReceived: (content: string) => void;
    
    /**
     * Called when an error occurs during message processing
     */
    onError: (error: Error | string) => void;
    
    /**
     * Called when message processing is complete
     */
    onComplete: (finalContent: string) => Promise<void>;
  }
  
  /**
   * Parameters for the processMessage function
   */
  export interface ProcessMessagesParams {
    /**
     * The user ID
     */
    userId: string;
    
    /**
     * The chat ID
     */
    chatId: string;
    
    /**
     * The message text from the user
     */
    messageText: string;
    
    /**
     * Callback functions to handle different stages of processing
     */
    callbacks: MessageProcessingCallbacks;
    
    /**
     * React ref to check if component is still mounted
     */
    isMounted: React.MutableRefObject<boolean>;
  }
  
  /**
   * Raw message from Firestore
   */
  export interface FirestoreMessage {
    id?: string;
    createdAt: any; // Can be Timestamp, Date, or string
    text: string;
    userId: string;
    role: "user" | "assistant";
  }
  
  /**
   * Interface for grouped chat history
   */
  export interface GroupedChatHistory {
    [key: string]: ChatHistoryItem[];
  }
  
  /**
   * Properties for the ChatMessages component
   */
  export interface ChatMessagesProps {
    chatMessages: ChatMessage[];
    error: string | null;
    isLoading: boolean;
    isStreaming: boolean;
    messagesEndRef: React.RefObject<HTMLDivElement>;
  }
  
  /**
   * Properties for the ChatHistory component
   */
  export interface ChatHistoryProps {
    isVisible: boolean;
    onClose: () => void;
    chatHistory: ChatHistoryItem[];
    currentChatId: string | null;
    onChatSelect: (chatId: string) => void;
  }