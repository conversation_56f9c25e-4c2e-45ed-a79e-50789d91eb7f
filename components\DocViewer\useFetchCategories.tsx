import { useState, useEffect } from 'react';
import { collection, getDocs, query } from 'firebase/firestore';
import { db } from '@/components/firebase';
import { useSession } from 'next-auth/react';

function useFetchCategories() {
    const [categories, setCategories] = useState<string[]>([]);
    const { data: session } = useSession(); // Get session data

    useEffect(() => {
        const fetchCategories = async () => {
            if (!session?.user?.email) {
                console.error('User session is not available');
                return;
            }

            const userId = session.user.email;
            const categoriesSet = new Set<string>();

            try {
                // Reference to the user's files collection
                const filesCollectionRef = collection(db, 'users', userId, 'files');

                const q = query(filesCollectionRef);
                const querySnapshot = await getDocs(q);

                querySnapshot.forEach((doc) => {
                    const category = doc.data().category;
                    if (category) {
                        categoriesSet.add(category);
                    }
                });

                setCategories(Array.from(categoriesSet));
            } catch (error) {
                console.error('Error fetching categories:', error);
            }
        };

        fetchCategories();
    }, [session]);

    return categories;
}

export default useFetchCategories;
