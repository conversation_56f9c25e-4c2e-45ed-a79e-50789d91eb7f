"use client";

import { useState, useEffect } from "react";
import type { User } from "firebase/auth";
import { doc, setDoc } from "firebase/firestore";
import { db } from "@/components/firebase/config";
import type { ProfileData } from "@/types/user";

interface ProfileCompletionProgressProps {
  user: User | null;
  profileData: ProfileData | null;
  onEditProfile: () => void;
  isEditingProfile: boolean;
  onProgressChange: (progress: number) => void;
  onProfileUpdate: () => void;
}

export function ProfileCompletionProgress({
  user,
  profileData,
  onEditProfile,
  isEditingProfile,
  onProgressChange,
  onProfileUpdate,
}: ProfileCompletionProgressProps) {
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (user && profileData) {
      const totalFields = 13; // Adjusted to 13 required fields
      let completedFields = 0;

      if (parseFloat(profileData.basic_info.height.value) > 0) completedFields++;
      if (parseFloat(profileData.basic_info.weight.value) > 0) completedFields++;
      if (parseInt(profileData.basic_info.age, 10) > 0) completedFields++;
      if (profileData.basic_info.gender) completedFields++;
      if (profileData.fitness_goals.primary) completedFields++;
      if (profileData.fitness_goals.secondary) completedFields++;
      if (parseInt(profileData.workout_preferences.frequency, 10) > 0) completedFields++;
      if (parseInt(profileData.workout_preferences.duration, 10) > 0) completedFields++;
      if (profileData.workout_preferences.preferred_time) completedFields++;
      if (profileData.health_information.medical_conditions) completedFields++;
      if (profileData.health_information.injuries) completedFields++;
      if (profileData.preferences.training_location) completedFields++;
      if (profileData.preferences.focus_areas?.length > 0) completedFields++;
      // Removed sport_specific as it’s optional

      const newProgress = (completedFields / totalFields) * 100;
      setProgress(newProgress);
      onProgressChange(newProgress);
    }
  }, [user, profileData, onProgressChange]);

  const handleUpdateProfile = async () => {
    if (!user?.email) return;
    setIsLoading(true);
    try {
      const fitnessDocRef = doc(db, `IF_users/${user.email}/Profile/fitness`);
      await setDoc(fitnessDocRef, profileData || {}, { merge: true });
      onProfileUpdate();
    } catch (error) {
      console.error("Error updating profile:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <div className="text-gray-300">Loading profile progress...</div>;
  }

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-semibold text-white">Profile Completion</h2>
      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
        <div
          className="bg-purple-600 h-2.5 rounded-full"
          style={{ width: `${progress}%` }}
        ></div>
      </div>
      <p className="text-sm text-gray-300">Progress: {Math.round(progress)}%</p>
      {progress < 100 && !isEditingProfile && (
        <button
          onClick={onEditProfile}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Edit Profile
        </button>
      )}
      {isEditingProfile && (
        <button
          onClick={handleUpdateProfile}
          className="px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          Save Profile
        </button>
      )}
    </div>
  );
}