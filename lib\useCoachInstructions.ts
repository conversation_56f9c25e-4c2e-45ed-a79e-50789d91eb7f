import { doc, getDoc, setDoc } from "firebase/firestore";
import { db } from "@/components/firebase/config";
import { v4 as uuidv4 } from "uuid";
import type { DocumentReference } from "firebase/firestore";

// For the Personal Trainer plan, specs are stored as strings.
type ExerciseSpecs = string;

interface WorkoutDayExercise {
  libraryId?: string | null; // libraryId is now expected in the input
  name: string;
  specs: ExerciseSpecs;
  instructions?: string;
  mistakes?: string;
  modifications?: string;
  location?: string; // Location where the exercise is performed
  weight?: string;   // Weight lifted during the exercise (if applicable)
}

interface ParsedCoachInstructions {
  workoutDays: {
    day: string;
    exercises: WorkoutDayExercise[];
  }[];
}

interface CoachInstructions {
  instructions: string;
  mistakes: string;
  modifications: string;
}

interface ScheduleExercise {
  libraryId: string;
  exerciseName: string;
  completed: boolean;
  coachInstructions: CoachInstructions;
  specs: ExerciseSpecs;
  location?: string;
  weight?: string;
}

// Main hook: Parse coach instructions JSON and store the schedule in Firestore.
export async function useCoachInstructions(
  userEmail: string,
  coachInstructions: string,
  workoutPlanRef: DocumentReference
): Promise<void> {
  try {
    console.log("useCoachInstructions: Parsing coach instructions for schedule");
    const parsedCoachInstructions = JSON.parse(coachInstructions) as ParsedCoachInstructions;

    if (!parsedCoachInstructions?.workoutDays) {
      console.error("useCoachInstructions: No workoutDays in parsed coach instructions", parsedCoachInstructions);
      return;
    }

    // Get a reference to the user's schedule document.
    const scheduleRef = doc(db, `IF_users/${userEmail}/Profile`, "schedule");

    console.log("useCoachInstructions: Fetching workout plan document to get workoutReference");
    const workoutPlanDoc = await getDoc(workoutPlanRef);
    const workoutPlanData = workoutPlanDoc.data();

    // Use the existing WorkoutReference if available; otherwise, use the workout plan document ID.
    const workoutReference = workoutPlanData?.WorkoutReference ?? workoutPlanRef.id;
    console.log(`useCoachInstructions: Using WorkoutReference: ${workoutReference}`);

    // Build the exercises object.
    const exercises: Record<string, ScheduleExercise[]> = {};

    for (const workoutDay of parsedCoachInstructions.workoutDays) {
      const dayExercises: ScheduleExercise[] = [];
      for (const exercise of workoutDay.exercises) {
        const {
          name,
          libraryId = "", // Default to empty string if not provided
          instructions = "",
          mistakes = "",
          modifications = "",
          specs,
          weight = "",
          location = ""
        } = exercise;

        const scheduleExercise: ScheduleExercise = {
          libraryId: libraryId || "", // Use provided libraryId or fallback to empty string
          exerciseName: name,
          completed: false,
          coachInstructions: {
            instructions,
            mistakes,
            modifications,
          },
          specs,
          weight,
          location,
        };
        dayExercises.push(scheduleExercise);
      }
      exercises[workoutDay.day] = dayExercises;
    }

    console.log(`useCoachInstructions: Creating schedule document with workoutReference: ${workoutReference}`);
    await setDoc(
      scheduleRef,
      {
        scheduleId: uuidv4(),
        exercises,
        lastUpdated: new Date(),
        workoutReference,
      },
      { merge: true }
    );

    console.log("useCoachInstructions: Successfully stored workout schedule with workoutReference:", workoutReference);
  } catch (scheduleError) {
    console.error("useCoachInstructions: Error parsing schedule:", scheduleError);
  }
}