'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { User, <PERSON><PERSON><PERSON>, Building2 } from 'lucide-react'

export default function UserTypeSelection() {
  const [selectedType, setSelectedType] = useState<string | null>(null)
  const router = useRouter()

  const handleContinue = () => {
    if (selectedType) {
      // Store the selected user type in localStorage or context
      localStorage.setItem('userType', selectedType)
      router.push('/auth/complete-profile/experience-goals')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Select Your User Type
          </h2>
        </div>
        <div className="mt-8 space-y-4">
          {[
            { type: 'Individual', icon: User },
            { type: 'Fitness Coach/Trainer', icon: Dumbbell },
            { type: 'Business/Studio', icon: Building2 },
          ].map((option) => (
            <button
              key={option.type}
              onClick={() => setSelectedType(option.type)}
              className={`relative w-full flex items-center justify-start px-4 py-2 border ${
                selectedType === option.type ? 'border-blue-600' : 'border-gray-300'
              } text-sm font-medium rounded-md ${
                selectedType === option.type ? 'bg-blue-50 text-blue-700' : 'bg-white text-gray-700'
              } hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
            >
              <option.icon className="h-5 w-5 mr-3" aria-hidden="true" />
              {option.type}
            </button>
          ))}
        </div>
        <button
          onClick={handleContinue}
          disabled={!selectedType}
          className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </div>
  )
}

