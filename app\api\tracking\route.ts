import { type NextRequest, NextResponse } from "next/server"
import { doc, getDoc, setDoc, collection, query, getDocs, Timestamp, updateDoc, deleteDoc } from "firebase/firestore"
import { db } from "@/components/firebase/config"

// GET handler to fetch tracking records for a user
export async function GET(request: NextRequest) {
  try {
    // Get user email from query params
    const { searchParams } = new URL(request.url)
    const userEmail = searchParams.get("userEmail")

    if (!userEmail) {
      return NextResponse.json({ error: "User email is required" }, { status: 400 })
    }

    // Check if tracking document exists
    const trackingRef = doc(db, "IF_users", userEmail, "Profile", "tracking")
    const trackingSnap = await getDoc(trackingRef)

    if (!trackingSnap.exists()) {
      // Create tracking document if it doesn't exist
      await createTrackingDocument(userEmail)
    }

    // Fetch tracking records
    const recordsRef = collection(db, "IF_users", userEmail, "tracking_records")
    const q = query(recordsRef)
    const querySnapshot = await getDocs(q)

    // Define a type for tracking records
    interface TrackingRecord {
      id: string;
      exerciseId: string;
      exerciseName: string;
      libraryId: string;
      workoutReference: string;
      completedDate: { toMillis: () => number }; // Simplified Firestore Timestamp interface
      difficultyLevel: number;
      energyLevel: number;
      timeToComplete: number;
      sets?: number;
      reps?: number;
      weight?: number;
      notes?: string;
      day: number;
      category: string;
      [key: string]: unknown; // For any other properties
    }

    const records: TrackingRecord[] = []

    querySnapshot.forEach((doc) => {
      // Cast the document data to TrackingRecord type
      records.push({ id: doc.id, ...doc.data() } as TrackingRecord)
    })

    return NextResponse.json({ success: true, records })
  } catch (error) {
    console.error("Error fetching tracking records:", error)
    return NextResponse.json({ error: "Failed to fetch tracking records" }, { status: 500 })
  }
}

// POST handler to create a new tracking record
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userEmail, recordData } = body

    if (!userEmail || !recordData) {
      return NextResponse.json({ error: "User email and record data are required" }, { status: 400 })
    }

    // Check if tracking document exists
    const trackingRef = doc(db, "IF_users", userEmail, "Profile", "tracking")
    const trackingSnap = await getDoc(trackingRef)

    if (!trackingSnap.exists()) {
      // Create tracking document if it doesn't exist
      await createTrackingDocument(userEmail)
    }

    // Create new record
    const recordRef = doc(collection(db, "IF_users", userEmail, "tracking_records"))
    await setDoc(recordRef, {
      ...recordData,
      completedDate: Timestamp.now(),
    })

    // Update tracking document's lastUpdated field
    await updateDoc(trackingRef, {
      lastUpdated: Timestamp.now(),
    })

    return NextResponse.json({
      success: true,
      message: "Tracking record created successfully",
      recordId: recordRef.id,
    })
  } catch (error) {
    console.error("Error creating tracking record:", error)
    return NextResponse.json({ error: "Failed to create tracking record" }, { status: 500 })
  }
}

// PUT handler to update an existing tracking record
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { userEmail, recordId, recordData } = body

    if (!userEmail || !recordId || !recordData) {
      return NextResponse.json({ error: "User email, record ID, and record data are required" }, { status: 400 })
    }

    // Update record
    const recordRef = doc(db, "IF_users", userEmail, "tracking_records", recordId)
    await updateDoc(recordRef, recordData)

    // Update tracking document's lastUpdated field
    const trackingRef = doc(db, "IF_users", userEmail, "Profile", "tracking")
    await updateDoc(trackingRef, {
      lastUpdated: Timestamp.now(),
    })

    return NextResponse.json({
      success: true,
      message: "Tracking record updated successfully",
    })
  } catch (error) {
    console.error("Error updating tracking record:", error)
    return NextResponse.json({ error: "Failed to update tracking record" }, { status: 500 })
  }
}

// DELETE handler to delete a tracking record
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userEmail = searchParams.get("userEmail")
    const recordId = searchParams.get("recordId")

    if (!userEmail || !recordId) {
      return NextResponse.json({ error: "User email and record ID are required" }, { status: 400 })
    }

    // Delete record
    const recordRef = doc(db, "IF_users", userEmail, "tracking_records", recordId)
    await deleteDoc(recordRef)

    // Update tracking document's lastUpdated field
    const trackingRef = doc(db, "IF_users", userEmail, "Profile", "tracking")
    await updateDoc(trackingRef, {
      lastUpdated: Timestamp.now(),
    })

    return NextResponse.json({
      success: true,
      message: "Tracking record deleted successfully",
    })
  } catch (error) {
    console.error("Error deleting tracking record:", error)
    return NextResponse.json({ error: "Failed to delete tracking record" }, { status: 500 })
  }
}

// Helper function to create tracking document
async function createTrackingDocument(userEmail: string) {
  try {
    const trackingRef = doc(db, "IF_users", userEmail, "Profile", "tracking")

    // Get workoutReference from kick_starter document
    const kickStarterRef = doc(db, "IF_users", userEmail, "Profile", "kick_starter")
    const kickStarterSnap = await getDoc(kickStarterRef)

    if (!kickStarterSnap.exists()) {
      throw new Error("Kick starter document not found")
    }

    const kickStarterData = kickStarterSnap.data()
    const workoutReference = kickStarterData.workoutReference || ""

    // Create tracking document with initial data
    await setDoc(trackingRef, {
      workoutReference,
      createdAt: Timestamp.now(),
      lastUpdated: Timestamp.now(),
    })

    console.log("Tracking document created successfully")
  } catch (err) {
    console.error("Error creating tracking document:", err)
    throw err
  }
}

