"use client";

import Link from "next/link";
import { Target, Users, Zap } from "lucide-react";
import Image from "next/image";
import "./styles/animations.css";
import { JSX, useEffect, useState } from "react";
import { 
  getProfileStatus, 
  checkWorkoutPlanExists, 
  getSelectedPlanName,
  checkWorkoutPlanDocumentExists,
  getUserProfile 
} from "@/utils/firebase";
import { auth } from "@/components/firebase/config";
import { onAuthStateChanged } from "firebase/auth";
import Footer from "@/components/Footer";
import { useWorkoutGeneration } from "@/components/WorkoutGenerationContext";
import { WorkoutGenerationOverlay } from "@/components/WorkoutGenerationOverlay";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/components/firebase/config";

// Import modal components
import { FitnessJourneyModal, ExpectedLongTermSchedule } from "@/components/DashboardModal/fitness-journey-modal";
import { KickStarterModal } from "@/components/DashboardModal/kick-starter-modal";
import { PersonalTrainerModal, PersonalTrainerSchedule } from "@/components/DashboardModal/personal-trainer-modal";
import { SevenDayModal } from "@/components/DashboardModal/Seven-day-modal";

interface ProfileStatus {
  HasPdf: boolean;
  audioPath: string;
  error: string | null;
  lastError: string;
  lastPdfGenerated: string;
  lastUpdated: string;
  pdfPath: string;
  status: string;
  summary: string;
}

type WorkoutPlanType = "kick_starter" | "shortTerm" | "longTerm" | "schedule";

interface OptimizedSchedule {
  title: string;
  days: {
    name: string;
    exercises: { name: string; completed: boolean }[];
  }[];
  lastUpdated: string;
  workoutReference?: string;
  scheduleId?: string;
}

interface SevenDaySchedule {
  schedule: {
    [day: string]: {
      focus: string;
      location: string;
      duration: string;
      intensity: string;
      coreWork: string;
      cardio: string;
      exercises: {
        exerciseName: string;
        specs: {
          duration: string;
          weight?: string;
        };
        completed: boolean;
        coachInstructions: {
          instructions: string;
          mistakes: string;
          modifications: string;
        };
      }[];
    };
  };
  weeklyGoals: {
    primary: string;
    secondary: string[];
  };
  recovery: {
    restDays: string[];
    stretchingRoutine: string;
    coolDown: string;
  };
  nutrition: {
    preworkout: string;
    postworkout: string;
    hydration: string;
  };
}

type ModalData = ExpectedLongTermSchedule | PersonalTrainerSchedule | SevenDaySchedule | OptimizedSchedule | null;

export default function Home() {
  const [userStatus, setUserStatus] = useState({
    hasCompletedAssessment: false,
    hasWorkoutPlan: false,
    hasWorkoutPlanDocument: false,
    planType: null as WorkoutPlanType | null,
    profileCompletion: 0,
  });
  const [loading, setLoading] = useState(true);
  const { generationState, startGeneration } = useWorkoutGeneration();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [modalData, setModalData] = useState<ModalData>(null);
  const [selectedPlanName, setSelectedPlanName] = useState<string | null>(null);

  useEffect(() => {
    let unsubscribeAuth: () => void = () => {};

    const fetchUserData = async (userEmail: string | null) => {
      if (!userEmail) {
        setLoading(false);
        return;
      }

      try {
        const profileData = await getUserProfile(userEmail);
        const profileStatus = await getProfileStatus(userEmail);
        
        if (profileData || profileStatus) {
          const assessment = profileStatus as unknown as ProfileStatus;
          const hasCompletedAssessment = assessment?.status === "completed" || (profileData && profileData.profileComplete === true);
          const profileCompletion = profileData?.profileComplete === true ? 100 : (profileData ? 50 : 0);

          if (hasCompletedAssessment) {
            const [hasWorkoutPlan, workoutPlanDocExists, planTypeString] = await Promise.all([
              checkWorkoutPlanExists(userEmail),
              checkWorkoutPlanDocumentExists(userEmail),
              getSelectedPlanName(userEmail),
            ]);
            
            const planType = (["kick_starter", "shortTerm", "longTerm", "schedule"] as const)
              .includes(planTypeString as WorkoutPlanType) 
                ? planTypeString as WorkoutPlanType 
                : null;

            setUserStatus({
              hasCompletedAssessment: true,
              hasWorkoutPlan: !!planType && hasWorkoutPlan,
              hasWorkoutPlanDocument: workoutPlanDocExists,
              planType,
              profileCompletion,
            });
            setSelectedPlanName(planType); // Set for button text
          } else {
            setUserStatus({
              hasCompletedAssessment: false,
              hasWorkoutPlan: false,
              hasWorkoutPlanDocument: false,
              planType: null,
              profileCompletion: profileData ? 50 : 0,
            });
          }
        } else {
          setUserStatus({
            hasCompletedAssessment: false,
            hasWorkoutPlan: false,
            hasWorkoutPlanDocument: false,
            planType: null,
            profileCompletion: 0,
          });
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
        setUserStatus({
          hasCompletedAssessment: false,
          hasWorkoutPlan: false,
          hasWorkoutPlanDocument: false,
          planType: null,
          profileCompletion: 0,
        });
      } finally {
        setLoading(false);
      }
    };

    unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      const email = user?.email ?? null;
      fetchUserData(email);
    });

    return () => unsubscribeAuth();
  }, []);

  const fetchScheduleData = async (userEmail: string, planName: string): Promise<ModalData> => {
    try {
      const scheduleRef = doc(db, "IF_users", userEmail, "Profile", planName);
      const scheduleSnapshot = await getDoc(scheduleRef);
      if (!scheduleSnapshot.exists()) {
        console.error(`No schedule found for plan: ${planName}`);
        return null;
      }
      const data = scheduleSnapshot.data() as
        | ExpectedLongTermSchedule
        | OptimizedSchedule
        | SevenDaySchedule
        | PersonalTrainerSchedule;
      if (planName === "longTerm") {
        // Assuming transformLongTermSchedule is not needed here for simplicity, adjust if required
        return data as ExpectedLongTermSchedule;
      }
      return data as ModalData;
    } catch (error: unknown) {
      console.error(`Error fetching schedule for ${planName}:`, error);
      return null;
    }
  };

  const handleGenerateWorkoutPlan = async () => {
    try {
      const user = auth.currentUser;
      if (!user || !user.email) {
        console.error("User not logged in");
        return;
      }
      
      const profileData = await getUserProfile(user.email);
      if (!profileData) {
        console.error("No profile data found");
        return;
      }
      
      const profileText = JSON.stringify(profileData);
      await startGeneration(profileText);
    } catch (error) {
      console.error("Error initiating workout plan generation:", error);
    }
  };

  const handleAlignedWorkoutPlanAction = async () => {
    const user = auth.currentUser;
    if (!user || !user.email) {
      console.log("No user logged in");
      return;
    }

    console.log("Button clicked with status:", {
      profileCompletion: userStatus.profileCompletion,
      hasWorkoutPlan: userStatus.hasWorkoutPlan,
      planType: userStatus.planType,
      hasWorkoutPlanDocument: userStatus.hasWorkoutPlanDocument
    });

    if (userStatus.profileCompletion < 100 && !userStatus.hasWorkoutPlan) {
      window.location.href = "/profile/complete?step=basic-info";
    } else if (generationState !== 'idle') {
      console.log("Generation in progress, not taking action");
      // Do nothing while generating
    } else if (userStatus.profileCompletion === 100 && !userStatus.hasWorkoutPlanDocument) {
      await handleGenerateWorkoutPlan();
    } else if (userStatus.profileCompletion >= 100 && userStatus.planType && userStatus.hasWorkoutPlan) {
      try {
        console.log("Fetching workout plan data for", user.email, userStatus.planType);
        const scheduleData = await fetchScheduleData(user.email, userStatus.planType);
        console.log("Fetched schedule data:", scheduleData);
        
        if (scheduleData) {
          setModalData(scheduleData);
          setSelectedPlanName(userStatus.planType);
          setIsModalOpen(true);
          console.log("Modal should be open now with data:", scheduleData);
        } else {
          console.error("No schedule data found");
        }
      } catch (error) {
        console.error("Error opening workout plan:", error);
      }
    } else if (userStatus.profileCompletion === 100 && !userStatus.hasWorkoutPlan) {
      window.location.href = "/workout-selector";
    }
  };

  const getAlignedButtonText = (): string => {
    if (userStatus.profileCompletion < 100 && !userStatus.hasWorkoutPlan) return "Complete Personal Profile";
    if (generationState !== 'idle') return "Generating...";
    if (userStatus.profileCompletion === 100 && !userStatus.hasWorkoutPlanDocument) return "Generate Workout Plan";
    if (userStatus.profileCompletion === 100 && !userStatus.hasWorkoutPlan) return "Select Workout Plan";
    if (userStatus.planType) return `View ${userStatus.planType.replace('_', ' ').charAt(0).toUpperCase() + userStatus.planType.slice(1)} Plan`;
    return "Select Workout Plan";
  };

  const getButtonColor = (): string => {
    if (userStatus.profileCompletion < 100 && !userStatus.hasWorkoutPlan) {
      return "border-blue-500 text-blue-500 hover:border-blue-600 hover:text-blue-600";
    }
    if (generationState !== 'idle') {
      return "border-gray-500 text-gray-500 opacity-50 cursor-not-allowed";
    }
    if (userStatus.profileCompletion === 100 && !userStatus.hasWorkoutPlanDocument) {
      return "border-red-400 text-red-400 hover:border-red-500 hover:text-red-500";
    }
    if (userStatus.profileCompletion === 100 && !userStatus.hasWorkoutPlan) {
      return "border-amber-500 text-amber-500 hover:border-amber-600 hover:text-amber-600";
    }
    if (userStatus.planType) {
      return "border-green-500 text-green-500 hover:border-green-600 hover:text-green-600";
    }
    return "border-amber-500 text-amber-500 hover:border-amber-600 hover:text-amber-600";
  };

  // Add effect hook to monitor modal state changes
  useEffect(() => {
    console.log("Modal state changed:", {
      isModalOpen,
      selectedPlanName,
      hasModalData: !!modalData
    });
  }, [isModalOpen, selectedPlanName, modalData]);

  const renderModal = (): JSX.Element | null => {
    console.log("Rendering modal with:", {
      isModalOpen,
      selectedPlanName,
      modalData: modalData ? "Has data" : "No data"
    });
    
    if (!isModalOpen || !selectedPlanName || !modalData) {
      console.log("Not rendering modal due to missing requirements");
      return null;
    }
    
    switch (selectedPlanName) {
      case "longTerm":
        return (
          <FitnessJourneyModal
            isOpen={isModalOpen}
            onClose={() => {
              console.log("Closing longTerm modal");
              setIsModalOpen(false);
            }}
            data={modalData as ExpectedLongTermSchedule}
            currentPhase={1}
            setCurrentPhase={() => {}}
            phaseProgress={0}
          />
        );
      case "kick_starter":
        return (
          <KickStarterModal
            isOpen={isModalOpen}
            onClose={() => {
              console.log("Closing kick_starter modal");
              setIsModalOpen(false);
            }}
            data={modalData as OptimizedSchedule}
          />
        );
      case "shortTerm":
        return (
          <SevenDayModal
            isOpen={isModalOpen}
            onClose={() => {
              console.log("Closing shortTerm modal");
              setIsModalOpen(false);
            }}
            data={modalData as SevenDaySchedule}
          />
        );
      case "schedule":
        return (
          <PersonalTrainerModal
            isOpen={isModalOpen}
            onClose={() => {
              console.log("Closing schedule modal");
              setIsModalOpen(false);
            }}
            data={modalData as PersonalTrainerSchedule}
          />
        );
      default:
        console.log("No matching modal component found for:", selectedPlanName);
        return null;
    }
  };

  let linkComponent = null;

  if (loading) {
    linkComponent = (
      <div className="block w-full max-w-md mx-auto text-center text-white">
        Loading...
      </div>
    );
  } else {
    const buttonText = getAlignedButtonText();
    const buttonColor = getButtonColor();

    if (!userStatus.hasCompletedAssessment) {
      linkComponent = (
        <Link
          href="/profile/complete?step=basic-info"
          className={`block w-full max-w-md mx-auto rounded-4xl bg-transparent ${buttonColor} text-2xl font-bold py-3 px-6 hover:${buttonColor.replace('border-', 'border-').replace('text-', 'text-').replace('-500', '-600')} transition duration-300 ease-in-out transform hover:-translate-y-1`}
        >
          {buttonText}
        </Link>
      );
    } else if (userStatus.hasCompletedAssessment && !userStatus.hasWorkoutPlanDocument) {
      linkComponent = (
        <button
          onClick={handleGenerateWorkoutPlan}
          disabled={generationState !== 'idle'}
          className={`block w-full max-w-md mx-auto rounded-4xl bg-transparent ${buttonColor} text-2xl font-bold py-3 px-6 hover:${buttonColor.replace('border-', 'border-').replace('text-', 'text-').replace('-500', '-600')} transition duration-300 ease-in-out transform hover:-translate-y-1 ${
            generationState !== 'idle' ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {buttonText}
        </button>
      );
    } else if (userStatus.hasCompletedAssessment && userStatus.hasWorkoutPlanDocument && !userStatus.hasWorkoutPlan) {
      linkComponent = (
        <Link
          href="/workout-selector"
          className={`block w-full max-w-md text-xl mx-auto rounded-4xl bg-transparent ${buttonColor} text-2xl font-bold py-3 px-6 hover:${buttonColor.replace('border-', 'border-').replace('text-', 'text-').replace('-500', '-600')} transition duration-300 ease-in-out transform hover:-translate-y-1`}
        >
          {buttonText}
        </Link>
      );
    } else {
      const planNameMap: Record<WorkoutPlanType, string> = {
        "kick_starter": "Kick Starter",
        "shortTerm": "4 Week",
        "longTerm": "Long-Term",
        "schedule": "7-Day",
      };
      const planName = userStatus.planType ? planNameMap[userStatus.planType] : "Workout";
      linkComponent = (
        <div className="flex flex-col text-xs sm:flex-row w-full max-w-md mx-auto space-y-4 sm:space-y-0 sm:space-x-4">
          <Link
            href="/profile-summary"
            className="block w-full max-w-md rounded-4xl bg-transparent border-4 border-amber-500 text-amber-500 text-xl font-bold py-3 px-6 hover:border-amber-600 hover:text-amber-600 transition duration-300 ease-in-out transform hover:-translate-y-1"
          >
            Review Assessment
          </Link>
          <button
            onClick={handleAlignedWorkoutPlanAction}
            disabled={generationState !== 'idle'}
            className={`block w-full max-w-md rounded-4xl bg-transparent border-4 font-bold py-3 px-6 transition duration-300 ease-in-out transform hover:-translate-y-1 ${getButtonColor()} ${
              generationState !== 'idle' ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            View {planName} Plan
          </button>
        </div>
      );
    }
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-grow">
        <section className="relative">
          <Image
            src="/bg-websiteProfile.png?height=1080&width=1920"
            alt="Fitness background"
            width={1920}
            height={1080}
            className="absolute inset-0 w-full h-full object-cover"
          />
          <div className="relative z-10 bg-black bg-opacity-50 py-20">
            <div className="container mx-auto px-4 text-center text-white">
              <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-fade-in-slide">
                Your AI-Powered <span className="text-blue-400">Fitness Journey</span>
              </h1>
              <p className="text-xl mb-8 max-w-2xl mx-auto animate-fade-in delay-200">
                Personalized workout plans, expert guidance, and a supportive community to help you achieve your fitness
                goals.
              </p>
              {linkComponent}
            </div>
          </div>
        </section>
        <section className="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 mb-10">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: Zap,
                  title: "AI Workout Plans",
                  description: "Personalized routines tailored to your goals and fitness level",
                },
                {
                  icon: Target,
                  title: "Goal Tracking",
                  description: "Track your progress and stay motivated with detailed analytics",
                },
                {
                  icon: Users,
                  title: "Community",
                  description: "Connect with like-minded fitness enthusiasts and share your journey",
                },
              ].map((feature, index) => (
                <div key={index} className="text-center">
                  <feature.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>
      <Footer />
      {renderModal()}
      <WorkoutGenerationOverlay />
    </div>
  );
}