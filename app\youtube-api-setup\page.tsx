"use client";

import { useState, useEffect } from "react";
import Link from "next/link";

export default function YouTubeApiSetup() {
  const [_apiKey, _setApiKey] = useState<string>("");
  const [baseUrl, setBaseUrl] = useState<string>("");
  const [testResult, setTestResult] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Get the base URL of the application
    setBaseUrl(window.location.origin);
  }, []);

  const testApiKey = async () => {
    setIsLoading(true);
    setTestResult(null);

    try {
      // Create a simple test query
      const query = "exercise demonstration";
      const url = `/api/youtube-search?q=${encodeURIComponent(query)}&maxResults=1&mock=false`;

      const response = await fetch(url);
      const data = await response.json();

      if (data.items && data.items.length > 0) {
        setTestResult("Success! The YouTube API is working correctly.");
      } else if (data.error) {
        setTestResult(`Error: ${JSON.stringify(data.error, null, 2)}`);
      } else {
        setTestResult("No results returned. This might indicate an issue with the API.");
      }
    } catch (error) {
      setTestResult(`Error testing API: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 text-slate-800">
      <h1 className="text-3xl font-bold mb-6 text-amber-500">YouTube API Setup</h1>

      <div className="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
        <p className="text-blue-700">
          This page helps you configure and test your YouTube API key for use with the application.
        </p>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4 text-amber-500">Current Configuration</h2>
        <div className="bg-gray-100 p-4 rounded-md text-slate-500">
          <p>
            <strong className="text-amber-500">Base URL:</strong> {baseUrl}
          </p>
          <p className="mt-2">
            <strong className="text-amber-500">Environment:</strong> {process.env.NODE_ENV}
          </p>
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4 text-amber-500">API Key Configuration</h2>
        <p className="mb-4 text-blue-200">
          Your YouTube API key needs to be configured with the correct HTTP referrer restrictions.
          Add the following to your allowed referrers in the Google Cloud Console:
        </p>
        <div className="bg-gray-100 p-4 rounded-md font-mono text-slate-500">
          {`${baseUrl}/*`}
        </div>
        <p className="mt-4 text-blue-200">
          For local development, also add:
        </p>
        <div className="bg-gray-100 p-4 rounded-md font-mono text-slate-500">
          http://localhost:3000/*
        </div>
        <p className="mt-4 text-blue-200">
          For production, also add:
        </p>
        <div className="bg-gray-100 p-4 rounded-md font-mono text-slate-500">
          https://intelligentfitness.ai/*
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4 text-amber-500">Test Your API Key</h2>
        <button
          onClick={testApiKey}
          disabled={isLoading}
          className="px-4 py-2 bg-amber-500 hover:bg-amber-600 text-white rounded-md disabled:opacity-50"
        >
          {isLoading ? "Testing..." : "Test API Key"}
        </button>

        {testResult && (
          <div className={`mt-4 p-4 rounded-md ${testResult.startsWith("Success") ? "bg-green-50 text-green-700" : "bg-red-50 text-red-700"}`}>
            <pre className="whitespace-pre-wrap">{testResult}</pre>
          </div>
        )}
      </div>

      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4 text-amber-500">Detailed Setup Instructions</h2>
        <p className="text-blue-200">
          For detailed instructions on configuring your YouTube API key, view the{" "}
          <Link href="/youtube-api-setup.html" target="_blank" className="text-amber-500 hover:underline">
            YouTube API Setup Guide
          </Link>
          .
        </p>
      </div>

      <div className="mt-8 pt-4 border-t border-gray-200">
        <Link href="/library" className="text-amber-500 hover:underline">
          ← Back to Exercise Library
        </Link>
      </div>
    </div>
  );
}
