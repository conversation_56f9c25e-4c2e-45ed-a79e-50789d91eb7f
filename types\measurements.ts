export type HeightUnit = "cm" | "ft" | "m"
export type WeightUnit = "kg" | "lbs" | "st"
export type Gender = "male" | "female" | "other" | "prefer-not-to-say"

export interface MeasurementWithUnit {
  value: string
  unit: HeightUnit | WeightUnit
}

export interface BasicInfo {
  height: MeasurementWithUnit
  weight: MeasurementWithUnit
  age: string
  gender: Gender
}

// New types for preferences
export type TrainingLocation = "gym" | "home" | "outdoors"
export type FocusArea = "arms" | "legs" | "glutes" | "shoulders" | "chest" | "back" | "core" | "full body"

export interface StepData {
  basic_info: {
    height: {
      value: string
      unit: HeightUnit
    }
    weight: {
      value: string
      unit: WeightUnit
    }
    age: string
    gender: Gender
  }
  fitness_goals: {
    primary: string
    secondary: string
  }
  preferences: {
    training_location: string
    focus_areas?: string[] // Updated to be an array of strings
    sport_specific?: string
  }
  workout_preferences: {
    frequency: string
    duration: string
    preferred_time: string
  }
  health_information: {
    medical_conditions: string
    injuries: string
  }
}

