// src/utils/profile-utils.ts

import type { StepData } from "@/types/measurements"

/**
 * Generates a formatted text representation of a user's fitness profile
 * 
 * @param formData - The complete form data containing all profile sections
 * @param city - Optional city location of the user
 * @param country - Optional country location of the user
 * @returns A formatted string containing the complete profile information
 * 
 * @example
 * const profileText = generateProfileText(formData, "New York", "USA");
 */
export const generateProfileText = (
  formData: StepData,
  city?: string,
  country?: string
): string => {
  // Safely access nested properties with nullish coalescing
  const basicInfo = formData.basic_info || {}
  const fitnessGoals = formData.fitness_goals || {}
  const workoutPrefs = formData.workout_preferences || {}
  const healthInfo = formData.health_information || {}

  // Format height and weight with units
  const heightDisplay = basicInfo.height?.value
    ? `${basicInfo.height.value} ${basicInfo.height.unit || ''}`
    : "Not specified"
  
  const weightDisplay = basicInfo.weight?.value
    ? `${basicInfo.weight.value} ${basicInfo.weight.unit || ''}`
    : "Not specified"

  // Build the profile text with consistent formatting
  return `
    User Profile Summary:
    
    Personal Information:
    - Age: ${basicInfo.age || "Not specified"}
    - Height: ${heightDisplay}
    - Weight: ${weightDisplay}
    - Gender: ${basicInfo.gender || "Not specified"}
    - Location: ${city || "Not specified"}, ${country || "Not specified"}
    
    Fitness Goals:
    - Primary Goal: ${formatGoal(fitnessGoals.primary)}
    - Secondary Goal: ${formatGoal(fitnessGoals.secondary)}
    
    Workout Preferences:
    - Frequency: ${workoutPrefs.frequency || "Not specified"}
    - Duration: ${workoutPrefs.duration || "Not specified"}
    - Preferred Time: ${workoutPrefs.preferred_time || "Not specified"}
    
    Health Information:
    - Medical Conditions: ${healthInfo.medical_conditions || "None specified"}
    - Injuries: ${healthInfo.injuries || "None specified"}
  `.trim()
}

/**
 * Formats a fitness goal string by converting from kebab-case to Title Case
 * 
 * @param goal - The raw goal string from the form data
 * @returns Formatted goal string
 */
const formatGoal = (goal?: string): string => {
  if (!goal) return "Not specified"
  
  return goal
    .split("-")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")
}