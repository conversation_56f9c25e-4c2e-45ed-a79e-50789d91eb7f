'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function AdditionalInfo() {
  const [userType, setUserType] = useState<string | null>(null)
  const [certifications, setCertifications] = useState<string[]>([''])
  const [specializations, setSpecializations] = useState<string[]>([''])
  const [clientExperience, setClientExperience] = useState('')
  const router = useRouter()

  useEffect(() => {
    try {
      const storedUserType = window.localStorage.getItem('userType')
      setUserType(storedUserType)
    } catch (error) {
      console.error('Error accessing localStorage:', error)
    }
  }, [])

  const handleCertificationChange = (index: number, value: string) => {
    const newCertifications = [...certifications]
    newCertifications[index] = value
    setCertifications(newCertifications)
  }

  const handleSpecializationChange = (index: number, value: string) => {
    const newSpecializations = [...specializations]
    newSpecializations[index] = value
    setSpecializations(newSpecializations)
  }

  const addCertification = () => {
    setCertifications([...certifications, ''])
  }

  const addSpecialization = () => {
    setSpecializations([...specializations, ''])
  }

  const handleContinue = () => {
    try {
      window.localStorage.setItem('certifications', JSON.stringify(certifications.filter(Boolean)))
      window.localStorage.setItem('specializations', JSON.stringify(specializations.filter(Boolean)))
      window.localStorage.setItem('clientExperience', clientExperience)
      router.push('/auth/complete-profile/final-step')
    } catch (error) {
      console.error('Error saving to localStorage:', error)
    }
  }

  useEffect(() => {
    if (userType !== null && userType !== 'Fitness Coach/Trainer') {
      router.push('/auth/complete-profile/final-step')
    }
  }, [userType, router])

  if (userType !== 'Fitness Coach/Trainer') {
    return null
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Additional Information for Coaches/Trainers
          </h2>
        </div>
        <div className="mt-8 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700">Certifications</label>
            {certifications.map((cert, index) => (
              <input
                key={index}
                type="text"
                value={cert}
                onChange={(e) => handleCertificationChange(index, e.target.value)}
                className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder={`Certification ${index + 1}`}
              />
            ))}
            <button
              type="button"
              onClick={addCertification}
              className="mt-2 text-sm text-blue-600 hover:text-blue-500"
            >
              + Add another certification
            </button>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Specializations</label>
            {specializations.map((spec, index) => (
              <input
                key={index}
                type="text"
                value={spec}
                onChange={(e) => handleSpecializationChange(index, e.target.value)}
                className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder={`Specialization ${index + 1}`}
              />
            ))}
            <button
              type="button"
              onClick={addSpecialization}
              className="mt-2 text-sm text-blue-600 hover:text-blue-500"
            >
              + Add another specialization
            </button>
          </div>
          <div>
            <label htmlFor="clientExperience" className="block text-sm font-medium text-gray-700">
              Client Experience
            </label>
            <textarea
              id="clientExperience"
              value={clientExperience}
              onChange={(e) => setClientExperience(e.target.value)}
              rows={4}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Describe your experience working with clients"
            />
          </div>
        </div>
        <button
          onClick={handleContinue}
          className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Continue
        </button>
      </div>
    </div>
  )
}