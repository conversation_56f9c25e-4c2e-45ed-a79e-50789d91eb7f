import type { Metada<PERSON> } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { SiteHeader } from '@/components/site-header'
import Providers from '@/components/providers'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'IntelligentFitness - AI-Powered Workout Plans',
  description: 'Personalized workout plans powered by artificial intelligence',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={`${inter.className} bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 dark:bg-gray-900`}>
        <Providers>
          <div className="min-h-screen flex flex-col">
            <SiteHeader />
            {children}
          </div>
        </Providers>
      </body>
    </html>
  )
}