// types/tracking.ts

export interface ExerciseTrackingData {
  // Core Reference Identifiers
  sessionId: string;                // Unique identifier for this tracking session
  workoutReferenceId: string;       // Reference to the workout plan
  planType: string;                 // Type of plan (kick_starter, seven_day, etc.)
  userId: string;                   // User identifier
  
  // Exercise Metadata
  workoutDay: number;               // Day number in the program
  exerciseName: string;             // Name for display/search
  libraryId: string;                // Reference to exercise library
  exerciseType: 'warm_up' | 'resistance_training' | 'cardio_training' | 'cool_down'; // Type of exercise
  
  // Tracking Metrics
  weight?: number;                  // Weight used (kg/lbs)
  sets?: number;                    // Number of sets performed
  reps?: number[];                  // Array of reps per set for detailed tracking
  duration?: number;                // Duration in minutes (for cardio/warm-up/cool-down)
  completionStatus: 'complete' | 'partial' | 'skipped'; // Completion status
  setsCompleted?: number;           // How many sets actually completed
  
  // User Experience Metrics
  difficultyRating?: number;        // Scale 1-5
  energyLevel?: number;             // Scale 1-5 (user's energy level)
  notes?: string;                   // User observations
  
  // Temporal Data
  dateCompleted: string;            // ISO timestamp
  weekNumber: number;               // Week in program
  createdAt: string;                // When record was created
  updatedAt: string;                // When record was last modified
}

export interface WorkoutDayTracking {
  dayNumber: number;
  dayName: string;
  completionPercentage: number;
  exercisesTotal: number;
  exercisesCompleted: number;
  dateCompleted?: string;
  lastUpdated: string;
}

export interface WorkoutPlanTracking {
  workoutReferenceId: string;
  planType: string;
  userId: string;
  title: string;
  daysTotal: number;
  daysCompleted: number;
  overallCompletionPercentage: number;
  dayTracking: { [key: string]: WorkoutDayTracking };
  startDate: string;
  lastUpdated: string;
}
