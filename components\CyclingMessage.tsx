"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON><PERSON> } from "lucide-react"

export default function CyclingMessage() {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0)
  const cyclingMessages = [
    "Ready to start your personalized fitness journey?",
    "Our AI can create a custom workout plan based on your goals and preferences."
  ]

  // Effect to cycle through messages
  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentMessageIndex((prevIndex) =>
        prevIndex === cyclingMessages.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000) // Change message every 5 seconds

    return () => clearInterval(intervalId)
  }, [])

  return (
    <div className="mb-4">
      <div className="flex items-start space-x-3">
        <div className="bg-transparent border-4 border-amber-500 rounded-full p-2 flex-shrink-0">
          <Dumbbell className="h-5 w-5 text-amber-500" />
        </div>
        <div className="bg-transparent border-4 border-amber-500 rounded-3xl p-4 text-amber-500 w-[350px] h-[100px] flex items-center justify-center">
          <div className="w-full h-full flex items-center justify-center">
            <p className="text-left transition-opacity duration-500 ease-in-out">
              {cyclingMessages[currentMessageIndex]}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
