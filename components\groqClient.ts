// Enhanced groqClient.ts - Simplified without unused references to fallbacks
import type { Groq } from "groq-sdk";
import { createGroqClient } from "@/lib/llms/groq";
import {
  ChatCompletionTool,
  ChatCompletionToolChoiceOption,
  type ChatCompletionCreateParamsBase,
} from "groq-sdk/resources/chat/completions.mjs";

// Extend the base type to include reasoning_format
interface ExtendedChatCompletionCreateParams extends ChatCompletionCreateParamsBase {
  reasoning_format?: "raw" | "parsed" | "hidden";
}

// Define default models
const DEFAULT_GROQ_MODEL = process.env.GROQ_MODEL2 || "llama-3.1-70b-chat";
const STREAMING_GROQ_MODEL = process.env.STREAMING_GROQ_MODEL || "llama-3.1-70b-chat";
const MAX_TOKENS = 3500;
const DEFAULT_TEMP = 0.7;

// Type definitions
interface ChatCompletionMessage {
  role: "system" | "user" | "assistant" | "function";
  content: string;
  name?: string;
}

interface ChatCompletionRequest {
  messages: ChatCompletionMessage[];
  temperature?: number;
  modelName?: string;
  maxTokens?: number;
  reasoning_format?: "raw" | "parsed" | "hidden";
  tools?: ChatCompletionTool[] | null;
  toolChoice?: ChatCompletionToolChoiceOption | null;
}

interface ChatCompletionResponse {
  choices: Array<{
    message: {
      content: string | null;
      tool_calls?: Array<{
        id: string;
        type: string;
        function: {
          name: string;
          arguments: string;
        };
      }>;
    };
  }>;
}

interface StreamingChatCompletionRequest extends ChatCompletionRequest {
  stream?: boolean;
  onChunk?: (content: string) => Promise<void> | void;
}

// Store instances by user email
const groqInstances: Map<string, Groq> = new Map();

export function getGroqClient(config: { userEmail: string }): Groq {
  const { userEmail } = config;
  if (!groqInstances.has(userEmail)) {
    const client = createGroqClient({ userEmail });
    groqInstances.set(userEmail, client);
  }
  return groqInstances.get(userEmail)!;
}

export async function callGroqAi({
  messages,
  temperature = DEFAULT_TEMP,
  modelName = DEFAULT_GROQ_MODEL,
  maxTokens = MAX_TOKENS,
  tools,
  toolChoice,
}: ChatCompletionRequest): Promise<ChatCompletionResponse> {
  if (!messages?.length) {
    throw new Error("Messages array is required and must not be empty");
  }

  const userEmail = messages.find((msg) => msg.name)?.name;
  if (!userEmail) {
    throw new Error("User email not found in messages");
  }

  const groqInstance = getGroqClient({ userEmail });

  const request: ExtendedChatCompletionCreateParams = {
    messages: messages.map((message) => ({
      role: message.role,
      content: message.content,
      name: message.name ?? "defaultName",
    })),
    model: modelName,
    temperature,
    max_tokens: maxTokens,
    tools,
    tool_choice: toolChoice,
  };

  try {
    const response = await groqInstance.chat.completions.create(request);
    if (!('choices' in response) || !response.choices?.[0]?.message) {
      throw new Error("Invalid response from Groq API");
    }
    return response;
  } catch (error) {
    console.error("Groq API error:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    throw new Error(`Failed to get Groq completion: ${errorMessage}`);
  }
}

export async function callGroqAiEx({
  messages,
  stream = false,
  onChunk,
  temperature = DEFAULT_TEMP,
  modelName = STREAMING_GROQ_MODEL,
  maxTokens = MAX_TOKENS,
}: StreamingChatCompletionRequest): Promise<ChatCompletionResponse> {
  if (!messages?.length) {
    throw new Error("Messages array is required and must not be empty");
  }

  // Get API key with better error feedback
  const apiKey = process.env.NEXT_PUBLIC_GROQ_API_KEY || '********************************************************';
  if (!apiKey) {
    console.error("Missing Groq API key in environment variables");
    throw new Error("Configuration error: Groq API key is missing");
  }

  if (stream && !onChunk) {
    throw new Error("onChunk callback is required when stream is true");
  }

  // Add request parameters validation
  if (!modelName) {
    console.warn("No model specified, using default:", STREAMING_GROQ_MODEL);
    modelName = STREAMING_GROQ_MODEL;
  }

  const formattedMessages = messages.map((message) => ({
    role: message.role,
    content: message.content,
    name: message.name,
  }));

  // Add diagnostic info to help troubleshoot
  console.log(`Calling Groq API with model: ${modelName}, stream: ${stream}`);
  
  try {
    const url = "https://api.groq.com/openai/v1/chat/completions";
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: modelName,
        messages: formattedMessages,
        stream,
        temperature,
        max_tokens: maxTokens,
      }),
    });

    // Enhanced error handling with better diagnostics
    if (!response.ok) {
      const statusCode = response.status;
      let errorMessage = `HTTP error ${statusCode}`;
      
      try {
        // Try to get detailed error from JSON response
        const errorData = await response.json();
        errorMessage = errorData.error?.message || errorMessage;
        
        // Log the full error response for debugging
        console.error("Groq API error details:", JSON.stringify(errorData));
      } catch {
        // If response isn't valid JSON, use text instead
        try {
          const textContent = await response.text();
          errorMessage = textContent || errorMessage;
          console.error("Non-JSON error response:", textContent);
        } catch {
          console.error("Failed to parse error response as text");
        }
      }
      
      // Specific feedback for common status codes
      if (statusCode === 401) {
        throw new Error(`Groq API authentication failed: ${errorMessage}. Check your API key.`);
      } else if (statusCode === 429) {
        throw new Error(`Groq API rate limit exceeded: ${errorMessage}`);
      } else if (statusCode === 503) {
        throw new Error(`Groq API service unavailable: ${errorMessage}. The model ${modelName} may be at capacity.`);
      } else {
        throw new Error(`Groq API error: ${errorMessage}`);
      }
    }

    if (stream) {
      if (!response.body) {
        throw new Error("Response body is null or undefined");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let fullContent = "";

      // Improved stream processing with better error handling
      const processChunk = async ({ done, value }: ReadableStreamReadResult<Uint8Array>): Promise<void> => {
        if (done) {
          // Process any remaining data in buffer
          if (buffer.trim() && onChunk) {
            try {
              const jsonStr = buffer.replace(/^data: /, "").trim();
              if (jsonStr && jsonStr !== "[DONE]") {
                const json = JSON.parse(jsonStr);
                const content = json.choices[0]?.delta?.content || "";
                if (content) {
                  fullContent += content;
                  await onChunk(content);
                }
              }
            } catch (error) {
              console.error("Error parsing final chunk:", error, "Buffer:", buffer);
            }
          }
          return;
        }

        // Decode and process the incoming chunk
        const chunk = decoder.decode(value, { stream: true });
        buffer += chunk;
        
        // Split by data: prefix and process each line
        const lines = buffer.split("\n");
        buffer = lines.pop() || "";

        for (const line of lines) {
          if (!line.trim() || line === "data: [DONE]") continue;
          if (!line.startsWith("data: ")) continue;

          try {
            const jsonStr = line.replace(/^data: /, "").trim();
            if (!jsonStr) continue;

            const json = JSON.parse(jsonStr);
            const content = json.choices[0]?.delta?.content || "";
            if (content && onChunk) {
              fullContent += content;
              await onChunk(content);
            }
          } catch (error) {
            console.error("Error parsing chunk JSON:", error, "Line:", line);
          }
        }

        return reader.read().then(processChunk);
      };

      await reader.read().then(processChunk);
      return { choices: [{ message: { content: fullContent } }] };
    }

    return await response.json();
  } catch (error) {
    // Improved error logging with contextual information
    console.error("Groq API call failed:", error);
    
    if (error instanceof Error) {
      // Preserve the original error but add context
      throw new Error(`Groq API request failed: ${error.message}`);
    }
    
    throw new Error(`Groq API request failed: Unknown error`);
  }
}