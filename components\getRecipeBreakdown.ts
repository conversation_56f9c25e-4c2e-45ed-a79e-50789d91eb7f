// getRecipeBreakdown.tsx
import { processWithGroq } from "@/components/tools/groq-ai";

/**
 * Process recipe data using Groq AI to extract nutritional information
 * @param prompt - The prompt containing recipe data to analyze
 * @returns Object with processed recipe data and JSON structure
 */
export async function getRecipeBreakdown(prompt: string) {
  try {
    console.log('Processing recipe with Groq...');

    // Use processWithGroq to get the recipe breakdown
    const result = await processWithGroq({
      prompt,
      model: process.env.GROQ_MODEL || "llama-3.3-70b-versatile",
      modelOptions: {
        temperature: 0.2,
        maxTokens: 4000,
        // Request JSON output format
        response_format: { type: "json_object" }
      }
    });

    // Parse the result as JSON
    let jsonData;
    try {
      jsonData = JSON.parse(result);
      console.log('Successfully parsed recipe data as JSON');
    } catch (parseError) {
      console.error('Error parsing Groq response as JSON:', parseError);
      console.log('Raw response:', result);

      // Attempt to extract <PERSON><PERSON><PERSON> from the response if it contains markdown code blocks
      const jsonMatch = result.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        try {
          jsonData = JSON.parse(jsonMatch[1].trim());
          console.log('Successfully extracted and parsed JSON from markdown');
        } catch (extractError) {
          console.error('Failed to extract JSON from markdown:', extractError);
          throw new Error('Unable to parse recipe data as JSON');
        }
      } else {
        throw new Error('Unable to parse recipe data as JSON');
      }
    }

    // Return in the expected format
    return {
      json: jsonData,
      text: result
    };
  } catch (error) {
    console.error('Error in getRecipeBreakdown:', error);
    return null; // Handle error case by returning null
  }
}

