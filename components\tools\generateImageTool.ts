// src/components/tools/generateImageTool.ts

import OpenAI from 'openai';

interface GenerateImageResult {
  success: boolean;
  base64Image?: string;
  error?: string;
}

export class GenerateImageTool {
  private openai: OpenAI;

  constructor(openaiApiKey: string) {
    this.openai = new OpenAI({
      apiKey: openaiApiKey,
    });
  }

  /**
   * Refines a user’s initial prompt via a GPT model.
   */
  public async refinePrompt(initialPrompt: string): Promise<string> {
    try {
      const chatCompletion = await this.openai.chat.completions.create({
        model: 'gpt-4o', // Using GPT-4o for prompt refinement
        messages: [
          {
            role: 'system',
            content: `You are an expert in generating highly detailed, photorealistic image prompts. When a user provides a prompt, your task is to transform it into a single, exceptionally descriptive and precise prompt. Focus on the following elements to enhance the image’s realism and artistic quality:

Lighting and Atmosphere

Specify the type of lighting (e.g., soft natural light, dramatic backlight, golden-hour glow).
Include atmospheric details (e.g., mist, haze, crisp air).
Camera Angle and Perspective

Indicate viewpoint (e.g., low-angle shot, eye-level, aerial).
Determine focal length and depth of field (e.g., wide-angle lens, shallow depth of field).
Color Palette and Mood

Choose complementary or contrasting colors to establish a specific mood (e.g., warm and inviting, cool and mysterious).
Include descriptive adjectives for the palette (e.g., muted pastels, vibrant neons).
Textures and Materials

Describe the surfaces and materials in detail (e.g., weathered wood, glossy metal, soft fabric).
Highlight subtle elements like reflections or patterns.
Environmental Details

Enrich the scene with contextual elements (e.g., surrounding architecture, natural landscape, distant objects).
Ensure they support the main subject without overwhelming it.
Composition and Framing

Apply compositional guidelines (e.g., rule of thirds, centered framing, leading lines).
Clarify the placement of the subject and supporting elements.
How to Apply These Steps

Read the User’s Core Concept – Identify the subject or central idea.
Expand with Relevant Details – Incorporate each of the categories above to heighten realism.
Maintain Clarity – Combine all details into a single prompt, ensuring it flows naturally and concisely.
Avoid Extraneous Commentary – Present the final refined prompt without additional explanations or meta-notes.
Example of a Refined Prompt (Using These Steps)
“Capture a crisp, eye-level shot of a solitary figure standing on a secluded
shoreline at dawn, illuminated by soft, warm light filtering through a gentle
morning haze. Incorporate a shallow depth of field to keep the subject in sharp focus
while softly blurring the rolling waves and distant horizon. Emphasize natural textures—subtle ripples
in wet sand, the subdued sheen of the water, and the slight wrinkles in the figure’s light-colored
fabric. Use a serene color palette of soft blues and pale golds to evoke a peaceful, reflective mood.
Place the main subject slightly off-center, guided by the rule of thirds, to balance the wide,
open sky and gentle surf. Highlight realistic details such as delicate reflections on the water’s
surface and faint footprints trailing behind the figure, creating a professional,
photorealistic scene.”`
          },
          { role: 'user', content: initialPrompt }
        ],
        max_tokens: 2000,
        temperature: 0.7
      });

      // If no message, return original prompt to avoid throwing
      return chatCompletion.choices[0]?.message?.content || initialPrompt;
    } catch (error) {
      console.error('Prompt refinement error:', error);

      // Log detailed error information
      if (error instanceof Error) {
        console.error('Error details:', {
          message: error.message,
          name: error.name,
          stack: error.stack
        });
      }

      // Fallback: just return original prompt
      return initialPrompt;
    }
  }

  /**
   * Generates a base64-encoded image from a prompt and returns it.
   * @param prompt The text prompt to generate an image from
   * @param model The model to use for generation (dall-e-3, gpt-image-1, etc.)
   * @param size The size of the generated image
   * @param quality The quality level (low, medium, high, auto)
   * @param format The output format (png, jpeg, webp)
   * @param background The background type (transparent, opaque, auto)
   * @param compression The compression level (0-100) for JPEG and WebP formats
   */
  public async generateImageBase64(
    prompt: string,
    model = 'dall-e-3',
    size: string = '1024x1024',
    quality: string = 'auto',
    format: string = 'png',
    background: string = 'auto',
    compression?: number
  ): Promise<GenerateImageResult> {
    try {
      console.log(`[generateImageTool] Input model parameter: ${model}`);
      console.log(`[generateImageTool] Generating image with model: ${model}`);
      console.log(`[generateImageTool] Using size: ${size}`);
      console.log(`[generateImageTool] Using quality: ${quality}`);
      console.log(`[generateImageTool] Using format: ${format}`);
      console.log(`[generateImageTool] Using background: ${background}`);
      if (compression !== undefined) {
        console.log(`[generateImageTool] Using compression: ${compression}`);
      }

      // Configure parameters based on the selected model
      if (model === 'dall-e-3') {
        // DALL-E 3 specific parameters
        const dalleParams = {
          prompt,
          model: 'dall-e-3',
          n: 1,
          size: size as '1024x1024' | '1792x1024' | '1024x1792' | '512x512' | '256x256', // Type assertion for size
          response_format: 'b64_json' as const, // Using as const for literal types
          style: 'vivid' as const,
          quality: 'hd' as const
        };

        const response = await this.openai.images.generate(dalleParams);
        const b64Data = response.data?.[0]?.b64_json;

        if (b64Data) {
          console.log('Successfully generated image with dall-e-3 model');
          return { success: true, base64Image: b64Data };
        } else {
          throw new Error('No image data returned from dall-e-3');
        }
      }
      else if (model === 'gpt-image-1') {
        // GPT-Image-1 specific parameters

        // Create a base parameters object
        // Note: gpt-image-1 doesn't support response_format parameter
        const baseParams = {
          prompt,
          model: 'gpt-image-1',
          n: 1,
          size,
        };

        // Create the full parameters object with type assertion
        // We need to use type assertion because the OpenAI SDK doesn't fully support all parameters
        // that the API actually accepts
        const fullParams = {
          ...baseParams,
          quality,
          background,
          output_format: format,
          ...(compression !== undefined && (format === 'jpeg' || format === 'webp') ? { compression } : {})
        };

        // Use type assertion to bypass TypeScript's type checking for the OpenAI API
        const response = await this.openai.images.generate(fullParams as unknown as OpenAI.ImageGenerateParams);

        // Check if we have a URL or base64 data
        if (response.data?.[0]?.url) {
          console.log('Successfully generated image URL with gpt-image-1 model');
          // For URL responses, we return the URL as the base64Image
          // The calling code will handle both URL and base64 formats
          return { success: true, base64Image: response.data[0].url };
        } else if (response.data?.[0]?.b64_json) {
          console.log('Successfully generated base64 image with gpt-image-1 model');
          return { success: true, base64Image: response.data[0].b64_json };
        } else {
          throw new Error('No image data returned from gpt-image-1');
        }
      }
      else if (model === 'dall-e-2') {
        // DALL-E 2 specific parameters
        const dalle2Params = {
          prompt,
          model: 'dall-e-2',
          n: 1,
          size: size as '1024x1024' | '512x512' | '256x256', // Type assertion for size
          response_format: 'b64_json' as const // Using as const for literal types
        };

        const response = await this.openai.images.generate(dalle2Params);
        const b64Data = response.data?.[0]?.b64_json;

        if (b64Data) {
          console.log('Successfully generated image with dall-e-2 model');
          return { success: true, base64Image: b64Data };
        } else {
          throw new Error('No image data returned from dall-e-2');
        }
      }
      else if (model.includes('imagen')) {
        // For Imagen models, we should not use the OpenAI client
        // Instead, we should return an error indicating that Imagen models should be handled separately
        console.log('Imagen models should be handled by the Imagen-specific implementation');
        throw new Error('Imagen models cannot be used with the OpenAI client. Use the Imagen-specific implementation instead.');
      }
      else {
        // For any other model, use generic parameters and fall back if needed
        try {
          // Create base parameters
          const baseParams = {
            prompt,
            model: model,
            n: 1,
            size: size as '1024x1024' | '1792x1024' | '1024x1792' | '512x512' | '256x256' // Type assertion for size
          };

          // Create the full parameters object based on model support
          const fullParams = model === 'dall-e-2' || model === 'dall-e-3'
            ? { ...baseParams, response_format: 'b64_json' }
            : baseParams;

          // Use type assertion to bypass TypeScript's type checking for the OpenAI API
          const response = await this.openai.images.generate(fullParams as unknown as OpenAI.ImageGenerateParams);
          const b64Data = response.data?.[0]?.b64_json;

          if (b64Data) {
            console.log(`Successfully generated image with ${model} model`);
            return { success: true, base64Image: b64Data };
          } else {
            throw new Error(`No image data returned from ${model}`);
          }
        } catch (modelError) {
          // Log the error but don't automatically fall back to gpt-image-1
          console.error(`${model} generation failed:`, modelError);

          // Log detailed error information for debugging
          if (modelError instanceof Error) {
            console.error(`${model} Error Details:`, {
              name: modelError.name,
              message: modelError.message,
              stack: modelError.stack,
              status: (modelError as { status?: unknown })?.status,
              code: (modelError as { code?: unknown })?.code,
              type: (modelError as { type?: unknown })?.type,
              param: (modelError as { param?: unknown })?.param
            });
          }

          // Only fall back to gpt-image-1 if the model is not already gpt-image-1
          if (model !== 'gpt-image-1') {
            console.warn(`Falling back to gpt-image-1 as requested model ${model} failed`);

            try {
              // Create base parameters for gpt-image-1
              // Note: gpt-image-1 doesn't support response_format parameter
              const baseParams = {
                prompt,
                model: 'gpt-image-1',
                n: 1,
                size,
              };

              // Create the full parameters object with all gpt-image-1 specific parameters
              const fullParams = {
                ...baseParams,
                quality,
                background,
                output_format: format,
                ...(compression !== undefined && (format === 'jpeg' || format === 'webp') ? { compression } : {})
              };

              console.log('Attempting to generate image with gpt-image-1 fallback using parameters:', fullParams);

              // Use type assertion to bypass TypeScript's type checking for the OpenAI API
              const fallbackResponse = await this.openai.images.generate(fullParams as unknown as OpenAI.ImageGenerateParams);

              // Check if we have a URL or base64 data
              if (fallbackResponse.data?.[0]?.url) {
                console.log('Successfully generated image URL with gpt-image-1 fallback');
                // For URL responses, we return the URL as the base64Image
                return { success: true, base64Image: fallbackResponse.data[0].url };
              } else if (fallbackResponse.data?.[0]?.b64_json) {
                console.log('Successfully generated base64 image with gpt-image-1 fallback');
                return { success: true, base64Image: fallbackResponse.data[0].b64_json };
              } else {
                throw new Error('No image data returned from gpt-image-1 fallback');
              }
            } catch (fallbackError: unknown) {
              console.error('gpt-image-1 fallback failed:', fallbackError);
              const errorMessage = fallbackError instanceof Error ? fallbackError.message : 'Unknown error';
              throw new Error(`Fallback to gpt-image-1 failed: ${errorMessage}`);
            }
          } else {
            // If the model is already gpt-image-1 and it failed, don't try again
            throw new Error('gpt-image-1 model failed to generate an image');
          }
        }
      }
    } catch (error: unknown) {
      console.error('All image generation attempts failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        error: errorMessage || 'Image generation failed with all models',
      };
    }
  }
}
