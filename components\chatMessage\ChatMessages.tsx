// ChatMessages.tsx - Updated to use EnhancedMarkdownContent for message rendering
import { motion, AnimatePresence } from "framer-motion";
import { RefObject, useEffect, useState } from "react";
import EnhancedMarkdownContent from "./EnhancedMarkdownContent"; // Import the markdown renderer

interface ChatMessage {
  id?: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
}

interface ChatMessagesProps {
  chatMessages: ChatMessage[];
  error: string | null;
  isLoading: boolean;
  isStreaming: boolean;
  messagesEndRef: RefObject<HTMLDivElement | null>;
  onFollowUpClick?: (text: string) => void; // Added prop for follow-up clicks
}

export function ChatMessages({
  chatMessages,
  error,
  isLoading,
  isStreaming,
  messagesEndRef,
  onFollowUpClick, // Added parameter
}: ChatMessagesProps) {
  // Track if content is actually visible (not empty)
  const [contentVisible, setContentVisible] = useState<boolean>(false);
  // Track the last assistant message for transition purposes
  const [lastAssistantMessage, setLastAssistantMessage] = useState<ChatMessage | null>(null);
  
  // When messages change, check if the last assistant message has content
  useEffect(() => {
    // Find the most recent assistant message
    const assistantMessages = chatMessages.filter(msg => msg.role === "assistant");
    if (assistantMessages.length > 0) {
      const lastMsg = assistantMessages[assistantMessages.length - 1];
      setLastAssistantMessage(lastMsg);
      
      // If we're streaming and the message has visible content, update state
      if (isStreaming && lastMsg && lastMsg.content.trim().length > 0) {
        setContentVisible(true);
      }
    }
    
    // Reset contentVisible when we're not streaming
    if (!isStreaming) {
      setContentVisible(false);
    }
  }, [chatMessages, isStreaming]);
  
  // Determine if we should show the typing indicator
  // Show when loading but hide only after content is actually visible
  const showTypingIndicator = isLoading && (!isStreaming || !contentVisible);

  // Function to handle clicking on a message item
  const handleItemClick = (text: string) => {
    console.log("Item clicked:", text);
    // Call the onFollowUpClick handler if provided
    if (onFollowUpClick) {
      onFollowUpClick(text);
    }
  };

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="w-full p-3 bg-red-500/20 border border-red-500/50 text-white rounded-lg mb-4"
        >
          <p className="text-sm font-medium">Error: {error}</p>
        </motion.div>
      )}

      {chatMessages.length === 0 && (
        <div className="text-gray-400 text-sm text-center">
          Start a conversation with your AI Fitness Coach
        </div>
      )}

      {/* User messages */}
      {chatMessages.map((message, index) =>
        message.role === "user" ? (
          <motion.div
            key={message.id || `user-${index}`}
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="flex justify-end"
          >
            <div
              className="max-w-[80%] p-3 bg-gradient-to-r from-purple-600 to-green-600 text-white rounded-2xl rounded-tr-sm shadow-lg shadow-purple-500/20"
            >
              <p className="text-sm">{message.content}</p>
              <p className="text-xs text-gray-400 mt-1 text-right">{message.timestamp}</p>
            </div>
          </motion.div>
        ) : (
          // Only render non-empty assistant messages
          (message.content.trim().length > 0 || (isStreaming && message === lastAssistantMessage)) && (
            <motion.div
              key={message.id || `assistant-${index}`}
              initial={{ opacity: 0, y: 10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{ duration: 0.3, ease: "easeOut" }}
              className="flex justify-start"
            >
              <div
                className="max-w-[80%] p-3 bg-black/30 backdrop-blur-sm border border-white/10 text-white rounded-2xl rounded-tl-sm"
              >
                {/* Use EnhancedMarkdownContent for assistant messages */}
                {isStreaming && message === lastAssistantMessage && message.content.trim().length === 0 ? (
                  <div className="text-sm whitespace-pre-wrap min-h-[1.25rem]">
                    <span className="inline-block w-2 h-4 bg-white animate-pulse ml-1"></span>
                  </div>
                ) : (
                  <div className="text-sm">
                    <EnhancedMarkdownContent 
                      content={message.content} 
                      onItemClick={handleItemClick}
                    />
                  </div>
                )}
                <p className="text-xs text-gray-400 mt-1 text-right">{message.timestamp}</p>
              </div>
            </motion.div>
          )
        )
      )}

      {/* Typing indicator with AnimatePresence for smooth transitions */}
      <AnimatePresence>
        {showTypingIndicator && (
          <motion.div 
            key="typing-indicator"
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95, transition: { duration: 0.2 } }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="flex justify-start"
          >
            <div className="p-3 rounded-2xl rounded-tl-sm bg-black/30 backdrop-blur-sm border border-white/10 text-white shadow-lg">
              <div className="flex space-x-1">
                <span
                  className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
                  style={{ animationDelay: "0ms" }}
                ></span>
                <span
                  className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
                  style={{ animationDelay: "150ms" }}
                ></span>
                <span
                  className="w-2 h-2 bg-purple-500 rounded-full animate-bounce"
                  style={{ animationDelay: "300ms" }}
                ></span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      <div ref={messagesEndRef} />
    </div>
  );
}