// hooks/audio/useAudioController.ts

import { useRef, useState } from "react"
import { doc, updateDoc } from "firebase/firestore"
import { ref, deleteObject, uploadBytes } from "firebase/storage"
import { db, auth, storage } from "@/components/firebase/config"

interface AssessmentSummary {
  content: string;
  audioPath?: string;
  // Add other properties as needed
}

interface AudioControllerProps {
  userEmail: string;
  assessmentSummary: AssessmentSummary | null;
  onError: (error: string | null) => void;
  generateSummary: () => Promise<void>;
}

export const useAudioController = ({ 
  userEmail, 
  assessmentSummary, 
  onError,
  generateSummary 
}: AudioControllerProps) => {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isAudioPlaying, setIsAudioPlaying] = useState<boolean>(false);
  const [audioLoading, setAudioLoading] = useState<boolean>(false);

  const handlePlayAudio = async (): Promise<void> => {
    if (!assessmentSummary) {
      onError("No summary available to play.");
      return;
    }

    const currentUser = auth.currentUser;
    if (!currentUser?.email) {
      onError("User not authenticated.");
      return;
    }

    setAudioLoading(true);

    try {
      const idToken = await currentUser.getIdToken();
      const ttsResponse = await fetch("/api/summaryToTTS", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify({ userEmail: currentUser.email }),
      });

      if (!ttsResponse.ok) {
        const errorData = await ttsResponse.json();
        throw new Error(errorData.error || "Failed to generate audio");
      }

      if (audioRef.current && ttsResponse.body) {
        const mediaSource = new MediaSource();
        audioRef.current.src = URL.createObjectURL(mediaSource);

        mediaSource.addEventListener("sourceopen", async () => {
          const sourceBuffer = mediaSource.addSourceBuffer("audio/mpeg");
          const reader = ttsResponse.body!.getReader();

          const pump = () => {
            reader.read().then(({ done, value }) => {
              if (done) {
                mediaSource.endOfStream();
                return;
              }
              sourceBuffer.appendBuffer(value!);
              pump();
            });
          };

          pump();
        });

        await audioRef.current.play();
        setIsAudioPlaying(true);
      }
    } catch (audioError) {
      console.error("Audio generation error:", audioError);
      onError(audioError instanceof Error ? audioError.message : "Failed to generate/play audio");
    } finally {
      setAudioLoading(false);
    }
  };

  const handlePauseAudio = (): void => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsAudioPlaying(false);
    }
  };

  const handleRegenerateSummary = async (): Promise<void> => {
    onError(null);
    setAudioLoading(true);

    try {
      const assessmentRef = doc(db, "IF_users", userEmail, "Profile", "assessment");
      await updateDoc(assessmentRef, { audioPath: "" });

      const audioPath = assessmentSummary?.audioPath;
      if (audioPath) {
        const storageRef = ref(storage, audioPath);
        try {
          await deleteObject(storageRef);
        } catch (deleteError) {
          console.error("Error deleting audio file:", deleteError);
        }
      }

      await generateSummary();

      const currentUser = auth.currentUser;
      if (!currentUser?.email) throw new Error("User not authenticated.");

      const idToken = await currentUser.getIdToken();
      const ttsResponse = await fetch("/api/summaryToTTS", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify({ userEmail: currentUser.email }),
      });

      if (!ttsResponse.ok) {
        const errorData = await ttsResponse.json();
        throw new Error(errorData.error || "Failed to generate new audio");
      }

      const audioBuffer = await bufferStream(ttsResponse.body!);
      const fileName = `summary_${new Date().toISOString()}.mp3`;
      const newStorageRef = ref(storage, `audio/${userEmail}/fitness/${fileName}`);
      await uploadBytes(newStorageRef, audioBuffer);

      await updateDoc(assessmentRef, {
        audioPath: `audio/${userEmail}/fitness/${fileName}`,
      });
    } catch (error) {
      console.error("Error in regenerate summary process:", error);
      onError(error instanceof Error ? error.message : "Failed to regenerate AI assessment");
    } finally {
      setAudioLoading(false);
    }
  };

  const bufferStream = async (stream: ReadableStream<Uint8Array>): Promise<Uint8Array> => {
    const reader = stream.getReader();
    const chunks: Uint8Array[] = [];

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      if (value) chunks.push(value);
    }

    let totalLength = 0;
    for (const chunk of chunks) {
      totalLength += chunk.length;
    }

    const result = new Uint8Array(totalLength);
    let offset = 0;
    for (const chunk of chunks) {
      result.set(chunk, offset);
      offset += chunk.length;
    }

    return result;
  };

  return {
    audioRef,
    isAudioPlaying,
    audioLoading,
    handlePlayAudio,
    handlePauseAudio,
    handleRegenerateSummary,
    bufferStream
  };
};