"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import {
  <PERSON><PERSON><PERSON>,
  Target,
  Calendar,
  Trophy,
  BarChart3,
  Smartphone,
  MessageCircle,
  ChevronRight,
  Zap,
  Users,
  Mic,
} from "lucide-react"
import {
  getProfileStatus,
  checkWorkoutPlanExists,
  getSelectedPlanName,
  checkWorkoutPlanDocumentExists,
  getUserProfile,
} from "@/utils/firebase"
import { auth } from "@/components/firebase/config"
import { onAuthStateChanged } from "firebase/auth"
import Footer from "@/components/Footer"
import { useWorkoutGeneration } from "@/components/WorkoutGenerationContext"
import { WorkoutGenerationOverlay } from "@/components/WorkoutGenerationOverlay"
import ChatBot from "@/components/ChatBot"
import ElevenLabsVoiceAssistant from "@/components/ElevenLabsVoiceAssistant"
import CyclingMessage from "@/components/CyclingMessage"

interface ProfileStatus {
  HasPdf: boolean
  audioPath: string
  error: string | null
  lastError: string
  lastPdfGenerated: string
  lastUpdated: string
  pdfPath: string
  status: string
  summary: string
}

type WorkoutPlanType = "kick_starter" | "shortTerm" | "longTerm" | "schedule"

export default function Home() {
  const [userStatus, setUserStatus] = useState({
    hasCompletedAssessment: false,
    hasWorkoutPlan: false,
    hasWorkoutPlanDocument: false,
    planType: null as WorkoutPlanType | null,
  })
  const [loading, setLoading] = useState(true)
  const { generationState, startGeneration } = useWorkoutGeneration()
  const [showChat, setShowChat] = useState(false)
  const [showVoiceAssistant, setShowVoiceAssistant] = useState(false)
  const [activeFeature, setActiveFeature] = useState(0)

  // Feature data
  const features = [
    {
      id: 1,
      title: "Personalized Workout Plans",
      description:
        "Stop guessing and start seeing results! IntelligentFitness.ai crafts dynamic workout plans based on your fitness level, goals, available equipment, and even your preferred workout style.",
      highlight: "Personalized and Adaptive Training for Optimal Results",
      icon: Dumbbell,
      color: "from-blue-500 to-indigo-600",
    },
    {
      id: 2,
      title: "Smart Exercise Guidance",
      description:
        "Our AI doesn't just give you exercises; it helps you do them right. Access detailed instructions, video demonstrations, and track your progress effortlessly.",
      highlight: "Expert Guidance and Progress Monitoring at Your Fingertips",
      icon: Target,
      color: "from-green-500 to-emerald-600",
    },
    {
      id: 3,
      title: "Flexible Scheduling",
      description:
        "Life gets busy, but your fitness doesn't have to suffer. IntelligentFitness.ai offers flexible scheduling options and seamlessly integrates with your calendar.",
      highlight: "Fitness That Fits Your Life, Not the Other Way Around",
      icon: Calendar,
      color: "from-purple-500 to-violet-600",
    },
    {
      id: 4,
      title: "Goal-Oriented Programs",
      description:
        "Whether you want to build strength, lose weight, improve endurance, or achieve a specific fitness milestone, we offer structured programs and engaging challenges.",
      highlight: "Achieve Your Fitness Dreams with Targeted Programs",
      icon: Trophy,
      color: "from-amber-500 to-orange-600",
    },
    {
      id: 5,
      title: "Data-Driven Insights",
      description:
        "Go beyond just tracking – understand your fitness journey. IntelligentFitness.ai provides insightful reports and analytics on your progress.",
      highlight: "Unlock the Power of Your Fitness Data",
      icon: BarChart3,
      color: "from-cyan-500 to-blue-600",
    },
    {
      id: 6,
      title: "Access Anywhere",
      description:
        "Your personalized fitness platform is always with you. Access your workouts, track your progress, and stay motivated from any device.",
      highlight: "Your Personal Trainer in Your Pocket, 24/7",
      icon: Smartphone,
      color: "from-red-500 to-pink-600",
    },
  ]

  // Rotate through features automatically
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % features.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [features.length])

  // Firebase auth and user data
  useEffect(() => {
    let unsubscribeAuth: () => void = () => {}

    const fetchUserData = async (userEmail: string | null) => {
      if (!userEmail) {
        setLoading(false)
        return
      }

      try {
        const profileStatus = await getProfileStatus(userEmail)
        if (profileStatus) {
          const assessment = profileStatus as unknown as ProfileStatus
          const hasCompletedAssessment = assessment.status === "completed"

          if (hasCompletedAssessment) {
            const [hasWorkoutPlan, workoutPlanDocExists, planTypeString] = await Promise.all([
              checkWorkoutPlanExists(userEmail),
              checkWorkoutPlanDocumentExists(userEmail),
              getSelectedPlanName(userEmail),
            ])

            const planType = (["kick_starter", "shortTerm", "longTerm", "schedule"] as const).includes(
              planTypeString as WorkoutPlanType,
            )
              ? (planTypeString as WorkoutPlanType)
              : null

            setUserStatus({
              hasCompletedAssessment: true,
              hasWorkoutPlan,
              hasWorkoutPlanDocument: workoutPlanDocExists,
              planType,
            })
          } else {
            setUserStatus({
              hasCompletedAssessment: false,
              hasWorkoutPlan: false,
              hasWorkoutPlanDocument: false,
              planType: null,
            })
          }
        } else {
          setUserStatus({
            hasCompletedAssessment: false,
            hasWorkoutPlan: false,
            hasWorkoutPlanDocument: false,
            planType: null,
          })
        }
      } catch (error) {
        console.error("Error fetching user data:", error)
        setUserStatus({
          hasCompletedAssessment: false,
          hasWorkoutPlan: false,
          hasWorkoutPlanDocument: false,
          planType: null,
        })
      } finally {
        setLoading(false)
      }
    }

    unsubscribeAuth = onAuthStateChanged(auth, (user) => {
      const email = user?.email ?? null
      fetchUserData(email)
    })

    return () => unsubscribeAuth()
  }, [])

  const handleGenerateWorkoutPlan = async () => {
    try {
      const user = auth.currentUser
      if (!user || !user.email) {
        console.error("User not logged in")
        return
      }

      // Get the user's profile data
      const profileData = await getUserProfile(user.email)
      if (!profileData) {
        console.error("No profile data found")
        return
      }

      // Prepare profile text
      const profileText = JSON.stringify(profileData)

      // Start the generation process with the context
      await startGeneration(profileText)
    } catch (error) {
      console.error("Error initiating workout plan generation:", error)
    }
  }


  // Determine which CTA to show based on user status
  let linkComponent = null

  if (loading) {
    linkComponent = (
      <div className="block w-full max-w-md mx-auto text-center text-white">
        <div className="animate-pulse bg-transparent h-12 rounded-lg"></div>
      </div>
    )
  } else if (!userStatus.hasCompletedAssessment) {
    // Case 1: User hasn't completed assessment
    linkComponent = (
      <Link
        href="/profile/complete?step=basic-info"
        className="block w-full max-w-md rounded-3xl bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-6 transition-all duration-300 transform hover:-translate-y-1 shadow-lg"
      >
        Free AI Assessment
      </Link>
    )
  } else if (userStatus.hasCompletedAssessment && !userStatus.hasWorkoutPlanDocument) {
    // Case 2: User has completed assessment but needs to generate a workout plan
    linkComponent = (
      <button
        onClick={handleGenerateWorkoutPlan}
        disabled={generationState !== "idle"}
        className={`block w-full max-w-md rounded-3xl bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-3 px-6 transition-all duration-300 transform hover:-translate-y-1 shadow-lg ${
          generationState !== "idle" ? "opacity-50 cursor-not-allowed" : ""
        }`}
      >
        Generate Workout Plan
      </button>
    )
  } else if (userStatus.hasCompletedAssessment && userStatus.hasWorkoutPlanDocument && !userStatus.hasWorkoutPlan) {
    // Case 3: User has completed assessment and has the workoutplan document but no specific plan selected
    linkComponent = (
      <Link
        href="/workout-selector"
        className="block w-full max-w-md rounded-3xl bg-gradient-to-r from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700 text-white font-semibold py-3 px-6 transition-all duration-300 transform hover:-translate-y-1 shadow-lg"
      >
        Select Workout Plan
      </Link>
    )
  } else {
    // Case 4: User has completed assessment and has a selected workout plan
    const planNameMap: Record<WorkoutPlanType, string> = {
      kick_starter: "Kick Starter Plan",
      shortTerm: "4 Week Workout",
      longTerm: "Long-Term Plan",
      schedule: "7-Day Plan",
    }
    const planName = userStatus.planType ? planNameMap[userStatus.planType] : "Workout Plan"
    linkComponent = (
      <div className="flex flex-col text-xs sm:flex-row w-full max-w-md space-y-4 sm:space-y-0 sm:space-x-4">
        <Link
          href="/profile-summary"
          className="block w-full rounded-3xl bg-transparent border-4 border-amber-500 text-amber-500 text-xl font-bold py-3 px-6 hover:border-amber-600 hover:text-amber-600 transition duration-300 ease-in-out transform hover:-translate-y-1"
        >
          Review Assessment
        </Link>
        <Link
          href="/workout-dashboard"
          className="block w-full rounded-3xl bg-transparent border-4 border-green-500 text-green-500 text-xl font-bold py-3 px-6 hover:border-green-600 hover:text-green-600 transition duration-300 ease-in-out transform hover:-translate-y-1"
        >
          View {planName}
        </Link>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col">
      {/* Navigation */}
      {/* <header
        className={`sticky top-0 z-50 transition-all duration-300 bg-transparent`}
      >
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Link href="/" className="text-white font-bold text-2xl flex items-center">
                <Image
                  src="/placeholder.svg?height=40&width=40"
                  alt="IntelligentFitness Logo"
                  width={40}
                  height={40}
                  className="mr-2"
                />
                IntelligentFitness
              </Link>
            </div>
            <nav className="hidden md:flex space-x-8">
              <Link href="#features" className="text-gray-300 hover:text-white transition-colors">
                Features
              </Link>
              <Link href="#community" className="text-gray-300 hover:text-white transition-colors">
                Community
              </Link>
              <Link href="#rewards" className="text-gray-300 hover:text-white transition-colors">
                Rewards
              </Link>
            </nav>
            <div className="flex items-center space-x-4">
              <Link
                href="/dashboard"
                className="hidden md:block bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:-translate-y-1"
              >
                Dashboard
              </Link>
              <button className="md:hidden text-white">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </header> */}

      <main className="flex-grow">
        {/* Hero Section */}
        <section className="relative">
          <Image
            src="/bg-websiteProfile.png?height=1080&width=1920"
            alt="Fitness background"
            width={1920}
            height={1080}
            className="absolute inset-0 w-full h-full object-cover"
            priority
          />
          <div className="relative z-10 bg-black bg-opacity-50 py-20 md:py-32">
            <div className="container mx-auto px-4 flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-10 md:mb-0 text-center md:text-left">
                <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white leading-tight animate-fade-in">
                  Your AI-Powered <span className="text-blue-400">Fitness Journey</span>
                </h1>
                <p className="text-xl text-gray-300 mb-8 max-w-lg animate-fade-in delay-200">
                  Personalized workout plans, expert guidance, and a supportive community to help you achieve your
                  fitness goals.
                </p>
                <div className="animate-fade-in delay-300 md:text-left">{linkComponent}</div>
              </div>
              <div className="md:w-1/2 flex justify-center">
                <div className="relative w-full max-w-md">

                  <div className="relative bg-transparent rounded-2xl p-6">
                    <div className="flex flex-col h-full justify-left">
                      <CyclingMessage />

                      <div className="flex flex-col mt-8 animate-fade-in delay-500">
                        <div className="flex items-center justify-center mb-4">
                          <h3 className="text-xl ml-12 font-semibold text-white">Contact Jayden Ai for more info</h3>
                        </div>

                        <div className="flex items-start space-x-3">
                          <div className="bg-transparent border-4 border-white rounded-full p-2 flex-shrink-0">
                            <MessageCircle className="h-5 w-5 text-white" />
                          </div>
                          <div className="bg-transparent border-4 border-white rounded-3xl p-6 text-white w-[350px] flex flex-col items-center justify-center">
                            <div className="flex justify-center space-x-3">
                              <button
                                onClick={() => setShowChat(true)}
                                className="flex items-center justify-center space-x-2 bg-gradient-to-r from-slate-700 to-emerald-700  text-white px-6 py-3 rounded-3xl hover:from-slate-800 hover:to-slate-900 transition-all duration-300 shadow-lg"
                              >
                                <MessageCircle className="h-5 w-5" />
                                <span>Chat Now</span>
                              </button>
                              <button
                                onClick={() => setShowVoiceAssistant(true)}
                                className="flex items-center justify-center space-x-2 bg-gradient-to-r from-emerald-700 to-slate-700  text-white px-6 py-3 rounded-3xl hover:from-slate-800 hover:to-slate-900 first-line:transition-all duration-300 shadow-lg"

                              >
                                <Mic className="h-5 w-5" />
                                <span>Speak</span>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Intelligent Features</h2>
              <p className="text-xl text-gray-400 max-w-2xl mx-auto">
                Our AI-powered platform offers everything you need to achieve your fitness goals
              </p>
            </div>

            <div className="grid md:grid-cols-2 gap-8 mb-16">
              <div className="bg-slate-800 rounded-2xl p-6 md:p-8 shadow-xl">
                <div className="flex flex-col h-full">
                  <div className="mb-6">
                    <div className="flex items-center space-x-4 mb-4">
                      {features.map((feature, index) => (
                        <button
                          key={feature.id}
                          onClick={() => setActiveFeature(index)}
                          className={`w-3 h-3 rounded-full transition-all duration-300 ${
                            activeFeature === index ? `bg-gradient-to-r ${feature.color} w-6` : "bg-gray-600"
                          }`}
                          aria-label={`View ${feature.title}`}
                        />
                      ))}
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-2">{features[activeFeature].title}</h3>
                    <p className="text-purple-400 font-medium mb-4">{features[activeFeature].highlight}</p>
                    <p className="text-gray-300 mb-6">{features[activeFeature].description}</p>
                  </div>
                  <div className="mt-auto">
                    <Link
                      href={`/features/${features[activeFeature].id}`}
                      className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      Learn more <ChevronRight className="h-4 w-4 ml-1" />
                    </Link>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-blue-500 rounded-2xl blur opacity-30"></div>
                <div className="relative bg-slate-800 rounded-2xl overflow-hidden h-full">
                  <div className="absolute inset-0 flex items-center justify-center">
                    {activeFeature === 0 && <Dumbbell className="h-32 w-32 text-gray-700 opacity-20" />}
                    {activeFeature === 1 && <Target className="h-32 w-32 text-gray-700 opacity-20" />}
                    {activeFeature === 2 && <Calendar className="h-32 w-32 text-gray-700 opacity-20" />}
                    {activeFeature === 3 && <Trophy className="h-32 w-32 text-gray-700 opacity-20" />}
                    {activeFeature === 4 && <BarChart3 className="h-32 w-32 text-gray-700 opacity-20" />}
                    {activeFeature === 5 && <Smartphone className="h-32 w-32 text-gray-700 opacity-20" />}
                  </div>
                  <div className="relative z-10 p-6 md:p-8 h-full flex flex-col">
                    <div className="bg-gradient-to-r from-slate-900/80 to-slate-900/40 backdrop-blur-sm rounded-xl p-6 mb-6">
                      {activeFeature === 0 && (
                        <Dumbbell
                          className={`h-12 w-12 mb-4 bg-gradient-to-r ${features[activeFeature].color} bg-clip-text text-transparent`}
                        />
                      )}
                      {activeFeature === 1 && (
                        <Target
                          className={`h-12 w-12 mb-4 bg-gradient-to-r ${features[activeFeature].color} bg-clip-text text-transparent`}
                        />
                      )}
                      {activeFeature === 2 && (
                        <Calendar
                          className={`h-12 w-12 mb-4 bg-gradient-to-r ${features[activeFeature].color} bg-clip-text text-transparent`}
                        />
                      )}
                      {activeFeature === 3 && (
                        <Trophy
                          className={`h-12 w-12 mb-4 bg-gradient-to-r ${features[activeFeature].color} bg-clip-text text-transparent`}
                        />
                      )}
                      {activeFeature === 4 && (
                        <BarChart3
                          className={`h-12 w-12 mb-4 bg-gradient-to-r ${features[activeFeature].color} bg-clip-text text-transparent`}
                        />
                      )}
                      {activeFeature === 5 && (
                        <Smartphone
                          className={`h-12 w-12 mb-4 bg-gradient-to-r ${features[activeFeature].color} bg-clip-text text-transparent`}
                        />
                      )}
                      <h4 className="text-xl font-semibold text-white mb-2">Why it matters</h4>
                      <p className="text-gray-300">
                        {activeFeature === 0 &&
                          "Personalized plans lead to 64% better results than generic programs, adapting to your progress in real-time."}
                        {activeFeature === 1 &&
                          "Proper form reduces injury risk by 72% and increases exercise effectiveness by up to 35%."}
                        {activeFeature === 2 &&
                          "Flexible scheduling increases workout consistency by 47%, making it easier to maintain your routine."}
                        {activeFeature === 3 &&
                          "Goal-oriented programs increase motivation by 78% and make you 3x more likely to achieve your targets."}
                        {activeFeature === 4 &&
                          "Data-driven insights help you optimize your training, leading to 52% faster progress toward your goals."}
                        {activeFeature === 5 &&
                          "24/7 access means you're 89% more likely to stick with your fitness program long-term."}
                      </p>
                    </div>
                    <div className="mt-auto">
                      <div className="flex items-center justify-between">
                        <button
                          onClick={() => setActiveFeature((activeFeature - 1 + features.length) % features.length)}
                          className="p-2 rounded-full bg-slate-700 hover:bg-slate-600 transition-colors"
                          aria-label="Previous feature"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-white"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                        <div className="text-gray-400">
                          {activeFeature + 1} of {features.length}
                        </div>
                        <button
                          onClick={() => setActiveFeature((activeFeature + 1) % features.length)}
                          className="p-2 rounded-full bg-slate-700 hover:bg-slate-600 transition-colors"
                          aria-label="Next feature"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-white"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid md:grid-cols-3 gap-6">
              {features.map((feature) => (
                <div
                  key={feature.id}
                  className="bg-slate-800 rounded-xl p-6 hover:bg-slate-750 transition-colors group"
                >
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${feature.color} mb-4 inline-block`}>
                    <feature.icon className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                  <p className="text-gray-400 mb-4">{feature.description}</p>
                  <Link
                    href={`/features/${feature.id}`}
                    className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    Learn more{" "}
                    <ChevronRight className="h-4 w-4 ml-1 transform group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Original Three Features Section */}
        <section className="py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  icon: Zap,
                  title: "AI Workout Plans",
                  description: "Personalized routines tailored to your goals and fitness level",
                },
                {
                  icon: Target,
                  title: "Goal Tracking",
                  description: "Track your progress and stay motivated with detailed analytics",
                },
                {
                  icon: Users,
                  title: "Community",
                  description: "Connect with like-minded fitness enthusiasts and share your journey",
                },
              ].map((feature, index) => (
                <div key={index} className="text-center">
                  <feature.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2 text-white">{feature.title}</h3>
                  <p className="text-gray-300">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="py-20 bg-gradient-to-b from-slate-900 to-slate-800">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Success Stories</h2>
              <p className="text-xl text-gray-400 max-w-2xl mx-auto">
                See how IntelligentFitness.ai has transformed the lives of our users
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {[
                {
                  name: "Sarah J.",
                  role: "Marathon Runner",
                  quote:
                    "The personalized training plan helped me shave 12 minutes off my marathon time in just 3 months!",
                  image: "/placeholder.svg?height=100&width=100",
                },
                {
                  name: "Michael T.",
                  role: "Busy Professional",
                  quote:
                    "With flexible scheduling, I finally found a fitness routine that fits my unpredictable work schedule.",
                  image: "/placeholder.svg?height=100&width=100",
                },
                {
                  name: "Elena R.",
                  role: "Fitness Beginner",
                  quote:
                    "The guidance and form corrections helped me build confidence in the gym. I've lost 25 pounds in 6 months!",
                  image: "/placeholder.svg?height=100&width=100",
                },
              ].map((testimonial, index) => (
                <div key={index} className="bg-slate-800 rounded-xl p-6 shadow-xl">
                  <div className="flex items-center mb-4">
                    <Image
                      src={testimonial.image || "/placeholder.svg"}
                      alt={testimonial.name}
                      width={60}
                      height={60}
                      className="rounded-full mr-4"
                    />
                    <div>
                      <h3 className="text-lg font-semibold text-white">{testimonial.name}</h3>
                      <p className="text-purple-400">{testimonial.role}</p>
                    </div>
                  </div>
                  <p className="text-gray-300 italic">&quot;{testimonial.quote}&quot;</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section id="community" className="py-20 bg-gradient-to-r from-purple-900 to-blue-900">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">Ready to Transform Your Fitness Journey?</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join thousands of users who have already achieved their fitness goals with IntelligentFitness.ai
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Link
                href="/profile/complete?step=basic-info"
                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:-translate-y-1 text-center"
              >
                Start Free Assessment
              </Link>
              <button
                onClick={() => setShowVoiceAssistant(true)}
                className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-purple-900 font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-2"
              >
                <MessageCircle className="h-5 w-5" />
                <span>Speak to Us</span>
              </button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
      <WorkoutGenerationOverlay />
      <ChatBot isOpen={showChat} onClose={() => setShowChat(false)} />
      <ElevenLabsVoiceAssistant isOpen={showVoiceAssistant} onClose={() => setShowVoiceAssistant(false)} />
    </div>
  )
}
