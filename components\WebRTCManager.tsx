"use client"
import { useEffect, useRef, useState } from "react"
// Import auth from firebase config
import { auth } from "@/components/firebase/config"
// Type alias for AudioContext constructor
type AudioContextConstructor = typeof AudioContext


interface OpenAISessionResponse {
  client_secret?: {
    value: string
  }
  error?: {
    message?: string
  }
  message?: string
  [key: string]: unknown
}

export function useWebRTCManager() {
  const [connecting, setConnecting] = useState<boolean>(false)
  const [connected, setConnected] = useState<boolean>(false)
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false)
  const [isListening, setIsListening] = useState<boolean>(false)
  const [micActive, setMicActive] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  const peerConnectionRef = useRef<RTCPeerConnection | null>(null)
  const dataChannelRef = useRef<RTCDataChannel | null>(null)
  const mediaStreamRef = useRef<MediaStream | null>(null)
  const audioContextRef = useRef<AudioContext | null>(null)
  const isMounted = useRef<boolean>(true)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    isMounted.current = true
    return () => {
      isMounted.current = false
      endRealtimeConnection()
    }
  }, [])

  const initializeRealtimeConnection = async (assessmentSummary: string): Promise<void> => {
    if (!assessmentSummary) {
      setError("No assessment data available for your Personal Coach")
      return
    }

    setConnecting(true)
    setError(null)
    setConnected(false)

    endRealtimeConnection()

    try {
      const currentUser = auth?.currentUser
      let idToken = ""

      if (currentUser) {
        try {
          idToken = await currentUser.getIdToken()
        } catch (authError) {
          console.error("Auth error, continuing without token:", authError)
        }
      }

      const headers: Record<string, string> = {
        Pragma: "no-cache",
        "Cache-Control": "no-cache",
      }

      if (idToken) headers.Authorization = `Bearer ${idToken}`

      const response = await fetch("/api/realtime-session", {
        method: "GET",
        headers,
      })

      if (!response.ok) {
        let errorMessage = `Server error: ${response.status}`
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorMessage
        } catch {
          const errorText = await response.text()
          errorMessage = errorText || errorMessage
        }
        throw new Error(errorMessage)
      }

      const data = await response.json()
      const ephemeralKey = data.ephemeral_key
      if (!ephemeralKey) {
        throw new Error("Invalid session data received: missing ephemeral key")
      }

      await setupWebRTC(ephemeralKey, assessmentSummary)
    } catch (err) {
      console.error("Connection initialization error:", err)
      const errorMessage = err instanceof Error ? err.message : "Failed to initialize your Personal Coach"
      setError(errorMessage)
      setConnecting(false)
      setConnected(false)
    }
  }

  const setupWebRTC = async (ephemeralKey: string, assessmentSummary: string): Promise<void> => {
    try {
      const peerConnection = new RTCPeerConnection({
        iceServers: [
          {
            urls: ["stun:stun1.l.google.com:19302", "stun:stun2.l.google.com:19302", "stun:stun.l.google.com:19302"],
          },
        ],
        iceCandidatePoolSize: 10,
      })
      peerConnectionRef.current = peerConnection

      setupPeerConnectionEventLogging(peerConnection)

      peerConnection.oniceconnectionstatechange = () => {
        const state = peerConnection.iceConnectionState
        if (!isMounted.current) return

        if (state === "connected" || state === "completed") {
          if (dataChannelRef.current?.readyState === "open") {
            setConnected(true)
            setConnecting(false)
            setError(null)
          }
        } else if (state === "disconnected") {
          setError("Connection unstable - attempting to recover")
        } else if (state === "failed") {
          setError("Connection to your Personal Trainer failed. Please try again.")
          setConnected(false)
          setConnecting(false)
        } else if (state === "closed") {
          setConnected(false)
          setConnecting(false)
        }
      }

      const dataChannel = peerConnection.createDataChannel("openai-data", {
        ordered: true,
        maxRetransmits: 3,
      })
      dataChannelRef.current = dataChannel

      await setupAudioProcessing()

      configureDataChannel(dataChannel, assessmentSummary)

      peerConnection.ontrack = (event) => {
        if (!isMounted.current || !event.streams[0]) return
        const stream = event.streams[0]
        if (audioRef.current) {
          audioRef.current.srcObject = stream
          audioRef.current.play().catch((playError) => {
            console.error("Audio play error:", playError)
            if (playError.name === "NotAllowedError") {
              setError("Audio playback blocked. Please interact with the page to enable audio.")
            }
          })
        }
      }

      const offer = await peerConnection.createOffer({
        offerToReceiveAudio: true,
        offerToReceiveVideo: false,
      })

      await peerConnection.setLocalDescription(offer)
      await waitForIceGathering(peerConnection)

      if (!peerConnection.localDescription?.sdp) {
        throw new Error("Failed to create session description")
      }

      const sdpResponse = await fetch("https://api.openai.com/v1/realtime", {
        method: "POST",
        body: peerConnection.localDescription.sdp,
        headers: {
          Authorization: `Bearer ${ephemeralKey}`,
          "Content-Type": "application/sdp",
        },
      })

      if (!sdpResponse.ok) {
        const errorText = await sdpResponse.text()
        let errorMessage = `SDP exchange failed with status: ${sdpResponse.status}`
        try {
          const errorJson = JSON.parse(errorText) as OpenAISessionResponse
          errorMessage = `SDP exchange failed: ${errorJson.error?.message || errorJson.error || errorJson.message || sdpResponse.status}`
        } catch {
          if (errorText) errorMessage = `SDP exchange failed: ${errorText}`
        }
        throw new Error(errorMessage)
      }

      const remoteSdp = await sdpResponse.text()
      await peerConnection.setRemoteDescription({
        type: "answer",
        sdp: remoteSdp,
      })

      setTimeout(() => {
        if (isMounted.current && !connected && dataChannelRef.current?.readyState !== "open") {
          setError("Connection timed out. Please try again.")
          setConnecting(false)
        }
      }, 15000)
    } catch (err) {
      console.error("WebRTC setup error:", err)
      setError(err instanceof Error ? err.message : "Failed to establish voice connection")
      setConnecting(false)
      endRealtimeConnection()
    }
  }

  const setupPeerConnectionEventLogging = (peerConnection: RTCPeerConnection): void => {
    peerConnection.onicegatheringstatechange = () => {
      console.log("ICE gathering state:", peerConnection.iceGatheringState)
    }

    peerConnection.onicecandidateerror = (event: RTCPeerConnectionIceErrorEvent) => {
      console.error("ICE candidate error:", event)
    }

    peerConnection.onnegotiationneeded = () => {
      console.log("Negotiation needed")
    }

    peerConnection.onicecandidate = (event: RTCPeerConnectionIceEvent) => {
      if (event.candidate) {
        console.log("New ICE candidate:", event.candidate.type)
      }
    }

    peerConnection.onsignalingstatechange = () => {
      console.log("Signaling state:", peerConnection.signalingState)
    }

    peerConnection.onconnectionstatechange = () => {
      console.log("Connection state:", peerConnection.connectionState)
    }
  }

  const configureDataChannel = (dataChannel: RTCDataChannel, assessmentSummary: string): void => {
    const logStateChange = () => {
      console.log(`Data channel state changed: ${dataChannel.readyState}`)
    }

    dataChannel.onopen = () => {
      logStateChange()
      if (!isMounted.current) return

      console.log("Data channel open - connection established")
      setConnected(true)
      setConnecting(false)
      setIsListening(true)
      setIsSpeaking(false)
      setError(null)

      try {
        const config = {
          type: "session.update",
          session: {
            instructions: `You are an AI Personal Fitness Trainer possessing the expertise and perspective of a seasoned professional with years of experience analyzing client assessments and creating effective fitness strategies.
**Your Context:** You have been provided with the complete fitness assessment results for a client. This includes comprehensive data on their current fitness profile.
**Your Mandate:** For all subsequent questions from the user regarding this specific assessment:
1. **Adopt the Persona:** Respond *consistently* as an experienced, knowledgeable, and supportive fitness trainer. Your tone should be professional yet encouraging.
2. **Context is King:** Base your answers *strictly and exclusively* on the data provided in the client's assessment. Do not introduce external assumptions or generic information not supported by the assessment results.
3. **Interpret, Don't Just Report:** Go beyond simply stating the numbers. *Interpret* what the results mean in practical terms regarding the client's current fitness level, potential strengths, and areas needing focus.
4. **Clarity is Crucial:** Explain any fitness terminology or assessment metrics in simple, easy-to-understand language.
5. **Identify Implications:** When relevant to the question, highlight the implications of the assessment findings for the client's health, performance, or potential goal achievement (based *only* on the data).
6. **Maintain Scope:** Confine your analysis and explanations to the realm of fitness and wellness. Avoid making medical diagnoses or giving advice that requires medical expertise.
7. **Be Objective:** While supportive, ensure your interpretation of the data is objective and accurately reflects the assessment findings.
8. **Greeting the Client:** Always initiate the discussion by saying.., "Hi, How can I assist you with your asessment today?" 

The client's assessment is as follows:
${assessmentSummary}`,
            turn_detection: { type: "server_vad" },
            voice: "alloy",
            modalities: ["text", "audio"],
          },
        }

        dataChannel.send(JSON.stringify(config))
      } catch (configError) {
        console.error("Config send failed:", configError)
        if (isMounted.current) {
          setError("Failed to initialize your Personal Coach. Please try reconnecting.")
          endRealtimeConnection()
        }
      }
    }

    dataChannel.onclose = () => {
      logStateChange()
      if (!isMounted.current) return
      setConnected(false)
      setError("Speak to your Personal Trainer")
    }

    dataChannel.onerror = (event: Event) => {
      if (!isMounted.current) return

      const errorEvent = event as RTCErrorEvent
      try {
        const errorProps: Record<string, string | number | boolean> = {
          error: errorEvent.error?.message || "Unknown error",
          type: errorEvent.type,
        }
        console.error("Data channel error:", errorProps)
      } catch {
        console.error("Data channel error (could not extract details)")
      }

      setError("Communication error with the Personal Coach. Please try reconnecting.")
    }

    dataChannel.onmessage = (event: MessageEvent<string>) => {
      if (!isMounted.current) return

      try {
        const message = JSON.parse(event.data) as {
          type: string
          text?: string
          error?: { message?: string } | string
        }

        if (message.type === "text") {
          console.log("Text received:", message.text)
        } else if (message.type === "audio_start") {
          setIsSpeaking(true)
          setIsListening(false)
        } else if (message.type === "audio_end") {
          setIsSpeaking(false)
          setIsListening(true)
        } else if (message.type === "session.error") {
          console.error("Session error:", message.error)
          const errorMsg =
            typeof message.error === "object" && message.error?.message
              ? message.error.message
              : "An error occurred with the Personal Coach"
          setError(errorMsg)
        } else if (message.type === "error") {
          console.error("Message error:", message)
          setError(typeof message.error === "string" ? message.error : "An error occurred with the Personal Coach")
        }
      } catch {
        console.error("Message parsing error")
      }
    }
  }

  const waitForIceGathering = (peerConnection: RTCPeerConnection): Promise<void> => {
    return new Promise((resolve) => {
      if (peerConnection.iceGatheringState === "complete") {
        resolve()
        return
      }

      const checkState = () => {
        if (peerConnection.iceGatheringState === "complete") {
          peerConnection.removeEventListener("icegatheringstatechange", checkState)
          resolve()
        }
      }

      peerConnection.addEventListener("icegatheringstatechange", checkState)
      setTimeout(() => {
        peerConnection.removeEventListener("icegatheringstatechange", checkState)
        resolve()
      }, 8000)
    })
  }

  const setupAudioProcessing = async (): Promise<void> => {
    try {
      // Use the type alias instead of 'any'
      const AudioContextClass: AudioContextConstructor | undefined =
        window.AudioContext ||
        ("webkitAudioContext" in window
          ? (window as unknown as { webkitAudioContext: AudioContextConstructor }).webkitAudioContext
          : undefined)

      if (!AudioContextClass) {
        throw new Error("AudioContext not supported in this browser")
      }

      const audioContext = new AudioContextClass()
      audioContextRef.current = audioContext

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          channelCount: 1,
          sampleRate: 24000,
        },
      })

      mediaStreamRef.current = stream
      const tracks = stream.getAudioTracks()

      if (tracks.length === 0) {
        throw new Error("No audio tracks found in microphone stream")
      }

      tracks.forEach((track) => {
        track.enabled = false
        if (peerConnectionRef.current) {
          try {
            peerConnectionRef.current.addTrack(track, stream)
          } catch (error) {
            console.error("Error adding audio track:", error)
            throw new Error("Failed to configure audio")
          }
        } else {
          throw new Error("Peer connection not initialized")
        }
      })
    } catch (err) {
      console.error("Audio setup error:", err)
      let errorMessage = "Microphone access error"

      if (err instanceof DOMException) {
        if (err.name === "NotAllowedError" || err.name === "PermissionDeniedError") {
          errorMessage = "Microphone access denied. Please allow microphone access in your browser settings."
        } else if (err.name === "NotFoundError" || err.name === "DevicesNotFoundError") {
          errorMessage = "No microphone detected. Please connect a microphone and try again."
        } else if (err.name === "NotReadableError" || err.name === "TrackStartError") {
          errorMessage = "Microphone is in use by another application. Please close other applications and try again."
        } else {
          errorMessage = `Microphone error: ${err.message}`
        }
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      setError(errorMessage)
      throw err
    }
  }

  const toggleMicrophone = (): void => {
    if (!mediaStreamRef.current || !connected) {
      console.warn("Cannot toggle microphone - stream not available or not connected")
      return
    }

    try {
      const newState = !micActive
      setMicActive(newState)
      mediaStreamRef.current.getAudioTracks().forEach((track) => {
        track.enabled = newState
      })
    } catch (micError) {
      console.error("Error toggling microphone:", micError)
      setError("Failed to control microphone. Please try reconnecting.")
      setMicActive(false)
    }
  }

  const endRealtimeConnection = (): void => {
    if (dataChannelRef.current) {
      try {
        if (dataChannelRef.current.readyState === "open") {
          dataChannelRef.current.send(JSON.stringify({ type: "session.close" }))
        }
      } catch (closeError) {
        console.error("Error sending close message:", closeError)
      }
      dataChannelRef.current.close()
      dataChannelRef.current = null
    }

    if (peerConnectionRef.current) {
      peerConnectionRef.current.oniceconnectionstatechange = null
      peerConnectionRef.current.onicegatheringstatechange = null
      peerConnectionRef.current.ontrack = null
      peerConnectionRef.current.onicecandidateerror = null
      peerConnectionRef.current.onnegotiationneeded = null
      peerConnectionRef.current.onicecandidate = null
      peerConnectionRef.current.onsignalingstatechange = null
      peerConnectionRef.current.onconnectionstatechange = null
      peerConnectionRef.current.close()
      peerConnectionRef.current = null
      if (mediaStreamRef.current) {
        try {
          mediaStreamRef.current.getTracks().forEach((track) => {
            track.stop()
          })
        } catch (trackError) {
          console.error("Error stopping media tracks:", trackError)
        }
        mediaStreamRef.current = null
      }

      if (audioContextRef.current) {
        if (audioContextRef.current.state !== "closed") {
          audioContextRef.current.close().catch((contextError) => {
            console.error("Error closing audio context:", contextError)
          })
        }
        audioContextRef.current = null
      }

      setConnected(false)
      setMicActive(false)
      setIsListening(false)
      setIsSpeaking(false)
    }
  }

  const setAudioElement = (element: HTMLAudioElement | null) => {
    audioRef.current = element;
  };

  return {
    connecting,
    connected,
    isSpeaking,
    isListening,
    micActive,
    error,
    initializeRealtimeConnection,
    toggleMicrophone,
    endRealtimeConnection,
    setAudioElement
  }
}


