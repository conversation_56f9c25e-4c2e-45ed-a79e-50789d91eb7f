//components/exercise-modal.tsx
"use client"

import { X, Save, Check, AlertCircle } from "lucide-react"
import Image from "next/image"
import { useEffect, useState, FormEvent } from "react"
import EnhancedMarkdownContent from "@/components/chatMessage/EnhancedMarkdownContent"
import YouTubeEmbed, { extractYouTubeVideoId } from "@/components/YouTubeEmbed"
import { doc, updateDoc } from "firebase/firestore"
import { db } from "@/components/firebase/config"
import { getAuth, onAuthStateChanged, User } from "firebase/auth"; // Added Firebase Auth

interface Exercise {
  name: string
  focusArea: string
  description: string
  level: string
  muscleGroups: string[]
  equipment: string
  variations: string[]
  image?: string
  video?: string
  urlVideo?: string // New field for YouTube URL
  videoId?: string // New field for direct YouTube video ID
  id: string
}

interface ExerciseModalProps {
  exercise: Exercise | null
  onClose: () => void
}

export function ExerciseModal({ exercise, onClose }: ExerciseModalProps) {
  const [videoError, setVideoError] = useState(false);
  const [youtubeUrl, setYoutubeUrl] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateStatus, setUpdateStatus] = useState<{ type: 'success' | 'error', message: string } | null>(null);

  // Next Auth session
  const [_currentUser, _setCurrentUser] = useState<User | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  // Admin email from environment variable with fallback
  const adminEmail = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>';

  // Derived state for admin access check
  const isAdmin = userEmail === adminEmail;

  // Next Auth session is handled by the useSession hook
  // No need for a separate authentication listener
    // Firebase Authentication listener
    useEffect(() => {
      const auth = getAuth();
  
      const unsubscribe = onAuthStateChanged(auth, (user) => {
        _setCurrentUser(user); // Update the user object (not directly used)
        setUserEmail(user?.email || null); // We only need the email for admin check
  
        if (user) {
          console.log("User authenticated:", user.email);
        } else {
          console.log("No user authenticated");
        }
      });
  
      // Clean up the listener on component unmount
      return () => unsubscribe();
    }, []);

    

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose()
      }
    }

    document.addEventListener("keydown", handleEscape)
    return () => document.removeEventListener("keydown", handleEscape)
  }, [onClose])

  useEffect(() => {
    // Reset error state when exercise changes
    setVideoError(false);
    setUpdateStatus(null);

    // Set the initial YouTube URL if it exists
    if (exercise?.urlVideo) {
      setYoutubeUrl(exercise.urlVideo);
    } else {
      setYoutubeUrl("");
    }
  }, [exercise]);

  if (!exercise) return null

  const hasVideo = Boolean(exercise.urlVideo || exercise.videoId);

  // Handle video errors by falling back to image
  const handleVideoError = () => {
    console.log("Video failed to load, falling back to image");
    setVideoError(true);
  };

  // Handle YouTube URL update
  const handleUpdateYouTubeUrl = async (e: FormEvent) => {
    e.preventDefault();

    if (!exercise) return;

    try {
      setIsUpdating(true);
      setUpdateStatus(null);

      // Validate the URL
      const videoId = extractYouTubeVideoId(youtubeUrl);
      if (!videoId) {
        setUpdateStatus({
          type: 'error',
          message: 'Invalid YouTube URL. Please enter a valid YouTube video URL.'
        });
        setIsUpdating(false);
        return;
      }

      // Update the Firestore document
      const exerciseRef = doc(db, "Fitness Library", exercise.id);
      await updateDoc(exerciseRef, {
        urlVideo: youtubeUrl,
        videoId: videoId,
        videoThumbnail: `https://i.ytimg.com/vi/${videoId}/hqdefault.jpg`,
        updatedAt: new Date()
      });

      // Update success
      setUpdateStatus({
        type: 'success',
        message: 'YouTube URL updated successfully!'
      });

      // Update the local exercise object to show the video immediately
      exercise.urlVideo = youtubeUrl;
      exercise.videoId = videoId;

      // Reset video error state to show the video
      setVideoError(false);
    } catch (error) {
      console.error('Error updating YouTube URL:', error);
      setUpdateStatus({
        type: 'error',
        message: 'Failed to update YouTube URL. Please try again.'
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4" onClick={onClose}>
      <div
        className="bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-2xl"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="sticky top-0 bg-slate-900 z-10 flex items-center justify-between p-6 border-b border-slate-700">
        <Image
              src="/images/logo2b.png"
              alt="IntelligentFitness Logo"
              width={36}
              height={32}
              className="object-contain"
              priority
            />
          <h2 className="text-2xl font-bold text-white text-left">{exercise.name}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-800 rounded-full transition-colors text-gray-400 hover:text-white"
            aria-label="Close modal"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="aspect-video relative mb-6 rounded-xl overflow-hidden">
            {/* Show YouTube video if available and not errored */}
            {hasVideo && !videoError ? (
              <YouTubeEmbed
                videoId={exercise.videoId || (exercise.urlVideo || '')}
                className="w-full h-full"
                onError={handleVideoError}
              />
            ) : exercise.image ? (
              <Image src={exercise.image} alt={exercise.name} fill className="object-cover" />
            ) : (
              <div className="w-full h-full bg-slate-800 flex items-center justify-center">
                <div className="w-16 h-16 rounded-full bg-slate-700" />
              </div>
            )}
          </div>

          <div className="space-y-6">
            <div className="flex flex-wrap gap-2">
              <span className="px-3 py-1 bg-blue-900/50 text-blue-300 rounded-full text-sm font-medium">
                {exercise.level}
              </span>
              <span className="px-3 py-1 bg-purple-900/50 text-purple-300 rounded-full text-sm font-medium">
                {exercise.focusArea}
              </span>
              <span className="px-3 py-1 bg-indigo-900/50 text-indigo-300 rounded-full text-sm font-medium">
                {exercise.equipment}
              </span>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2 text-white">Muscle Groups</h3>
              <div className="flex flex-wrap gap-2">
                {exercise.muscleGroups.map((muscle) => (
                  <span key={muscle} className="px-3 py-1 bg-slate-700 text-slate-300 rounded-full text-sm">
                    {muscle}
                  </span>
                ))}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2 text-gray-300">Description</h3>
              <div className="text-gray-300">
                <EnhancedMarkdownContent
                  content={exercise.description}
                  onCopyCode={(code) => {
                    navigator.clipboard.writeText(code)
                  }}
                />
              </div>
            </div>

            {exercise.variations && exercise.variations.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-2 text-gray-300">Variations</h3>
                <ul className="list-disc pl-5 text-gray-300">
                  {exercise.variations.map((variation) => (
                    <li key={variation}>{variation}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* YouTube URL update form - only visible for admin users */}
            {isAdmin && (
              <div className="mt-6 border-t border-slate-700 pt-6">
                <h3 className="text-lg font-semibold mb-3 text-white">Admin: Update YouTube Video</h3>

                <form onSubmit={handleUpdateYouTubeUrl} className="space-y-4">
                  <div>
                    <label htmlFor="youtubeUrl" className="block text-sm font-medium text-gray-300 mb-1">
                      YouTube URL
                    </label>
                    <input
                      type="text"
                      id="youtubeUrl"
                      value={youtubeUrl}
                      onChange={(e) => setYoutubeUrl(e.target.value)}
                      placeholder="https://www.youtube.com/watch?v=..."
                      className="w-full px-4 py-2 bg-slate-800 border border-slate-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                      disabled={isUpdating}
                    />
                  </div>

                  {/* Status message */}
                  {updateStatus && (
                    <div className={`p-3 rounded-md ${updateStatus.type === 'success' ? 'bg-green-900/50 text-green-300' : 'bg-red-900/50 text-red-300'} flex items-start gap-2`}>
                      {updateStatus.type === 'success' ? (
                        <Check className="w-5 h-5 mt-0.5 flex-shrink-0" />
                      ) : (
                        <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
                      )}
                      <span>{updateStatus.message}</span>
                    </div>
                  )}

                  <div className="flex justify-end">
                    <button
                      type="submit"
                      disabled={isUpdating}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isUpdating ? (
                        <>
                          <span className="animate-pulse">Updating...</span>
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4" />
                          Save YouTube URL
                        </>
                      )}
                    </button>
                  </div>
                </form>
              </div>
            )}

          </div>
        </div>
      </div>
    </div>
  )
}