import { Firestore, doc, getDoc, writeBatch, collection, DocumentReference } from 'firebase/firestore';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { OpenAIEmbeddings } from "@langchain/openai";
import { Pinecone } from '@pinecone-database/pinecone';

// Constants for text chunking
const CHUNK_SIZE = 1000;
const CHUNK_OVERLAP = 200;

// Define workout plan types
export type WorkoutPlanType = 'kick_starter' | 'schedule' | 'longTerm' | 'shortTerm';

// Generic interface for Firestore document data
interface FirestoreData {
  [key: string]: unknown;
}

// Interface for exercise details
interface Exercise {
  exercise?: string;
  sets?: string | number;
  reps?: string | number;
  duration?: string | number;
  notes?: string;
  details?: string;
  instructions?: string;
}

// Interface for a day in a workout plan
interface WorkoutDay {
  day?: string | number;
  name?: string;
  warm_up?: Exercise[];
  resistance_training?: Exercise[];
  cardio_training?: Exercise[];
  cool_down?: Exercise[];
  exercises?: Exercise[];
}

// Interface for Kick Starter plan data
interface KickStarterPlanData extends FirestoreData {
  schedule?: {
    title?: string;
    days?: WorkoutDay[];
    lastUpdated?: string;
  };
  workoutReferenceId?: string;
  createdAt?: string;
}

// Interface for Long Term plan data
interface LongTermPlanData extends FirestoreData {
  title?: string;
  notes?: string[];
  strategies?: {
    overcomePlateaus?: string;
    maintainMotivation?: string;
    variety?: string;
  };
  schedule?: WorkoutDay[];
  workoutReferenceId?: string;
  createdAt?: string;
  lastUpdated?: string;
}

// Interface for Short Term plan data
interface ShortTermPlanData extends FirestoreData {
  title?: string;
  goal?: string;
  duration?: string;
  weeks?: {
    week?: string | number;
    days?: WorkoutDay[];
  }[];
  workoutReferenceId?: string;
  createdAt?: string;
  lastUpdated?: string;
}

// Interface for Schedule plan data
interface SchedulePlanData extends FirestoreData {
  title?: string;
  days?: WorkoutDay[];
  workoutReferenceId?: string;
  createdAt?: string;
  lastUpdated?: string;
}

// Union type for all plan data
type PlanData = KickStarterPlanData | LongTermPlanData | ShortTermPlanData | SchedulePlanData;

// Interface for metadata stored in Pinecone
interface PlanMetadata {
  planType: WorkoutPlanType;
  title: string;
  createdAt: string;
  lastUpdated: string;
  [key: string]: string | number | boolean | string[];
}

// Interface for process result
interface ProcessResult {
  success: boolean;
  planType: WorkoutPlanType;
  planId?: string;
  workoutReferenceId?: string;
  chunkCount?: number;
  vectorsUpserted?: number;
  processingTime?: string;
  error?: string;
  stack?: string;
}

/**
 * Utility function to safely access nested properties in an object with a default fallback.
 */
function getNestedProperty<T = unknown>(obj: unknown, path: string, defaultValue: T = null as T): unknown {
  const keys = path.split('.');
  let current: unknown = obj;

  for (const key of keys) {
    if (current === null || current === undefined || typeof current !== 'object') {
      return defaultValue;
    }
    current = (current as Record<string, unknown>)[key];
  }

  return current === undefined ? defaultValue : current;
}

/**
 * Convert a kick_starter plan to a text format suitable for embedding.
 */
function kickStarterPlanToText(planData: KickStarterPlanData): string {
  const schedule = getNestedProperty(planData, 'schedule', {}) as { title?: string; days?: WorkoutDay[] };
  const title = getNestedProperty(schedule, 'title', 'Kick Starter Workout Plan') as string;
  const days = getNestedProperty(schedule, 'days', []) as WorkoutDay[];

  let text = `# ${title}\n\n`;

  for (const day of days) {
    const dayNumber = getNestedProperty(day, 'day', 'Unknown Day') as string | number;
    const dayName = getNestedProperty(day, 'name', '') as string;
    text += `## Day ${dayNumber}${dayName ? `: ${dayName}` : ''}\n\n`;

    const warmUp = getNestedProperty(day, 'warm_up', []) as Exercise[];
    if (warmUp.length > 0) {
      text += "### Warm-up\n";
      for (const exercise of warmUp) {
        const name = getNestedProperty(exercise, 'exercise', 'Unknown Exercise') as string;
        const duration = getNestedProperty(exercise, 'duration', '') as string | number;
        const details = getNestedProperty(exercise, 'details', '') as string;
        text += `- **${name}**`;
        if (duration) text += ` for ${duration} minutes`;
        if (details) text += ` (${details})`;
        text += `\n`;
      }
      text += "\n";
    }

    const resistance = getNestedProperty(day, 'resistance_training', []) as Exercise[];
    if (resistance.length > 0) {
      text += "### Resistance Training\n";
      for (const exercise of resistance) {
        const name = getNestedProperty(exercise, 'exercise', 'Unknown Exercise') as string;
        const sets = getNestedProperty(exercise, 'sets', '') as string | number;
        const reps = getNestedProperty(exercise, 'reps', '') as string | number;
        const notes = getNestedProperty(exercise, 'notes', '') as string;
        text += `- **${name}**: ${sets} sets x ${reps} reps ${notes ? `(${notes})` : ''}\n`;
      }
      text += "\n";
    }

    const cardio = getNestedProperty(day, 'cardio_training', []) as Exercise[];
    if (cardio.length > 0) {
      text += "### Cardio Training\n";
      for (const exercise of cardio) {
        const name = getNestedProperty(exercise, 'exercise', 'Unknown Exercise') as string;
        const duration = getNestedProperty(exercise, 'duration', '') as string | number;
        const notes = getNestedProperty(exercise, 'notes', '') as string;
        text += `- **${name}**: ${duration} minutes ${notes ? `(${notes})` : ''}\n`;
      }
      text += "\n";
    }

    const coolDown = getNestedProperty(day, 'cool_down', []) as Exercise[];
    if (coolDown.length > 0) {
      text += "### Cool-down\n";
      for (const exercise of coolDown) {
        const name = getNestedProperty(exercise, 'exercise', 'Unknown Exercise') as string;
        const duration = getNestedProperty(exercise, 'duration', '') as string | number;
        const details = getNestedProperty(exercise, 'details', '') as string;
        text += `- **${name}**`;
        if (duration) text += ` for ${duration} minutes`;
        if (details) text += ` (${details})`;
        text += `\n`;
      }
      text += "\n";
    }
  }

  return text;
}

/**
 * Convert a longTerm plan to a text format suitable for embedding.
 */
function longTermPlanToText(planData: LongTermPlanData): string {
  const title = getNestedProperty(planData, 'title', 'Long Term Workout Plan') as string;
  const notes = getNestedProperty(planData, 'notes', []) as string[];
  const strategies = getNestedProperty(planData, 'strategies', {}) as { overcomePlateaus?: string; maintainMotivation?: string; variety?: string };
  const schedule = getNestedProperty(planData, 'schedule', []) as WorkoutDay[];

  let text = `# ${title}\n\n`;

  if (notes.length > 0) {
    text += "## Program Notes\n";
    for (const note of notes) {
      text += `- ${note}\n`;
    }
    text += "\n";
  }

  if (Object.keys(strategies).length > 0) {
    text += "## Training Strategies\n";
    if (strategies.overcomePlateaus) text += `### Overcome Plateaus\n${strategies.overcomePlateaus}\n\n`;
    if (strategies.maintainMotivation) text += `### Maintain Motivation\n${strategies.maintainMotivation}\n\n`;
    if (strategies.variety) text += `### Training Variety\n${strategies.variety}\n\n`;
  }

  for (const day of schedule) {
    const dayName = getNestedProperty(day, 'name', 'Day') as string;
    text += `## ${dayName}\n\n`;
    const exercises = getNestedProperty(day, 'exercises', []) as Exercise[];
    for (const exercise of exercises) {
      const name = getNestedProperty(exercise, 'exercise', 'Unknown Exercise') as string;
      const sets = getNestedProperty(exercise, 'sets', '') as string | number;
      const reps = getNestedProperty(exercise, 'reps', '') as string | number;
      const notes = getNestedProperty(exercise, 'notes', '') as string;
      text += `- **${name}**: ${sets} sets x ${reps} reps ${notes ? `(${notes})` : ''}\n`;
    }
    text += "\n";
  }

  return text;
}

/**
 * Convert a shortTerm plan to a text format suitable for embedding.
 */
function shortTermPlanToText(planData: ShortTermPlanData): string {
  const title = getNestedProperty(planData, 'title', 'Short Term Workout Plan') as string;
  const goal = getNestedProperty(planData, 'goal', '') as string;
  const duration = getNestedProperty(planData, 'duration', '') as string;
  const weeks = getNestedProperty(planData, 'weeks', []) as { week?: string | number; days?: WorkoutDay[] }[];

  let text = `# ${title}\n\n`;

  if (goal) text += `## Goal\n${goal}\n\n`;
  if (duration) text += `## Duration\n${duration}\n\n`;

  for (const week of weeks) {
    const weekNumber = getNestedProperty(week, 'week', 'Week') as string | number;
    text += `## ${weekNumber}\n\n`;
    const days = getNestedProperty(week, 'days', []) as WorkoutDay[];
    for (const day of days) {
      const dayName = getNestedProperty(day, 'name', 'Day') as string;
      text += `### ${dayName}\n`;
      const exercises = getNestedProperty(day, 'exercises', []) as Exercise[];
      for (const exercise of exercises) {
        const name = getNestedProperty(exercise, 'exercise', 'Unknown Exercise') as string;
        const sets = getNestedProperty(exercise, 'sets', '') as string | number;
        const reps = getNestedProperty(exercise, 'reps', '') as string | number;
        const notes = getNestedProperty(exercise, 'notes', '') as string;
        text += `- **${name}**: ${sets} sets x ${reps} reps ${notes ? `(${notes})` : ''}\n`;
      }
      text += "\n";
    }
  }

  return text;
}

/**
 * Convert a schedule plan to a text format suitable for embedding.
 */
function schedulePlanToText(planData: SchedulePlanData): string {
  const title = getNestedProperty(planData, 'title', 'Workout Schedule') as string;
  const days = getNestedProperty(planData, 'days', []) as WorkoutDay[];

  let text = `# ${title}\n\n`;

  for (const day of days) {
    const dayName = getNestedProperty(day, 'name', 'Day') as string;
    text += `## ${dayName}\n\n`;
    const exercises = getNestedProperty(day, 'exercises', []) as Exercise[];
    for (const exercise of exercises) {
      const name = getNestedProperty(exercise, 'exercise', 'Unknown Exercise') as string;
      const sets = getNestedProperty(exercise, 'sets', '') as string | number;
      const reps = getNestedProperty(exercise, 'reps', '') as string | number;
      const instructions = getNestedProperty(exercise, 'instructions', '') as string;
      text += `- **${name}**: ${sets} sets x ${reps} reps${instructions ? ` (Instructions: ${instructions})` : ''}\n`;
    }
    text += "\n";
  }

  return text;
}

/**
 * Convert a workout plan to text based on its type.
 */
function workoutPlanToText(planData: PlanData, planType: WorkoutPlanType): string {
  switch (planType) {
    case 'kick_starter':
      return kickStarterPlanToText(planData as KickStarterPlanData);
    case 'longTerm':
      return longTermPlanToText(planData as LongTermPlanData);
    case 'shortTerm':
      return shortTermPlanToText(planData as ShortTermPlanData);
    case 'schedule':
      return schedulePlanToText(planData as SchedulePlanData);
    default:
      throw new Error(`Unsupported plan type: ${planType}`);
  }
}

/**
 * Extract metadata from a workout plan based on its type for inclusion in Pinecone vectors.
 */
function extractWorkoutPlanMetadata(planData: PlanData, planType: WorkoutPlanType): PlanMetadata {
  const metadata: PlanMetadata = {
    planType,
    title: planType === 'kick_starter'
      ? getNestedProperty(planData, 'schedule.title', 'Kick Starter Plan') as string
      : getNestedProperty(planData, 'title', `${planType} Plan`) as string,
    createdAt: getNestedProperty(planData, 'createdAt', new Date().toISOString()) as string,
    lastUpdated: planType === 'kick_starter'
      ? getNestedProperty(planData, 'schedule.lastUpdated', new Date().toISOString()) as string
      : getNestedProperty(planData, 'lastUpdated', new Date().toISOString()) as string,
  };

  switch (planType) {
    case 'kick_starter':
      const days = getNestedProperty(planData, 'schedule.days', []) as WorkoutDay[];
      metadata.dayCount = days.length;
      metadata.exerciseTypes = [];

      days.forEach((day: WorkoutDay) => {
        const warmUp = getNestedProperty(day, 'warm_up', []);
        if (Array.isArray(warmUp) && warmUp.length > 0) {
          (metadata.exerciseTypes as string[]).push('warm_up');
        }

        const resistanceTraining = getNestedProperty(day, 'resistance_training', []);
        if (Array.isArray(resistanceTraining) && resistanceTraining.length > 0) {
          (metadata.exerciseTypes as string[]).push('resistance_training');
        }

        const cardioTraining = getNestedProperty(day, 'cardio_training', []);
        if (Array.isArray(cardioTraining) && cardioTraining.length > 0) {
          (metadata.exerciseTypes as string[]).push('cardio_training');
        }

        const coolDown = getNestedProperty(day, 'cool_down', []);
        if (Array.isArray(coolDown) && coolDown.length > 0) {
          (metadata.exerciseTypes as string[]).push('cool_down');
        }
      });

      metadata.exerciseTypes = [...new Set(metadata.exerciseTypes as string[])];
      break;

    case 'longTerm':
      metadata.hasStrategies = Object.keys(getNestedProperty(planData, 'strategies', {}) as Record<string, unknown>).length > 0;
      metadata.dayCount = (getNestedProperty(planData, 'schedule', []) as WorkoutDay[]).length;
      metadata.noteCount = (getNestedProperty(planData, 'notes', []) as string[]).length;
      break;

    case 'shortTerm':
      metadata.goal = getNestedProperty(planData, 'goal', '') as string;
      metadata.duration = getNestedProperty(planData, 'duration', '') as string;
      metadata.weekCount = (getNestedProperty(planData, 'weeks', []) as { week?: string | number; days?: WorkoutDay[] }[]).length;
      break;

    case 'schedule':
      metadata.dayCount = (getNestedProperty(planData, 'days', []) as WorkoutDay[]).length;
      break;
  }

  return metadata;
}

/**
 * Process a workout plan for vector search by converting it to text, chunking it,
 * storing it in Firestore, generating embeddings, and upserting to Pinecone.
 */
export async function processWorkoutPlanForVectorSearch(
  db: Firestore,
  userEmail: string,
  planType: WorkoutPlanType
): Promise<ProcessResult> {
  try {
    const timestamp = new Date().toISOString();
    const planRef = doc(db, "IF_users", userEmail, "Profile", planType);
    const planSnap = await getDoc(planRef);

    if (!planSnap.exists()) {
      throw new Error(`No ${planType} plan found for user ${userEmail}`);
    }

    const planData = planSnap.data() as PlanData;
    const planId = planSnap.id;
    const workoutReferenceId = getNestedProperty(planData, 'workoutReferenceId', planId) as string;

    // Fetch the specific file document using workoutReferenceId
    const fileDocRef = doc(db, "users", userEmail, "files", workoutReferenceId);
    const fileSnap = await getDoc(fileDocRef);
    const documentTitle = fileSnap.exists() ? (fileSnap.data().name || 'Untitled') : 'Untitled';

    const assessmentRef = doc(db, "IF_users", userEmail, "Profile", "assessment");
    const assessmentSnap = await getDoc(assessmentRef);
    let summaryText = '';
    if (assessmentSnap.exists()) {
      summaryText = getNestedProperty(assessmentSnap.data(), 'summary', '') as string;
    }
    const Category = 'fitnessintelligentai';
    const workoutPlanText = workoutPlanToText(planData, planType);
    const combinedContent = summaryText ? `# User Fitness Profile Summary\n\n${summaryText}\n\n${workoutPlanText}` : workoutPlanText;

    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: CHUNK_SIZE,
      chunkOverlap: CHUNK_OVERLAP,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });
    const textChunks = await textSplitter.createDocuments([combinedContent]);

    const planMetadata = extractWorkoutPlanMetadata(planData, planType);

    const chunkIds: string[] = [];
    const chunksRef = collection(db, "users", userEmail, "byteStoreCollection");
    const batch = writeBatch(db);

    for (let i = 0; i < textChunks.length; i++) {
      const chunk = textChunks[i];
      const chunkId = `${workoutReferenceId}_${i}`;
      chunkIds.push(chunkId);

      const chunkDoc = doc(chunksRef, chunkId);
      batch.set(chunkDoc, {
        content: chunk.pageContent,
        metadata: {
          category: Category,
          chunk_id: chunkId,
          doc_id: workoutReferenceId,
          document_title: documentTitle,
          planType,
          hasSummary: i === 0 && !!summaryText,
          chunkIndex: i,
          totalChunks: textChunks.length,
          createdAt: timestamp
        }
      });
    }

    const processingRef = doc(db, "users", userEmail, "byteStoreCollection", workoutReferenceId);
    batch.set(processingRef, {
      planType,
      planId,
      workoutReferenceId,
      chunkIds,
      chunkCount: textChunks.length,
      processingTime: timestamp,
      status: "completed"
    });

    await batch.commit();

    // Get OpenAI API key from environment variables
    const apiKey = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';
    if (!apiKey) {
      throw new Error("The OPENAI_API_KEY environment variable is missing or empty");
    }

    // Explicitly pass the API key to the OpenAIEmbeddings constructor
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: apiKey
    });

    // Generate embeddings for each text chunk and format for Pinecone
    const vectors = await Promise.all(textChunks.map(async (chunk, i) => {
      const embedding = await embeddings.embedQuery(chunk.pageContent);
      const chunkId = `${workoutReferenceId}_${i}`;

      return {
        id: chunkId,
        values: embedding,
        metadata: {
          category: Category,
          chunk_id: chunkId,
          doc_id: workoutReferenceId,
          document_title: documentTitle,
          userEmail,
          chunkIndex: i,
          totalChunks: textChunks.length,
          hasSummary: i === 0 && !!summaryText,
          ...planMetadata,
          processingTimestamp: timestamp
        }
      };
    }));

    const pineconeApiKey = process.env.PINECONE_API_KEY || '3b176b1d-1ed2-437b-8346-0f497c138048';
    if (!pineconeApiKey) {
      throw new Error("The PINECONE_API_KEY environment variable is missing or empty");
    }

    const pineconeIndexName = process.env.PINECONE_INDEX || 'intelligentfitness';
    if (!pineconeIndexName) {
      throw new Error("The PINECONE_INDEX environment variable is missing or empty");
    }

    // Initialize Pinecone client and get the index
    const pinecone = new Pinecone({
      apiKey: pineconeApiKey
    });

    const pineconeIndex = pinecone.Index(pineconeIndexName);

    // Use the workoutReferenceId as the namespace for organizing vectors by workout plan
    const namespace = workoutReferenceId;

    // Format the vectors in the structure expected by Pinecone
    const pineconeVectors = {
      vectors: vectors.map(v => ({
        id: v.id,
        values: v.values,
        metadata: v.metadata
      }))
    };

    let upsertedCount = 0;

    try {
      await pineconeIndex.namespace(namespace).upsert(pineconeVectors.vectors);
    } catch (pineconeError) {
      console.error(`Error during Pinecone upsert operation:`, pineconeError);
      // Continue execution - we'll use the vectors.length as fallback
    }

    // Default to vectors length if upsertedCount is not available
    if (upsertedCount === 0) {
      upsertedCount = vectors.length;
    }

    return {
      success: true,
      planType,
      planId,
      workoutReferenceId,
      chunkCount: textChunks.length,
      vectorsUpserted: upsertedCount,
      processingTime: timestamp
    };
  } catch (error) {
    const typedError = error as Error;
    console.error(`Error processing ${planType} plan for vector search:`, typedError);
    return {
      success: false,
      planType,
      error: typedError.message,
      stack: typedError.stack
    };
  }
}

/**
 * Process all workout plans for vector search.
 */
export async function processAllWorkoutPlansForVectorSearch(
  db: Firestore,
  userEmail: string,
  planTypes: WorkoutPlanType[] | 'all'
): Promise<{ success: boolean; summary: { totalProcessed: number; successCount: number; failureCount: number }; results: ProcessResult[] }> {
  const allPlanTypes: WorkoutPlanType[] = ['kick_starter', 'schedule', 'longTerm', 'shortTerm'];
  const typesToProcess = planTypes === 'all' ? allPlanTypes : planTypes;
  const results: ProcessResult[] = [];

  for (const planType of typesToProcess) {
    try {
      const result = await processWorkoutPlanForVectorSearch(db, userEmail, planType);
      results.push(result);
    } catch (error) {
      const typedError = error as Error;
      results.push({ success: false, planType, error: typedError.message });
    }
  }

  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;

  return {
    success: successCount > 0,
    summary: { totalProcessed: results.length, successCount, failureCount },
    results
  };
}

/**
 * Generate a kick_starter plan and process it for vector search.
 */
export async function generateKickStarterWithVectors(
  db: Firestore,
  userEmail: string,
  documentId: string
): Promise<{ success: boolean; generation: { success: boolean; message: string }; vectorProcessing: ProcessResult }> {
  try {
    const planRef = doc(db, "IF_users", userEmail, "Profile", "kick_starter");
    await writeBatch(db).set(planRef, {
      schedule: {
        title: "Kick Starter Plan",
        days: [
          {
            day: 1,
            name: "Day 1",
            warm_up: [{ exercise: "Jumping Jacks", duration: 5 }],
            resistance_training: [{ exercise: "Push-ups", sets: 3, reps: 10 }]
          }
        ],
        lastUpdated: new Date().toISOString()
      },
      workoutReferenceId: documentId,
      createdAt: new Date().toISOString()
    }).commit();

    const vectorResult = await processWorkoutPlanForVectorSearch(db, userEmail, "kick_starter");

    return {
      success: vectorResult.success,
      generation: { success: true, message: "Kick starter plan generated successfully" },
      vectorProcessing: vectorResult
    };
  } catch (error) {
    const typedError = error as Error;
    console.error(`Error generating kick_starter plan with vectors for user ${userEmail}:`, typedError);
    return {
      success: false,
      generation: { success: false, message: "Failed to generate kick starter plan" },
      vectorProcessing: { success: false, planType: "kick_starter", error: typedError.message, stack: typedError.stack }
    };
  }
}

/**
 * Generate a longTerm plan and process it for vector search.
 */
export async function generateLongTermWithVectors(
  db: Firestore,
  userEmail: string,
  documentId: string
): Promise<{ success: boolean; generation: { success: boolean; message: string }; vectorProcessing: ProcessResult }> {
  try {
    const planRef = doc(db, "IF_users", userEmail, "Profile", "longTerm");
    await writeBatch(db).set(planRef, {
      title: "Long Term Plan",
      schedule: [
        {
          name: "Day 1",
          exercises: [{ exercise: "Squats", sets: 4, reps: 12 }]
        }
      ],
      workoutReferenceId: documentId,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    }).commit();

    const vectorResult = await processWorkoutPlanForVectorSearch(db, userEmail, "longTerm");

    return {
      success: vectorResult.success,
      generation: { success: true, message: "Long term plan generated successfully" },
      vectorProcessing: vectorResult
    };
  } catch (error) {
    const typedError = error as Error;
    console.error(`Error generating longTerm plan with vectors for user ${userEmail}:`, typedError);
    return {
      success: false,
      generation: { success: false, message: "Failed to generate long term plan" },
      vectorProcessing: { success: false, planType: "longTerm", error: typedError.message, stack: typedError.stack }
    };
  }
}

/**
 * Generate a shortTerm plan and process it for vector search.
 */
export async function generateShortTermWithVectors(
  db: Firestore,
  userEmail: string,
  documentId: string
): Promise<{ success: boolean; generation: { success: boolean; message: string }; vectorProcessing: ProcessResult }> {
  try {
    const planRef = doc(db, "IF_users", userEmail, "Profile", "shortTerm");
    await writeBatch(db).set(planRef, {
      title: "Short Term Plan",
      weeks: [
        {
          week: 1,
          days: [
            {
              name: "Day 1",
              exercises: [{ exercise: "Bench Press", sets: 3, reps: 8 }]
            }
          ]
        }
      ],
      workoutReferenceId: documentId,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    }).commit();

    const vectorResult = await processWorkoutPlanForVectorSearch(db, userEmail, "shortTerm");

    return {
      success: vectorResult.success,
      generation: { success: true, message: "Short term plan generated successfully" },
      vectorProcessing: vectorResult
    };
  } catch (error) {
    const typedError = error as Error;
    console.error(`Error generating shortTerm plan with vectors for user ${userEmail}:`, typedError);
    return {
      success: false,
      generation: { success: false, message: "Failed to generate short term plan" },
      vectorProcessing: { success: false, planType: "shortTerm", error: typedError.message, stack: typedError.stack }
    };
  }
}

/**
 * Process coach instructions to create a schedule and process it for vector search.
 */
export async function processCoachInstructionsWithVectors(
  db: Firestore,
  userEmail: string,
  coachInstructions: string,
  workoutPlanRef: DocumentReference
): Promise<{ success: boolean; generation: { success: boolean; message: string }; vectorProcessing: ProcessResult }> {
  try {
    const days: WorkoutDay[] = [];
    const lines = coachInstructions.split('\n');
    let currentDay: WorkoutDay = { name: "Day 1", exercises: [] };

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (trimmedLine.toLowerCase().startsWith('day')) {
        if (currentDay.exercises && currentDay.exercises.length > 0) {
          days.push(currentDay);
        }
        currentDay = { name: trimmedLine, exercises: [] };
      } else if (trimmedLine) {
        const [exercise, details] = trimmedLine.split(':');
        if (exercise && details) {
          const [sets, reps] = details.trim().split('x');
          currentDay.exercises!.push({
            exercise: exercise.trim(),
            sets: sets ? parseInt(sets.trim()) : undefined,
            reps: reps ? parseInt(reps.trim()) : undefined,
            instructions: trimmedLine
          });
        }
      }
    }

    if (currentDay.exercises && currentDay.exercises.length > 0) {
      days.push(currentDay);
    }

    await writeBatch(db).set(workoutPlanRef, {
      title: "Coach Created Schedule",
      days,
      workoutReferenceId: workoutPlanRef.id,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    }).commit();

    const vectorResult = await processWorkoutPlanForVectorSearch(db, userEmail, "schedule");

    return {
      success: vectorResult.success,
      generation: { success: true, message: "Schedule created from coach instructions successfully" },
      vectorProcessing: vectorResult
    };
  } catch (error) {
    const typedError = error as Error;
    console.error(`Error processing coach instructions with vectors for user ${userEmail}:`, typedError);
    return {
      success: false,
      generation: { success: false, message: "Failed to process coach instructions" },
      vectorProcessing: { success: false, planType: "schedule", error: typedError.message, stack: typedError.stack }
    };
  }
}