"use client"

import type React from "react"
import { <PERSON><PERSON>ircle, Loader2, <PERSON>ertCircle } from "lucide-react"
import type { ProgressStep } from "@/types/progress"

interface ProgressMeterProps {
  currentStep: ProgressStep
  steps: ProgressStep[]
  error?: string
}

export const ProgressMeter: React.FC<ProgressMeterProps> = ({ currentStep, steps, error }) => {
  const currentIndex = steps.indexOf(currentStep)

  return (
    <div className="w-full max-w-4xl mx-auto my-4 px-4">
      <ol className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-8 gap-4">
        {steps.map((step, index) => (
          <li
            key={index}
            className={`relative flex flex-col items-center ${
              index <= currentIndex ? "text-blue-600" : "text-gray-400"
            }`}
          >
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full border-2 bg-white
                ${index < currentIndex ? "border-blue-600 bg-blue-100" : "border-gray-300"}
                ${index === currentIndex ? "ring-2 ring-blue-400 ring-offset-2" : ""}
              `}
            >
              {index < currentIndex ? (
                <CheckCircle className="w-4 h-4 text-blue-600" />
              ) : index === currentIndex ? (
                <Loader2 className="w-4 h-4 animate-spin text-blue-600" />
              ) : (
                <span className="text-sm font-medium">{index + 1}</span>
              )}
            </div>
            {index < steps.length - 1 && (
              <div
                className={`hidden md:block absolute top-4 left-[calc(50%+16px)] w-[calc(100%-32px)] h-0.5 -z-10
                  ${index < currentIndex ? "bg-blue-600" : "bg-gray-300"}
                `}
              />
            )}
            <span className="mt-2 text-xs text-center font-medium break-words w-full px-1">{step}</span>
          </li>
        ))}
      </ol>
      {error && (
        <div className="flex items-center justify-center mt-6 text-red-600 bg-red-50 p-3 rounded-lg">
          <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
          <span className="text-sm">{error}</span>
        </div>
      )}
    </div>
  )
}

