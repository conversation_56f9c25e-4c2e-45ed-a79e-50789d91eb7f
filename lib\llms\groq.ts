import { Groq } from "groq-sdk";

interface GroqClientConfig {
  userEmail: string;
}

function getApiKey(userEmail: string): string {
  const adminEmail = process.env.SYS_ADMIN || '<EMAIL>';
  const apiKey = userEmail === adminEmail 
    ? process.env.GROQ_API_KEY || '********************************************************'
    : process.env.GROQ_ike_FREE_KEY || '********************************************************';
  
  return apiKey || "";
}

export function createGroqClient({ userEmail }: GroqClientConfig): Groq {
  const apiKey = getApiKey(userEmail);
  return new Groq({ 
    apiKey,
    dangerouslyAllowBrowser: true // Enable browser environment support
  });
}

export function createGroqCoachingClient(): Groq {
  const apiKey = process.env.GROQ_API_COACHING_KEY || '********************************************************'
  return new Groq({ 
    apiKey,
    dangerouslyAllowBrowser: true // Enable browser environment support
  });
}

export function createGroqShortTermCoachingClient(): Groq {
  const apiKey = process.env.GROQ_API_SHORTTERM_KEY || '********************************************************'
  //GROQ_API_SHORTTERM_KEY="********************************************************"
  return new Groq({ 
    apiKey,
    dangerouslyAllowBrowser: true // Enable browser environment support
  });
}

export default createGroqClient;