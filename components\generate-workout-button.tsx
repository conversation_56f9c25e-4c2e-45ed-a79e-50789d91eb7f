'use client'

import { useState } from 'react'
import { Loader2 } from 'lucide-react'

interface GenerateWorkoutButtonProps {
  onClick: () => Promise<void>
}

export function GenerateWorkoutButton({ onClick }: GenerateWorkoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleClick = async () => {
    setIsLoading(true)
    try {
      await onClick()
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <button
      onClick={handleClick}
      disabled={isLoading}
      className={`
        flex items-center justify-center
        px-4 py-2 font-semibold text-white
        bg-blue-600 rounded-lg shadow-md
        hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
      `}
    >
      {isLoading && <Loader2 className="mr-2 h-5 w-5 animate-spin" />}
      <span>{isLoading ? 'Generating...' : 'Generate workout plan'}</span>
    </button>
  )
}
