"use client"

import { Activity, Dumbbell, Target, Timer } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { useState } from "react"

export default function WorkoutPlans() {
  const [expandedImage, setExpandedImage] = useState<number | null>(null)

  const plans = [
    { 
      title: "Kick Starter",
      icon: <Activity className="w-6 h-6" />,
      iconBg: "bg-orange-500",
      description: "Kickstart Your Fitness: Tailored 7-Day Workout Plan",
      features: ["Quick-start routine", "Balanced exercises", "Easy implementation", "Immediate guidance"],
      buttonColor: "bg-orange-600 hover:bg-orange-700",
      link: "/start/kickstart",
      dotColor: "text-orange-500",
      detailedDescription:
        "Start your fitness journey with our carefully crafted 7-day program. Perfect for beginners and those looking to get back into exercise. Our quick-start routine ensures immediate progress while maintaining proper form and technique.",
    },
    {
      title: "Personal Trainer",
      icon: <Dumbbell className="w-6 h-6" />,
      iconBg: "bg-blue-500",
      description: "Get Expert Guidance: View Exercise Instructions, Common Mistakes, and Modifications",
      features: [
        "Detailed exercise instructions",
        "Common mistake prevention",
        "Exercise modifications",
        "Day-by-day breakdown",
      ],
      buttonColor: "bg-blue-600 hover:bg-blue-700",
      link: "/start/personal-trainer",
      dotColor: "text-blue-500",
      popular: true,
      detailedDescription:
        "Get personalized guidance with detailed instructions for each exercise. Our expert trainers provide insights on common mistakes to avoid and modifications to suit your fitness level.",
    },
    {
      title: "7-Day Workout Plan",
      icon: <Timer className="w-6 h-6" />,
      iconBg: "bg-green-500",
      description: "Unlock Your 7-Day Workout Plan: Warm-Up, Main Workout, and Recovery",
      features: ["Clear daily sections", "Balanced workout mix", "Progressive overload guide", "Self-guided format"],
      buttonColor: "bg-green-600 hover:bg-green-700",
      link: "/start/seven-day",
      dotColor: "text-green-500",
      detailedDescription:
        "A comprehensive week-long program featuring structured warm-ups, targeted workouts, and essential recovery periods. Follow our progressive overload guide for optimal results.",
    },
    {
      title: "Fitness Journey Plan",
      icon: <Target className="w-6 h-6" />,
      iconBg: "bg-purple-500",
      description: "Transform Your Body: Long-Term Fitness goals with Phased Approach",
      features: ["Phased progression", "Clear milestone targets", "Comprehensive approach", "Mental wellness focus"],
      buttonColor: "bg-purple-600 hover:bg-purple-700",
      link: "/start/journey",
      dotColor: "text-purple-500",
      detailedDescription:
        "A long-term transformation program designed to help you achieve sustainable results. Our phased approach ensures steady progress while maintaining focus on both physical and mental wellness.",
    },
  ]

  return (
    <div className="py-16 px-4">
      <div className="max-w-7xl mx-auto animate-fade-in-slide">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 items-stretch">
          {plans.map((plan, index) => (
            <div
              key={index}
              className="bg-gray-800 bg-opacity-80 rounded-xl p-6 hover:bg-opacity-100 transition-colors border border-gray-700 flex flex-col min-h-[700px] relative"
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">Most Popular</span>
                </div>
              )}
              <div className="flex-1">
                <div className={`flex items-center justify-center w-12 h-12 ${plan.iconBg} rounded-lg mb-4`}>
                  {plan.icon}
                </div>
                <h2 className="text-xl font-bold mb-4 text-white">{plan.title}</h2>
                <p className="text-gray-300 mb-6">{plan.description}</p>
                <div
                  className="relative w-full h-32 mb-6 group"
                  onMouseEnter={() => setExpandedImage(index)}
                  onMouseLeave={() => setExpandedImage(null)}
                >
                  <Image
                    src="/ThumbNails-1.png?height=200&width=250"
                    alt={`${plan.title} preview`}
                    fill
                    className="object-cover object-center rounded-lg"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    priority={index === 0}
                  />
                  {expandedImage === index && (
                    <div
                      className={`absolute top-0 left-0 w-full h-32 bg-black bg-opacity-75 rounded-lg overflow-hidden z-50 transition-all duration-300 ease-in-out ${
                        expandedImage === index ? "opacity-100 h-96" : "opacity-0 pointer-events-none"
                      }`}
                    >
                      <Image
                        src="/ThumbNails-1.png?height=200&width=250"
                        alt={`${plan.title} expanded preview`}
                        fill
                        className={`object-cover object-center transition-transform duration-300 ease-in-out ${
                          expandedImage === index ? "scale-110" : "scale-100"
                        }`}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                      />
                      <div
                        className={`absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 p-4 transition-all duration-300 ease-in-out ${
                          expandedImage === index ? "opacity-100" : "opacity-0"
                        }`}
                      >
                        <p className="text-white text-sm">{plan.detailedDescription}</p>
                      </div>
                    </div>
                  )}
                </div>
                <ul className="space-y-3 text-sm text-gray-400">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <span className={`${plan.dotColor} mr-2`}>•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <Link
                href={plan.link}
                className={`block text-center ${plan.buttonColor} text-white rounded-lg py-3 px-4 transition-colors mt-6`}
              >
                Start Now
              </Link>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

