// components/Agents/GenerateImageAgent.ts
import { GenerateImageTool } from '../tools/generateImageTool';
import { Timestamp } from 'firebase-admin/firestore';
import { adminDb } from '../firebase-admin';

export interface GenerateImageParams {
  prompt: string;
  model?: string;
  size?: string;
  style?: string;
  quality?: string;
  format?: string;
  background?: string;
  compression?: number;
}

export interface GenerateImageResponse {
  success: boolean;
  base64Image?: string;
  error?: string;
}

export interface ImageJob {
  id: string;
  prompt: string;
  userId: string;
  status: 'initialized' | 'refining' | 'generating' | 'completed' | 'failed';
  originalPrompt: string;
  refinedPrompt?: string;
  error?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  imageUrl?: string;
  model?: string;
  size?: string;
  style?: string;
  quality?: string;
  format?: string;
  background?: string;
  compression?: number;
}

export class GenerateImageAgent {
  private readonly generateImageTool: GenerateImageTool;

  constructor(config: { generateImageTool: GenerateImageTool }) {
    this.generateImageTool = config.generateImageTool;
  }

  private getUserImagesCollection(userId: string) {
    return adminDb.collection('users').doc(userId).collection('images');
  }

  public async initializeJob(
    prompt: string,
    userId: string,
    model: string,
    size: string,
    style: string,
    quality: string = 'auto',
    format: string,
    background: string = 'auto',
    compression?: number
  ): Promise<string> {
    if (!prompt?.trim()) {
      throw new Error('Invalid prompt provided');
    }
    if (!userId?.trim()) {
      throw new Error('Invalid userId provided');
    }

    // Log the parameters received
    console.log('[GenerateImageAgent.initializeJob] Parameters:');
    console.log(`  - model: ${model}`);
    console.log(`  - size: ${size}`);
    console.log(`  - style: ${style}`);
    console.log(`  - quality: ${quality}`);
    console.log(`  - format: ${format}`);
    console.log(`  - background: ${background}`);
    console.log(`  - compression: ${compression}`);

    try {
      const jobRef = this.getUserImagesCollection(userId).doc();
      const now = Timestamp.now();

      // Create job object without undefined values
      const job: ImageJob = {
        id: jobRef.id,
        prompt: prompt.trim(),
        userId,
        status: 'initialized',
        originalPrompt: prompt.trim(),
        model,
        size,
        style,
        quality,
        format,
        background,
        createdAt: now,
        updatedAt: now
      };

      // Only add compression if it's defined
      if (compression !== undefined) {
        job.compression = compression;
      }

      await jobRef.set(job);
      return jobRef.id;

    } catch (error) {
      console.error('[GenerateImageAgent] Job initialization failed:', error);
      throw new Error(
        error instanceof Error
          ? `Failed to initialize job: ${error.message}`
          : 'Failed to initialize image generation job'
      );
    }
  }

  public async refinePrompt(jobId: string, userId: string): Promise<void> {
    if (!jobId?.trim()) {
      throw new Error('Invalid jobId provided');
    }
    if (!userId?.trim()) {
      throw new Error('Invalid userId provided');
    }

    try {
      const jobRef = this.getUserImagesCollection(userId).doc(jobId);
      const jobDoc = await jobRef.get();

      if (!jobDoc.exists) {
        throw new Error(`Job ${jobId} not found for user ${userId}`);
      }

      const job = jobDoc.data() as ImageJob;

      await jobRef.update({
        status: 'refining',
        updatedAt: Timestamp.now()
      });

      // Try to refine the prompt, but handle potential failures gracefully
      let refinedPrompt: string;
      try {
        refinedPrompt = await this.generateImageTool.refinePrompt(job.originalPrompt);
      } catch (refinementError) {
        console.warn('[GenerateImageAgent] Prompt refinement failed, using original prompt:', refinementError);
        // If refinement fails, use the original prompt
        refinedPrompt = job.originalPrompt;
      }

      await jobRef.update({
        refinedPrompt,
        status: 'generating',
        updatedAt: Timestamp.now()
      });

    } catch (error) {
      console.error('[GenerateImageAgent] Prompt refinement failed:', error);

      try {
        const jobRef = this.getUserImagesCollection(userId).doc(jobId);
        await jobRef.update({
          status: 'failed',
          error: error instanceof Error ? error.message : 'Failed to refine prompt',
          updatedAt: Timestamp.now()
        });
      } catch (updateError) {
        console.error('[GenerateImageAgent] Failed to update job status:', updateError);
      }

      throw error;
    }
  }

  public async generateImage(params: GenerateImageParams): Promise<GenerateImageResponse> {
    if (!params.prompt?.trim()) {
      return {
        success: false,
        error: 'Invalid prompt provided'
      };
    }

    try {
      // Use the provided parameters without defaults
      const model = params.model;
      const size = params.size;
      const style = params.style;
      const quality = params.quality || 'auto';
      const format = params.format || 'jpeg';
      const background = params.background || 'auto';

      console.log(`[GenerateImageAgent] Received model parameter: ${params.model}`);
      console.log(`[GenerateImageAgent] Using model: ${model}`);
      console.log(`[GenerateImageAgent] Using size: ${size}`);
      console.log(`[GenerateImageAgent] Using style: ${style}`);
      console.log(`[GenerateImageAgent] Using quality: ${quality}`);
      console.log(`[GenerateImageAgent] Using format: ${format}`);
      console.log(`[GenerateImageAgent] Using background: ${background}`);
      console.log(`[GenerateImageAgent] Attempting to generate image with ${model} model`);

      // Check if we're using an Imagen model
      if (model && model.includes('imagen')) {
        console.log('[GenerateImageAgent] Imagen model detected - this should be handled by the Imagen-specific implementation');
        // For Imagen models, we should return an error indicating that they should be handled separately
        // This will be caught by the calling code and handled appropriately
        return {
          success: false,
          error: 'Imagen models should be handled by the Imagen-specific implementation'
        };
      }

      // For OpenAI models, use the generateImageBase64 method
      const result = await this.generateImageTool.generateImageBase64(
        params.prompt.trim(),
        model,
        size,
        quality,
        format,
        background,
        params.compression
      );

      return {
        success: result.success,
        base64Image: result.base64Image,
        error: result.error
      };

    } catch (error) {
      console.error('[GenerateImageAgent] Image generation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate image'
      };
    }
  }
}