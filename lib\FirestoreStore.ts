import { CustomStoreInterface } from "./CustomStoreInterface";
import { Document } from "langchain/document";
import { adminDb } from "@/components/firebase-admin";
import { Query, CollectionReference, FieldPath, DocumentSnapshot } from "firebase-admin/firestore";

interface FirestoreStoreInput {
  collectionPath: string;
}

interface FirestoreDocument {
  content: string;
  metadata: Record<string, unknown>;
  category?: string;
  document_title?: string;
  page_number?: number;
  doc_id?: string;
}

// Define a proper type for API response chunks
interface ApiChunkResponse {
  chunkId?: string;
  pageContent?: string;
  content?: string;
  metadata?: {
    chunk_id?: string;
    [key: string]: unknown;
  };
}

// lib/fetchDocumentChunksByChunkIds.ts
interface DocumentChunk {
  chunkId: string;
  pageContent: string;
  metadata?: Record<string, unknown>;
}

export async function fetchDocumentChunksByChunkIds(chunkIds: string[], userId: string): Promise<DocumentChunk[]> {
  try {
    const response = await fetch("/api/fetchDocumentChunks", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ chunkIds, userId }),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch document chunks: ${response.statusText}`);
    }

    const data = await response.json();
    return data.chunks.map((chunk: ApiChunkResponse) => ({
      chunkId: chunk.chunkId || (chunk.metadata?.chunk_id as string) || "",
      pageContent: chunk.pageContent || chunk.content || "",
      metadata: chunk.metadata || {},
    }));
  } catch (error) {
    console.error("Error in fetchDocumentChunksByChunkIds:", error);
    throw error;
  }
}


export class FirestoreStore implements CustomStoreInterface<string, Document> {
  private readonly collectionPath: string;
  private readonly collectionRef: CollectionReference;

  constructor(fields: FirestoreStoreInput) {
    this.collectionPath = fields.collectionPath;
    this.collectionRef = adminDb.collection(this.collectionPath);
  }

  /**
   * Sets multiple key-value pairs in the Firestore collection.
   * @param keyValuePairs - An array of [key, Document] tuples.
   * @throws {Error} If the batch write operation fails
   */
  async mset(keyValuePairs: [string, Document][]): Promise<void> {
    const batch = adminDb.batch();
    
    keyValuePairs.forEach(([key, doc]) => {
      const docRef = this.collectionRef.doc(key);
      batch.set(docRef, {
        content: doc.pageContent,
        metadata: doc.metadata,
      });
    });
    
    await batch.commit();
  }

  /**
   * Converts a Firestore document snapshot to a LangChain Document
   * @param snapshot - Firestore DocumentSnapshot
   * @returns Document instance or undefined if snapshot doesn't exist
   */
  private snapshotToDocument(snapshot: DocumentSnapshot): Document | undefined {
    if (!snapshot.exists) return undefined;
    
    const data = snapshot.data() as FirestoreDocument | undefined;
    if (!data) return undefined;

    return new Document({
      pageContent: data.content || "",
      metadata: data.metadata || {},
    });
  }

  /**
   * Retrieves multiple documents based on provided keys.
   * @param keys - An array of document IDs.
   * @returns An array of Document instances or undefined if not found.
   */
  async mget(keys: string[]): Promise<(Document | undefined)[]> {
    const snapshots = await Promise.all(
      keys.map((key) => this.collectionRef.doc(key).get())
    );
    
    return snapshots.map((snapshot) => this.snapshotToDocument(snapshot));
  }

  /**
   * Retrieves multiple documents based on `doc_id` field.
   * @param docIds - An array of `doc_id` values to search.
   * @returns An array of Document instances or undefined if not found.
   */
  async mgetByDocId(docIds: string[]): Promise<Document[]> {
    const retrievedDocs = await Promise.all(
      docIds.map(async (docId) => {
        const querySnapshot = await this.collectionRef
          .where('doc_id', '==', docId)
          .get();

        if (querySnapshot.empty) {
          console.warn(`Document not found in Firestore for doc_id: ${docId}`);
          return [];
        }

        return querySnapshot.docs
          .map((docSnapshot) => this.snapshotToDocument(docSnapshot))
          .filter((doc): doc is Document => doc !== undefined);
      })
    );

    return retrievedDocs.flat();
  }

  /**
   * Retrieves all document chunks for a given base docId by appending incremental indices.
   * @param docId - The base document ID (without chunk index).
   * @returns A Promise that resolves to an array of Document instances representing the chunks.
   */
  async fetchDocumentChunksByDocId(docId: string): Promise<Document[]> {
    const chunks: Document[] = [];
    let index = 1;

    try {
      while (true) {
        const chunkId = `${docId}_${index}`;
        console.log(`Searching for document with ID: ${chunkId}`);

        const docSnapshot = await this.collectionRef.doc(chunkId).get();
        if (!docSnapshot.exists) {
          console.log(`No more chunks found for document ID: ${chunkId}`);
          break;
        }

        console.log(`Found chunk for document ID: ${chunkId}`);
        const data = docSnapshot.data() as FirestoreDocument | undefined;
        
        if (data) {
          const metadata = {
            category: data.category || "",
            chunk_id: chunkId,
            document_title: data.document_title || "",
            page_number: data.page_number || 1,
            ...data.metadata,
          };

          chunks.push(
            new Document({
              pageContent: data.content || "",
              metadata,
            })
          );
        } else {
          console.warn(`No data found in document snapshot for ID: ${chunkId}`);
        }

        index++;
      }
    } catch (error) {
      console.error(`Error fetching document chunks for doc_id ${docId}:`, error);
    }

    return chunks;
  }

  /**
   * Deletes multiple documents based on provided keys.
   * @param keys - An array of document IDs to delete.
   * @throws {Error} If the batch delete operation fails
   */
  async mdelete(keys: string[]): Promise<void> {
    const batch = adminDb.batch();
    
    keys.forEach((key) => {
      const docRef = this.collectionRef.doc(key);
      batch.delete(docRef);
    });
    
    await batch.commit();
  }

  /**
   * Asynchronously yields keys from the Firestore collection, optionally filtered by a prefix.
   * @param prefix - Optional prefix to filter keys.
   * @returns An asynchronous generator that yields document IDs.
   */
  async *yieldKeys(prefix?: string): AsyncGenerator<string> {
    let query: Query = this.collectionRef;

    if (prefix) {
      query = query
        .where(FieldPath.documentId(), ">=", prefix)
        .where(FieldPath.documentId(), "<", prefix + "\uf8ff");
    }

    const snapshot = await query.get();
    for (const doc of snapshot.docs) {
      yield doc.id;
    }
  }
}