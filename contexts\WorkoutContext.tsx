"use client"

import React, { createContext, useContext, useState, ReactNode } from "react"

interface WorkoutContextType {
  currentDay: string
  availableDays: string[]
  setCurrentDay: (day: string) => void
  setAvailableDays: (days: string[]) => void
}

const defaultContext: WorkoutContextType = {
  currentDay: "Day 1",
  availableDays: ["Day 1", "Day 3", "Day 5", "Day 7"],
  setCurrentDay: () => {},
  setAvailableDays: () => {}
}

const WorkoutContext = createContext<WorkoutContextType>(defaultContext)

export const useWorkoutContext = () => useContext(WorkoutContext)

interface WorkoutProviderProps {
  children: ReactNode
}

export const WorkoutProvider = ({ children }: WorkoutProviderProps) => {
  const [currentDay, setCurrentDay] = useState<string>(defaultContext.currentDay)
  const [availableDays, setAvailableDays] = useState<string[]>(defaultContext.availableDays)

  return (
    <WorkoutContext.Provider
      value={{
        currentDay,
        availableDays,
        setCurrentDay,
        setAvailableDays
      }}
    >
      {children}
    </WorkoutContext.Provider>
  )
}
