"use client"

import { useState, useEffect } from "react"
import { X, Search, Video } from "lucide-react"
import { collection, query, orderBy, getDocs } from "firebase/firestore"
import { db } from "@/components/firebase/config"

// Define the Exercise interface to match the structure in the Firestore database
interface Exercise {
  name: string
  focusArea: string
  description: string
  level: string
  muscleGroups: string[]
  equipment: string
  variations: string[]
  image?: string
  video?: string
  urlVideo?: string // YouTube URL
  videoId?: string // Direct YouTube video ID
  videoThumbnail?: string
  id: string
}

interface ExerciseVideoListModalProps {
  onClose: () => void
  onSelectExercise: (exercise: Exercise) => void
}

export function ExerciseVideoListModal({ onClose, onSelectExercise }: ExerciseVideoListModalProps) {
  const [exercises, setExercises] = useState<Exercise[]>([])
  const [filteredExercises, setFilteredExercises] = useState<Exercise[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [error, setError] = useState<string | null>(null)

  // Fetch exercises with videos from Firestore
  useEffect(() => {
    async function fetchExercisesWithVideos() {
      try {
        setIsLoading(true)

        // Create a query to get exercises from the Fitness Library collection
        const exerciseCollectionRef = collection(db, "Fitness Library")

        // Get all exercises and filter for those with videos
        const q = query(exerciseCollectionRef, orderBy("name"))
        const querySnapshot = await getDocs(q)

        const exercisesWithVideos: Exercise[] = []

        querySnapshot.forEach((doc) => {
          const exercise = { ...doc.data(), id: doc.id } as Exercise
          // Only include exercises that have a video URL or video ID
          if (exercise.urlVideo || exercise.videoId) {
            exercisesWithVideos.push(exercise)
          }
        })

        setExercises(exercisesWithVideos)
        setFilteredExercises(exercisesWithVideos)
      } catch (err) {
        console.error("Error fetching exercises with videos:", err)
        setError("Failed to load exercises. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchExercisesWithVideos()
  }, [])

  // Filter exercises based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredExercises(exercises)
    } else {
      const lowercaseSearch = searchTerm.toLowerCase()
      const filtered = exercises.filter(
        (exercise) =>
          exercise.name.toLowerCase().includes(lowercaseSearch) ||
          (exercise.focusArea && exercise.focusArea.toLowerCase().includes(lowercaseSearch)) ||
          (exercise.muscleGroups && exercise.muscleGroups.some(group =>
            group.toLowerCase().includes(lowercaseSearch)
          ))
      )
      setFilteredExercises(filtered)
    }
  }, [searchTerm, exercises])

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose()
      }
    }

    document.addEventListener("keydown", handleEscape)
    return () => document.removeEventListener("keydown", handleEscape)
  }, [onClose])

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4" onClick={onClose}>
      <div
        className="bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden shadow-2xl flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="sticky top-0 bg-slate-900 z-10 flex items-center justify-between p-6 border-b border-slate-700">
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <Video className="w-5 h-5" />
            Exercise Videos
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-800 rounded-full transition-colors text-gray-400 hover:text-white"
            aria-label="Close modal"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-4 border-b border-slate-700">
          <div className="relative">
            <input
              type="text"
              placeholder="Search exercises..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-slate-800 text-white px-4 py-2 pl-10 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
              >
                <X className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-2">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="w-12 h-12 rounded-full border-4 border-blue-400 border-t-transparent animate-spin" />
            </div>
          ) : error ? (
            <div className="text-center p-8 text-red-400">
              <p>{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg text-white"
              >
                Retry
              </button>
            </div>
          ) : filteredExercises.length === 0 ? (
            <div className="text-center p-8 text-gray-400">
              {searchTerm ? (
                <p>No exercises found matching &quot;{searchTerm}&quot;</p>
              ) : (
                <p>No exercises with videos available</p>
              )}
            </div>
          ) : (
            <ul className="space-y-1">
              {filteredExercises.map((exercise) => (
                <li key={exercise.id}>
                  <button
                    onClick={() => onSelectExercise(exercise)}
                    className="w-full text-left px-4 py-3 hover:bg-slate-700/50 rounded-lg transition-colors flex items-center gap-3"
                  >
                    <div className="w-8 h-8 rounded-full bg-blue-900/50 flex items-center justify-center flex-shrink-0">
                      <Video className="w-4 h-4 text-blue-300" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-white font-medium truncate">{exercise.name}</p>
                      <div className="flex items-center text-sm text-gray-400 gap-2">
                        <span>{exercise.focusArea}</span>
                        {exercise.equipment && (
                          <>
                            <span className="w-1 h-1 rounded-full bg-gray-500"></span>
                            <span>{exercise.equipment}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  )
}
