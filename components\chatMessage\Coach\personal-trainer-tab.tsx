"use client"

import { useRef, useEffect } from "react"
import { Mi<PERSON>, Mic<PERSON>ff, PhoneOff, RefreshCw } from "lucide-react"
import { useWebRTCManager } from "./WebRTCManager"
import PersonalTrainerDialogue from "./PersonalTrainerDialogue"

interface PersonalTrainerTabProps {
  chatId: string
}

export default function PersonalTrainerTab({}: PersonalTrainerTabProps) {
  const audioRef = useRef<HTMLAudioElement>(null)

  const {
    connecting,
    connected,
    isSpeaking,
    isListening,
    micActive,
    error,
    initializeRealtimeConnection,
    toggleMicrophone,
    endRealtimeConnection,
    setAudioElement,
    currentResponse,
    dialogueHistory,
  } = useWebRTCManager()

  useEffect(() => {
    if (audioRef.current) {
      setAudioElement(audioRef.current)
    }
  }, [audioRef.current, setAudioElement])

  useEffect(() => {
    console.log("[PersonalTrainerTab] dialogueHistory updated:", dialogueHistory)
  }, [dialogueHistory])

  useEffect(() => {
    console.log("[PersonalTrainerTab] currentResponse updated:", currentResponse)
  }, [currentResponse])

  useEffect(() => {
    console.log("[PersonalTrainerTab] Connection state:", { connected, isSpeaking, isListening })
  }, [connected, isSpeaking, isListening])

  useEffect(() => {
    console.log("[PersonalTrainerTab] Rendering with dialogue:", dialogueHistory)
    console.log("[PersonalTrainerTab] Rendering with currentResponse:", currentResponse)
  }, [dialogueHistory, currentResponse])

  const handleStartConnection = async () => {
    initializeRealtimeConnection()
  }

  return (
    <>
      <div className="flex flex-col h-full">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
          {/* Left side - Mic controls */}
          <div className="flex flex-col items-center justify-center p-4 h-full">
            <div className="relative w-48 h-48 mx-auto">
              <div
                className={`absolute inset-0 rounded-full bg-black/40 border-2 transition-all duration-300 ${
                  isListening
                    ? "border-green-500/50 animate-pulse glowing-border"
                    : "border-white/10"
                }`}
              >
                <div className="absolute inset-0 rounded-full bg-white/5 backdrop-blur-sm" />
                <div
                  className={`absolute inset-[-2px] rounded-full transition-all duration-300 ${
                    connecting
                      ? "bg-gradient-to-r from-yellow-500/20 via-yellow-500/20 to-yellow-500/20"
                      : connected
                        ? isSpeaking
                          ? "bg-gradient-to-r from-green-500/30 via-green-400/30 to-green-300/30 animate-gradient-pulse"
                          : "bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-green-500/20"
                        : "bg-gradient-to-r from-red-500/20 via-red-500/20 to-red-500/20"
                  }`}
                />
                <div className="absolute inset-0 flex flex-col items-center justify-center p-8">
                  {connecting ? (
                    <div className="text-white/70">
                      <div className="animate-spin rounded-full h-8 w-8 border-2 border-purple-500 border-t-transparent mx-auto mb-2" />
                      <p className="text-sm">Connecting to your Personal Trainer...</p>
                    </div>
                  ) : error ? (
                    <p className="text-red-400 text-sm">{error}</p>
                  ) : connected ? (
                    <div className="flex flex-col items-center">
                      <Mic className={`h-12 w-12 ${micActive ? "text-green-400" : "text-white/70"}`} />
                      <p className="text-white/70 text-center text-sm mt-2">
                        {isSpeaking
                          ? "Trainer speaking..."
                          : isListening
                            ? micActive
                              ? "Listening to you..."
                              : "Click microphone to speak"
                            : "Ready - Click microphone to speak"}
                      </p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <Mic className="h-12 w-12 text-white/50 mb-2" />
                      <p className="text-center text-gray-400 text-sm">Personal Trainer</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-center gap-4 mt-6">
              {connected ? (
                <>
                  <button
                    onClick={toggleMicrophone}
                    className={`${micActive ? "bg-green-600 hover:bg-green-700" : "bg-purple-600 hover:bg-purple-700"} text-white px-4 py-2 text-sm rounded-3xl min-w-[120px] flex items-center justify-center`}
                  >
                    {micActive ? <MicOff className="h-4 w-4 mr-2" /> : <Mic className="h-4 w-4 mr-2" />}
                    {micActive ? "Mute" : "Speak"}
                  </button>
                  <button
                    onClick={endRealtimeConnection}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 text-sm rounded-3xl min-w-[120px] flex items-center justify-center"
                  >
                    <PhoneOff className="h-4 w-4 mr-2" />
                    Disconnect
                  </button>
                </>
              ) : (
                <button
                  onClick={handleStartConnection}
                  className={`bg-green-600 hover:bg-green-700 text-white px-4 py-2 text-sm rounded-3xl min-w-[120px] flex items-center justify-center ${connecting ? "opacity-50 cursor-not-allowed" : ""}`}
                  disabled={connecting}
                >
                  {connecting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                      Connecting...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Connect
                    </>
                  )}
                </button>
              )}
            </div>

            {connected && (
              <div className="mt-4 flex justify-center items-center">
                <div
                  className={`h-3 w-3 rounded-full mr-2 ${isSpeaking ? "bg-green-500 animate-pulse" : "bg-gray-500"}`}
                ></div>
                <span className="text-sm text-gray-400">
                  {isSpeaking ? "Trainer speaking..." : isListening ? "Listening..." : "Ready"}
                </span>
              </div>
            )}

            <audio ref={audioRef} autoPlay />
          </div>

          {/* Right side - Dialogue transcript with independent scrolling */}
          <div className="flex flex-col h-full border-l border-white/10">
            <div className="flex-1 overflow-y-auto" style={{ maxHeight: "calc(100vh - 2rem)" }}>
              <PersonalTrainerDialogue
                dialogue={dialogueHistory}
                currentResponse={currentResponse}
                isSpeaking={isSpeaking}
                isListening={isListening}
                connected={connected}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Global CSS for animations and glow */}
      <style jsx global>{`
        @keyframes gradient-pulse {
          0% {
            opacity: 0.7;
          }
          50% {
            opacity: 1;
          }
          100% {
            opacity: 0.7;
          }
        }

        @keyframes glow {
          0% {
            box-shadow: 0 0 5px rgba(74, 222, 128, 0.5);
          }
          50% {
            box-shadow: 0 0 15px rgba(74, 222, 128, 0.7);
          }
          100% {
            box-shadow: 0 0 5px rgba(74, 222, 128, 0.5);
          }
        }

        .animate-gradient-pulse {
          animation: gradient-pulse 2s infinite;
        }

        .glowing-border {
          animation: glow 1.5s infinite;
        }
      `}</style>
    </>
  )
}