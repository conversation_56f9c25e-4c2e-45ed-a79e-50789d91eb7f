'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { doc, setDoc } from 'firebase/firestore'
import { db, auth } from '@/components/firebase'

export default function FinalStep() {
  const [firstName, setFirstName] = useState('')
  const [surname, setSurname] = useState('')
  const [city, setCity] = useState('')
  const [country, setCountry] = useState('')
  const router = useRouter()

  useEffect(() => {
    const user = auth.currentUser
    if (user) {
      setFirstName(user.displayName?.split(' ')[0] || '')
      setSurname(user.displayName?.split(' ').slice(1).join(' ') || '')
    }
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const user = auth.currentUser
    if (!user) {
      console.error('No user found')
      return
    }

    try {
      const userType = localStorage.getItem('userType')
      const experienceLevel = localStorage.getItem('experienceLevel')
      const fitnessGoals = JSON.parse(localStorage.getItem('fitnessGoals') || '[]')
      const workoutFrequency = localStorage.getItem('workoutFrequency')
      const availableTime = localStorage.getItem('availableTime')
      const equipmentAccess = localStorage.getItem('equipmentAccess')
      const certifications = JSON.parse(localStorage.getItem('certifications') || '[]')
      const specializations = JSON.parse(localStorage.getItem('specializations') || '[]')
      const clientExperience = localStorage.getItem('clientExperience')

      const userData = {
        email: user.email,
        firstName,
        surname,
        city,
        country,
        userType,
        experienceLevel,
        fitnessGoals,
        workoutFrequency,
        availableTime,
        equipmentAccess,
        createdAt: new Date(),
        profileComplete: true,
        ...(userType === 'Fitness Coach/Trainer' && {
          certifications,
          specializations,
          clientExperience,
        }),
      }

      await setDoc(doc(db, 'IF_users', user.uid), userData)

      // Clear localStorage
      localStorage.clear()

      // Redirect to dashboard or home page
      router.push('/dashboard')
    } catch (error) {
      console.error('Error saving user data:', error)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Complete Your Profile
          </h2>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="firstName" className="sr-only">
                First Name
              </label>
              <input
                id="firstName"
                name="firstName"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="First Name"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="surname" className="sr-only">
                Surname
              </label>
              <input
                id="surname"
                name="surname"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Surname"
                value={surname}
                onChange={(e) => setSurname(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="city" className="sr-only">
                City
              </label>
              <input
                id="city"
                name="city"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="City"
                value={city}
                onChange={(e) => setCity(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="country" className="sr-only">
                Country
              </label>
              <input
                id="country"
                name="country"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Country"
                value={country}
                onChange={(e) => setCountry(e.target.value)}
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Complete Registration
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

