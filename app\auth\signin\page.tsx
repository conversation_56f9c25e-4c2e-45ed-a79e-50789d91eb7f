'use client'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { signInWithPopup, GoogleAuthProvider, AuthError } from 'firebase/auth'
import { auth } from '@/components/firebase/config'
import { LogIn } from 'lucide-react'

export default function SignIn() {
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const provider = new GoogleAuthProvider()
      provider.setCustomParameters({
        prompt: 'select_account'
      })
      
      const result = await signInWithPopup(auth, provider)
      
      if (result.user) {
        // Optional: Check if user exists in your database
        router.push('/')
      }
    } catch (error) {
      const authError = error as AuthError
      console.error('Sign in error:', authError)
      
      // More specific error messages based on error code
      switch (authError.code) {
        case 'auth/configuration-not-found':
          setError('Authentication configuration error. Please contact support.')
          break
        case 'auth/popup-blocked':
          setError('Pop-up was blocked by your browser. Please enable pop-ups for this site.')
          break
        case 'auth/popup-closed-by-user':
          setError('Sign-in was cancelled. Please try again.')
          break
        case 'auth/unauthorized-domain':
          setError('This domain is not authorized for sign-in. Please contact support.')
          break
        default:
          setError('Failed to sign in with Google. Please try again.')
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to IntelligentFitness
          </h2>
        </div>
        <button
          onClick={handleGoogleSignIn}
          disabled={isLoading}
          className={`group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
            isLoading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          <span className="absolute left-0 inset-y-0 flex items-center pl-3">
            <LogIn className="h-5 w-5 text-blue-500 group-hover:text-blue-400" aria-hidden="true" />
          </span>
          {isLoading ? 'Signing in...' : 'Sign in with Google'}
        </button>
        {error && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">{error}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}