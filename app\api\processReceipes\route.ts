import { NextRequest, NextResponse } from "next/server";
import {generateDocs } from "@/components/tools/generateDocs";
import { setDoc, doc } from "firebase/firestore";
import { db } from "@/components/firebase/config";
import { OpenAIEmbeddings } from "@langchain/openai";
import { Pinecone } from "@pinecone-database/pinecone";
import { getRecipeBreakdown } from "@/components/getRecipeBreakdown";
import { performSemanticCheck } from "@/components/tools/semanticCheck";


/**
 * Type for metadata values that can be stored in Pinecone
 */
type MetadataValue = string | number | boolean | string[] | number[];

/**
 * Type for input metadata that might contain null or undefined values
 */
type InputMetadataValue = MetadataValue | null | undefined;

/**
 * Cleans the metadata object by removing fields with null or undefined values.
 * This prevents Pinecone from storing unwanted empty or invalid fields.
 * @param metadata - The metadata object to clean.
 * @returns A cleaned metadata object with only valid fields.
 */
function cleanMetadata(metadata: Record<string, InputMetadataValue>): Record<string, MetadataValue> {
  const cleaned: Record<string, MetadataValue> = {};

  for (const key in metadata) {
    if (
      metadata[key] !== null &&
      metadata[key] !== undefined &&
      metadata[key] !== "" &&
      metadata[key] !== "N/A"
    ) {
      cleaned[key] = metadata[key];
    }
  }

  return cleaned;
}

export async function POST(request: NextRequest) {
  let body: { docId: string; userId: string; category: string; fileName: string };

  try {
    body = await request.json();
  } catch (parseError) {
    console.error("Error parsing request body:", parseError);
    return NextResponse.json(
      { success: false, error: "Invalid JSON format." },
      { status: 400 }
    );
  }

  const { docId, userId, category, fileName } = body;

  if (!docId || !userId || !category || !fileName) {
    return NextResponse.json(
      { success: false, error: "Missing required parameters." },
      { status: 400 }
    );
  }

  try {
    // Step 1: Generate document chunks and assign metadata
    const docsWithMetadata = await generateDocs(userId, docId);

    // Step 2: Combine text from all chunks
    const combinedText = docsWithMetadata.map((doc) => doc.pageContent).join("\n\n");

    // Step 3: We'll use processWithGroq instead of initializing ChatGroq directly
    // The model will be used via the performSemanticCheck function and getRecipeBreakdown

    // Step 4: Perform semantic check to identify if it's a recipe
    const isActuallyRecipe = await performSemanticCheck(combinedText);

    let finalCategory = category;
    // Initialize with empty object to avoid "used before assigned" errors
    let validatedData: Record<string, string | number | boolean | null | undefined> = {};

    if (isActuallyRecipe) {
      console.log("The file content appears to be a recipe despite the original category.");
      finalCategory = "Recipes"; // Update the final category to "Recipes"
    }

    console.log ("is recipes ? ", finalCategory)

    // Step 5: Process based on the (possibly updated) category
    if (finalCategory === "Recipes") {
      const prompt = `You are an expert nutritionist tasked with breaking down the recipe ${combinedText}
      into key components. Given the following {recipe} and list of ingredients, calculate
      the estimated caloric, nutrient, and macronutrient breakdown per serving. Assume the recipe serves
      4 people. Include the breakdown of calories, protein, fat, saturated fat, carbohydrates, fiber,
      sugar, sodium, and any key vitamins and minerals. Present the macronutrient breakdown as both grams
      and percentage of total calories, and list other important nutrients such as Vitamin A, Vitamin C, Calcium,
      Iron, and Potassium. If any of the properties are not available from the provided recipe, retrieve
      the answer from your knowledge base. Finally add a 'createdAt' timestamp. `;

      const result = await getRecipeBreakdown(prompt);
      console.log('Result from getRecipeBreakdown:', result); // For debugging

      if (result) {
        validatedData = result.json; // Use the LLM response JSON directly
        console.log("Validated Recipe Data:", validatedData);
      } else {
        throw new Error("Failed to retrieve recipe breakdown.");
      }
    }

    console.log("AI-Generated Structured Response:", validatedData);

      // Step 6: Save structured data to Firestore
      if (finalCategory === "Recipes") {
        const now = new Date();
        const formattedDate = now.toLocaleDateString('en-GB'); // Formats date as "dd/mm/yyyy"

        console.log(`Saving recipe to foodKhare_users/${userId}/Recipes/${docId}`);

        await setDoc(doc(db, "foodKhare_users", userId, "Recipes", docId), {
          ...validatedData, // Spread existing validated data
          createdAt: formattedDate, // Add the current date in "dd/mm/yyyy" format
        });
      } else {
        const now = new Date();
        const formattedDate = now.toLocaleDateString('en-GB'); // Formats date as "dd/mm/yyyy"

        await setDoc(doc(db, "foodKhare_users", userId, "Clients", docId), {
          ...validatedData, // Spread existing validated data
          createdAt: formattedDate, // Add the current date in "dd/mm/yyyy" format
        });
      }

    // Step 7: Initialize OpenAIEmbeddings
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY!,
    });

    console.log("OpenAIEmbeddings initialized.");

    // Step 8: Generate embeddings
    const summaryEmbedding = await embeddings.embedQuery(JSON.stringify(validatedData));
    console.log("Embeddings generated.");

    // Step 9: Initialize Pinecone Client
    const pinecone = new Pinecone();
    const pineconeIndex = pinecone.Index(process.env.PINECONE_NUTRITION_INDEX!);
    console.log("Pinecone client initialized.");

    // Step 10: Prepare metadata for Pinecone (always use the initial `category` from request)
    const metadataForPinecone = {
      summary: JSON.stringify(validatedData),
      documentId: docId,
      document_title: fileName,
      category: category, // Always use the initial category value
      doc_id: docId,
      // Include specific fields for Recipes only if the finalCategory is "Recipes"
      ...(finalCategory === "Recipes"
        ? {
            "Recipe Name": validatedData["Recipe Name"],
            Ingredients: validatedData["Ingredients"],
            "Protein (g)": validatedData["Protein (g)"],
            "Fat (g)": validatedData["Fat (g)"],
            "Carbohydrates (g)": validatedData["Carbohydrates (g)"],
            "Total Calories (kCal)": validatedData["Total Calories (kCal)"],
            "Fiber (g)": validatedData["Fiber (g)"],
            "Sugar (g)": validatedData["Sugar (g)"],
            "Sodium (mg)": validatedData["Sodium (mg)"],
            "Carbohydrates Breakdown": validatedData["Carbohydrates Breakdown"],
            "Protein Breakdown": validatedData["Protein Breakdown"],
            "Fat Breakdown": validatedData["Fat Breakdown"],
            "Vitamin A (% DV)": validatedData["Vitamin A (% DV)"],
            "Vitamin B (% DV)": validatedData["Vitamin B (% DV)"],
            "Vitamin C (% DV)": validatedData["Vitamin C (% DV)"],
            "Vitamin D (% DV)": validatedData["Vitamin D (% DV)"],
            "Vitamin E (% DV)": validatedData["Vitamin E (% DV)"],
            "Calcium (% DV)": validatedData["Calcium (% DV)"],
            "Iron (% DV)": validatedData["Iron (% DV)"],
            "Potassium (% DV)": validatedData["Potassium (% DV)"],
          }
        : {
            clientName: validatedData.clientName,
            age: validatedData.age,
            bmi: validatedData.bmi,
            bodyFatPercentage: validatedData.bodyFatPercentage,
            muscleMass: validatedData.muscleMass,
            activityLevel: validatedData.activityLevel,
            recommendedCalories: validatedData.recommendedCalories,
            waistCircumference: validatedData.waistCircumference,
            hipCircumference: validatedData.hipCircumference,
            waistToHipRatio: validatedData.waistToHipRatio,
            bmr: validatedData.bmr,
            hydrationNeeds: validatedData.hydrationNeeds,
            specialConsiderations: validatedData.specialConsiderations,
            otherDetails: validatedData.otherDetails,
          }),
    };

    console.log("Metadata prepared for Pinecone.");

    // Step 11: Clean metadata
    const cleanedMetadata = cleanMetadata(metadataForPinecone);
    console.log("Metadata cleaned.");

    // Step 12: Upsert to Pinecone
    // Convert all number arrays to string arrays for Pinecone compatibility
    const pineconeMetadata: Record<string, string | number | boolean | string[]> = {};

    // Convert any number arrays to string arrays
    for (const [key, value] of Object.entries(cleanedMetadata)) {
      if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'number') {
        pineconeMetadata[key] = value.map(v => String(v));
      } else {
        pineconeMetadata[key] = value as string | number | boolean | string[];
      }
    }

    await pineconeIndex.namespace(docId).upsert([
      {
        id: docId,
        values: summaryEmbedding,
        metadata: pineconeMetadata,
      },
    ]);

    console.log(`Successfully upserted summary and metadata for document ID: ${docId} into Pinecone under namespace ${docId}`);

    // Log success message
    console.log(`Successfully processed and saved document ID: ${docId} for user: ${userId} in category: ${finalCategory}`);

    // Respond success
    return NextResponse.json({ success: true });

  } catch (error: unknown) {
    console.error("Error processing file:", error);

    const errorMessage = error instanceof Error ? error.message : "Unknown error";

    // Attempt to save fallback metadata using the parsed body
    try {
      await setDoc(doc(db, "foodKhare_users", userId, "MetadataFallback", docId), {
        documentId: docId,
        document_title: fileName,
        category: category,
        error: errorMessage,
        createdAt: new Date(),
      });
    } catch (fallbackError) {
      console.error("Error saving fallback metadata:", fallbackError);
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
