"use client";

import { useState, useEffect, Suspense, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import type { User } from "firebase/auth";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { auth, db } from "@/components/firebase/config";
import type { StepData, HeightUnit, WeightUnit, Gender } from "@/types/measurements";
import { convertHeight, convertWeight } from "@/utils/unit-conversions";
import { ProfileSummaryDialog } from "@/components/ProfileSummaryDialog";
import { generateProfileText } from "@/utils/profile-utils";
import Image from "next/image";
import { motion } from "framer-motion";

const isProfileComplete = (data: StepData): boolean => {
  return (
    !!data.basic_info.height.value &&
    !!data.basic_info.weight.value &&
    !!data.basic_info.age &&
    !!data.basic_info.gender &&
    !!data.fitness_goals.primary &&
    !!data.fitness_goals.secondary &&
    !!data.workout_preferences.frequency &&
    !!data.workout_preferences.duration &&
    !!data.workout_preferences.preferred_time &&
    !!data.health_information.medical_conditions &&
    !!data.health_information.injuries &&
    !!data.preferences.training_location &&
    (data.preferences.focus_areas?.length || 0) > 0 &&
    !!data.preferences.sport_specific
  );
};

function ProfileForm() {
  // Component State Management
  const mounted = useRef(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [dataLoading, setDataLoading] = useState(true);
  const [showSummary, setShowSummary] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  // Router and Navigation
  const router = useRouter();
  const searchParams = useSearchParams();

  // Form Data Management
  const [formData, setFormData] = useState<StepData>({
    basic_info: {
      height: { value: "", unit: "ft" },
      weight: { value: "", unit: "kg" },
      age: "",
      gender: "prefer-not-to-say",
    },
    fitness_goals: { primary: "", secondary: "" },
    workout_preferences: { frequency: "", duration: "", preferred_time: "" },
    health_information: { medical_conditions: "", injuries: "" },
    preferences: {
      training_location: "gym",
      focus_areas: [],
      sport_specific: "",
    },
  });

  // Form Step Management
  const validSteps = ["basic-info", "fitness-goals", "workout-preferences", "health-information", "preferences"];
  const step = searchParams.get("step");
  const currentStep = validSteps.includes(step || "") ? step : "basic-info";

  // Authentication and Data Initialization
  useEffect(() => {
    mounted.current = true;
    let unsubscribe: () => void;

    const initializeForm = async () => {
      if (!mounted.current) return;

      try {
        unsubscribe = auth.onAuthStateChanged(async (currentUser) => {
          if (!mounted.current) return;

          if (currentUser) {
            setUser(currentUser);
            setLoading(false);
            await fetchExistingData(currentUser);
          } else {
            router.push("/auth/signin");
          }
        });
      } catch (error) {
        console.error("Error during initialization:", error);
        if (mounted.current) {
          setLoading(false);
        }
      }
    };

    initializeForm();

    return () => {
      mounted.current = false;
      unsubscribe?.();
    };
  }, [router]);

  // Data Fetching Function
  const fetchExistingData = async (user: User) => {
    if (!mounted.current) return;

    setDataLoading(true);
    try {
      const fitnessDocRef = doc(db, `IF_users/${user.email}/Profile/fitness`);
      const fitnessDocSnap = await getDoc(fitnessDocRef);

      if (!mounted.current) return;

      if (fitnessDocSnap.exists()) {
        const data = fitnessDocSnap.data();
        const transformedData: StepData = {
          basic_info: {
            height: {
              value: data.basic_info?.height?.value || "",
              unit: data.basic_info?.height?.unit || "cm",
            },
            weight: {
              value: data.basic_info?.weight?.value || "",
              unit: data.basic_info?.weight?.unit || "kg",
            },
            age: data.basic_info?.age || "",
            gender: data.basic_info?.gender || "prefer-not-to-say",
          },
          fitness_goals: {
            primary: data.fitness_goals?.primary || "",
            secondary: data.fitness_goals?.secondary || "",
          },
          workout_preferences: {
            frequency: data.workout_preferences?.frequency || "",
            duration: data.workout_preferences?.duration || "",
            preferred_time: data.workout_preferences?.preferred_time || "",
          },
          health_information: {
            medical_conditions: data.health_information?.medical_conditions || "",
            injuries: data.health_information?.injuries || "",
          },
          preferences: {
            training_location: data.preferences?.training_location || "gym",
            focus_areas: data.preferences?.focus_areas || [],
            sport_specific: data.preferences?.sport_specific || "",
          },
        };
        setFormData(transformedData);
        setIsComplete(isProfileComplete(transformedData));
      } else {
        const accountDocRef = doc(db, "IF_Accounts", user.uid);
        const accountDocSnap = await getDoc(accountDocRef);

        if (!mounted.current) return;

        if (accountDocSnap.exists()) {
          const data = accountDocSnap.data();
          setFormData((prevData) => ({
            ...prevData,
            ...data,
          }));
        }
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    } finally {
      if (mounted.current) {
        setDataLoading(false);
      }
    }
  };

  // Unit Conversion Handler
  const handleUnitChange = (field: "height" | "weight", newUnit: HeightUnit | WeightUnit) => {
    const currentValue = formData.basic_info[field].value;
    if (!currentValue) {
      setFormData((prev) => ({
        ...prev,
        basic_info: {
          ...prev.basic_info,
          [field]: {
            ...prev.basic_info[field],
            unit: newUnit,
          },
        },
      }));
      return;
    }

    const currentUnit = formData.basic_info[field].unit;
    const numericValue = Number.parseFloat(currentValue);

    if (isNaN(numericValue)) return;

    let convertedValue: number;
    if (field === "height") {
      convertedValue = convertHeight(numericValue, currentUnit as HeightUnit, newUnit as HeightUnit);
    } else {
      convertedValue = convertWeight(numericValue, currentUnit as WeightUnit, newUnit as WeightUnit);
    }

    setFormData((prev) => ({
      ...prev,
      basic_info: {
        ...prev.basic_info,
        [field]: {
          value: convertedValue.toString(),
          unit: newUnit,
        },
      },
    }));
  };

  // Form Input Handler
  const handleInputChange = (section: keyof StepData, field: string, value: string | string[] | boolean) => {
    if (section === "basic_info" && (field === "height" || field === "weight")) {
      setFormData((prev) => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: {
            ...prev[section][field],
            value,
          },
        },
      }));
    } else if (section === "preferences" && field === "focus_areas") {
      setFormData((prev) => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: Array.isArray(value) ? value : [],
        },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: value,
        },
      }));
    }
  };

  // Form Submission Handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user?.email) return;

    setIsProcessing(true);
    try {
      const currentStepKey = currentStep?.replace("-", "_") as keyof StepData;
      const fitnessDocRef = doc(db, `IF_users/${user.email}/Profile/fitness`);

      await setDoc(
        fitnessDocRef,
        {
          [currentStepKey]: formData[currentStepKey],
        },
        { merge: true },
      );

      const currentIndex = validSteps.indexOf(currentStep || "");
      if (currentIndex < validSteps.length - 1) {
        const nextStep = validSteps[currentIndex + 1];
        router.push(`/profile/complete?step=${nextStep}`);
      } else {
        const profileText = generateProfileText(formData);

        // Get initial summary from /api/summarizeProfile
        const summaryResponse = await fetch("/api/summarizeProfile", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${await auth.currentUser?.getIdToken()}`, // Best practice
          },
          body: JSON.stringify({
            userEmail: user.email,
            profileText: profileText,
          }),
        });

        if (!summaryResponse.ok) {
          const error = await summaryResponse.json();
          throw new Error(error.details || error.error || "Failed to generate summary");
        }

        setIsComplete(true);
        setShowSummary(true); // Opens ProfileSummaryDialog with autoPlayAudio={true}
      }
    } catch (error) {
      console.error("Error saving profile data:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  // Navigation Handler
  const handleBack = () => {
    const currentIndex = validSteps.indexOf(currentStep || "");
    if (currentIndex > 0) {
      const previousStep = validSteps[currentIndex - 1];
      router.push(`/profile/complete?step=${previousStep}`);
    } else {
      router.push("/dashboard");
    }
  };

  // Loading State UI
  if (loading || dataLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 flex items-center justify-center">
        <div className="bg-black p-8 rounded-lg shadow-md">
          <div className="flex items-center space-x-4">
            <div className="w-6 h-6 border-4 border-purple-600 border-t-transparent rounded-full animate-spin"></div>
            <p className="text-gray-300">{loading ? "Initializing..." : "Loading profile data..."}</p>
          </div>
        </div>
      </div>
    );
  }

  // Unauthenticated State
  if (!user) {
    router.push("/auth/signin");
    return null;
  }

  // Form Content Renderer
  const renderStepContent = () => {
    switch (currentStep) {
      case "basic-info":
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="col-span-2">
                <label htmlFor="height" className="block text-sm font-medium text-gray-300">
                  Height
                </label>
                <input
                  type="number"
                  id="height"
                  value={formData.basic_info.height.value}
                  onChange={(e) => handleInputChange("basic_info", "height", e.target.value)}
                  className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                  required
                />
              </div>
              <div>
                <label htmlFor="height-unit" className="block text-sm font-medium text-gray-300">
                  Unit
                </label>
                <select
                  id="height-unit"
                  value={formData.basic_info.height.unit}
                  onChange={(e) => handleUnitChange("height", e.target.value as HeightUnit)}
                  className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                >
                  <option value="cm">cm</option>
                  <option value="ft">ft</option>
                  <option value="m">m</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="col-span-2">
                <label htmlFor="weight" className="block text-sm font-medium text-gray-300">
                  Weight
                </label>
                <input
                  type="number"
                  id="weight"
                  value={formData.basic_info.weight.value}
                  onChange={(e) => handleInputChange("basic_info", "weight", e.target.value)}
                  className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                  required
                />
              </div>
              <div>
                <label htmlFor="weight-unit" className="block text-sm font-medium text-gray-300">
                  Unit
                </label>
                <select
                  id="weight-unit"
                  value={formData.basic_info.weight.unit}
                  onChange={(e) => handleUnitChange("weight", e.target.value as WeightUnit)}
                  className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                >
                  <option value="kg">kg</option>
                  <option value="lbs">lbs</option>
                  <option value="st">st</option>
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="age" className="block text-sm font-medium text-gray-300">
                Age
              </label>
              <input
                type="number"
                id="age"
                value={formData.basic_info.age}
                onChange={(e) => handleInputChange("basic_info", "age", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                required
              />
            </div>

            <div>
              <label htmlFor="gender" className="block text-sm font-medium text-gray-300">
                Gender
              </label>
              <select
                id="gender"
                value={formData.basic_info.gender}
                onChange={(e) => handleInputChange("basic_info", "gender", e.target.value as Gender)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                required
              >
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
                <option value="prefer-not-to-say">Prefer not to say</option>
              </select>
            </div>
          </div>
        );

      case "fitness-goals":
        return (
          <div className="space-y-4 text-gray-300">
            <div>
              <label htmlFor="primary-goal" className="block text-sm font-medium">
                Primary Goal
              </label>
              <select
                id="primary-goal"
                value={formData.fitness_goals.primary}
                onChange={(e) => {
                  handleInputChange("fitness_goals", "primary", e.target.value);
                  if (e.target.value === formData.fitness_goals.secondary) {
                    handleInputChange("fitness_goals", "secondary", "");
                  }
                }}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                required
              >
                <option value="">Select a goal</option>
                <option value="weight-loss">Weight Loss</option>
                <option value="muscle-gain">Muscle Gain</option>
                <option value="endurance">Improve Endurance</option>
                <option value="strength">Increase Strength</option>
                <option value="flexibility">Enhance Flexibility</option>
              </select>
            </div>
            <div>
              <label htmlFor="secondary-goal" className="block text-sm font-medium">
                Secondary Goal
              </label>
              <select
                id="secondary-goal"
                value={formData.fitness_goals.secondary}
                onChange={(e) => handleInputChange("fitness_goals", "secondary", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                required
              >
                <option value="">Select a goal</option>
                {["weight-loss", "muscle-gain", "endurance", "strength", "flexibility"]
                  .filter((goal) => goal !== formData.fitness_goals.primary)
                  .map((goal) => (
                    <option key={goal} value={goal}>
                      {goal
                        .split("-")
                        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                        .join(" ")}
                    </option>
                  ))}
              </select>
            </div>
          </div>
        );

      case "workout-preferences":
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="frequency" className="block text-sm font-medium text-gray-300">
                Workout Frequency
              </label>
              <select
                id="frequency"
                value={formData.workout_preferences.frequency}
                onChange={(e) => handleInputChange("workout_preferences", "frequency", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                required
              >
                <option value="">Select frequency</option>
                <option value="1-2">1-2 times per week</option>
                <option value="3-4">3-4 times per week</option>
                <option value="5+">5+ times per week</option>
              </select>
            </div>
            <div>
              <label htmlFor="duration" className="block text-sm font-medium text-gray-300">
                Preferred Workout Duration
              </label>
              <select
                id="duration"
                value={formData.workout_preferences.duration}
                onChange={(e) => handleInputChange("workout_preferences", "duration", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                required
              >
                <option value="">Select duration</option>
                <option value="15-30">15-30 minutes</option>
                <option value="30-60">30-60 minutes</option>
                <option value="60+">60+ minutes</option>
              </select>
            </div>
            <div>
              <label htmlFor="preferred-time" className="block text-sm font-medium text-gray-300">
                Preferred Workout Time
              </label>
              <select
                id="preferred-time"
                value={formData.workout_preferences.preferred_time}
                onChange={(e) => handleInputChange("workout_preferences", "preferred_time", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                required
              >
                <option value="">Select preferred time</option>
                <option value="morning">Morning</option>
                <option value="afternoon">Afternoon</option>
                <option value="evening">Evening</option>
              </select>
            </div>
          </div>
        );

      case "health-information":
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="medical-conditions" className="block text-sm font-medium text-gray-300">
                Medical Conditions
              </label>
              <textarea
                id="medical-conditions"
                value={formData.health_information.medical_conditions}
                onChange={(e) => handleInputChange("health_information", "medical_conditions", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                rows={3}
                placeholder="List any medical conditions that may affect your workout routine"
              ></textarea>
            </div>
            <div>
              <label htmlFor="injuries" className="block text-sm font-medium text-gray-300">
                Injuries
              </label>
              <textarea
                id="injuries"
                value={formData.health_information.injuries}
                onChange={(e) => handleInputChange("health_information", "injuries", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                rows={3}
                placeholder="List any current or past injuries that may affect your workout routine"
              ></textarea>
            </div>
          </div>
        );
      case "preferences":
        return (
          <div className="space-y-4">
            <div>
              <label htmlFor="training-location" className="block text-sm font-medium text-gray-300">
                Training Location
              </label>
              <select
                id="training-location"
                value={formData.preferences.training_location}
                onChange={(e) => handleInputChange("preferences", "training_location", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                required
              >
                <option value="gym">Gym</option>
                <option value="home">Home</option>
                <option value="outdoor">Outdoor</option>
              </select>
            </div>
            <div>
              <label htmlFor="focus-areas" className="block text-sm pt-2 font-medium text-gray-300">
                Focus Areas - Multiple selections allowed
              </label>
              <select
                id="focus-areas"
                value={formData.preferences.focus_areas}
                onChange={(e) =>
                  handleInputChange(
                    "preferences",
                    "focus_areas",
                    Array.from(e.target.selectedOptions).map((option) => option.value),
                  )
                }
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
                multiple
              >
                <option value="Arms">Arms</option>
                <option value="Legs">Legs</option>
                <option value="Shoulders">Shoulders</option>
                <option value="Chest">Chest</option>
                <option value="Back">Back</option>
                <option value="Core">Core</option>
                <option value="Full Body">Full Body</option>
              </select>
            </div>
            <div>
              <label htmlFor="sport-specific" className="block text-sm pt-2 font-medium text-gray-300">
                Sport Specific Training
              </label>
              <input
                type="text"
                id="sport-specific"
                value={formData.preferences.sport_specific}
                onChange={(e) => handleInputChange("preferences", "sport_specific", e.target.value)}
                className="mt-1 pl-2 block w-full rounded-md border-gray-600 bg-gray-800 shadow-sm focus:ring-purple-500 focus:border-purple-500 sm:text-sm text-gray-300"
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Main Component Render
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 overflow-hidden">
      {/* Background Image */}
      <Image
        src="/bg-websiteMain.png?height=1080&width=1920"
        alt="Fitness background"
        width={1920}
        height={1080}
        className="absolute inset-0 w-full h-full object-cover opacity-60"
        priority
      />
      <main className="relative container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl mx-auto"
        >
          <h1 className="text-3xl font-bold mb-6 text-white">Complete Your Profile</h1>
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-black/60 rounded-lg shadow-xl p-6 backdrop-blur-md border border-purple-500/20"
          >
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2 text-white">
                {currentStep
                  ?.split("-")
                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(" ")}
              </h2>
              <div className="h-2 bg-gray-800 rounded-full">
                <div
                  className="h-full bg-purple-600 rounded-full transition-all duration-300"
                  style={{
                    width: `${((validSteps.indexOf(currentStep || "") + 1) / validSteps.length) * 100}%`,
                  }}
                />
              </div>
            </div>
            <form onSubmit={handleSubmit}>
              {renderStepContent()}
              <div className="mt-6 flex justify-between">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  type="button"
                  onClick={handleBack}
                  className="px-4 py-2 text-sm font-medium text-white bg-gray-700 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Back
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  type="submit"
                  className="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
                >
                  {currentStep === "preferences" ? "Complete Profile" : "Next Step"}
                </motion.button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      </main>
      {isComplete && (
        <div className="relative flex justify-center">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowSummary(true)}
            className="mt-4 px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            View Profile Summary
          </motion.button>
        </div>
      )}
      {/* Profile Summary Dialog */}
      <ProfileSummaryDialog
        isOpen={showSummary}
        onClose={() => {
          setShowSummary(false);
          router.push("/dashboard");
        }}
        userEmail={user?.email || ""}
      />
      {isProcessing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-lg">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-center">Processing your profile...</p>
          </div>
        </div>
      )}
    </div>
  );
}

// Export the page component wrapped in Suspense
export default function ProfileCompletePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProfileForm />
    </Suspense>
  );
}