// processMessages.ts - Properly using FALLBACK_MODELS
import { convertPromptToVector } from "./vectorUtils";
import { getUserNamespace, getAssessmentSummary } from "@/utils/firebase";
import { getFirestore, collection, doc, getDoc, getDocs, query, orderBy, limit } from "firebase/firestore";
import { fetchDocumentChunksByChunkIds } from "@/lib/fetchDocumentChunksByChunkIds";
import { callGroqAiEx } from "@/components/groqClient";
import { getRelevantHistory } from "./getRelevantHistory";
import { Pinecone } from "@pinecone-database/pinecone";

// Define available Groq models in order of preference
const PRIMARY_MODEL = process.env.STREAMING_GROQ_MODEL || "llama-3.3-70b-versatile";
const FALLBACK_MODELS = [
  "meta-llama/llama-4-maverick-17b-128e-instruct",
  "deepseek-r1-distill-llama-70b",
  "mixtral-8x7b-32768"
];

// Initialize Pinecone client
const pinecone = new Pinecone({
  apiKey: process.env.PINECONE_API_KEY || "3b176b1d-1ed2-437b-8346-0f497c138048", // Moved to environment variable
});
const pineconeIndex = pinecone.Index("intelligentfitness");

// Message type definitions
export interface ChatMessage {
  id?: string;
  role: "user" | "assistant";
  content: string;
  timestamp: string;
}

interface MessageProcessingCallbacks {
  onChunkReceived: (content: string) => void;
  onError: (error: Error | string) => void;
  onComplete: (finalContent: string) => Promise<void>;
}

// Define types for workout plan data
interface Exercise {
  name?: string;
  sets?: number;
  reps?: number;
  weight?: number;
  [key: string]: unknown;
}

interface DayWorkout {
  workoutType?: string;
  exercises?: Exercise[];
  [key: string]: unknown;
}

type DayData = Exercise[] | DayWorkout | string | unknown;

interface ProcessMessagesParams {
  userId: string;
  chatId: string;
  messageText: string;
  callbacks: MessageProcessingCallbacks;
  isMounted: React.RefObject<boolean>;
}

/**
 * Validates required environment variables on startup
 * Should be called during application initialization
 */
export function validateEnvironmentVariables() {
  const requiredEnvVars = [
    'NEXT_PUBLIC_GROQ_API_KEY',
    'PINECONE_API_KEY'
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error(`Missing required environment variables: ${missingVars.join(', ')}`);
    // Consider showing an admin-only warning in the UI
  }
}

/**
 * Fetches recent chat history from Firestore
 *
 * @param userId - The user's ID
 * @param chatId - The chat ID
 * @param messageLimit - Maximum number of messages to retrieve (default: 4)
 * @returns A formatted string containing the chat history
 */
async function fetchChatHistory(userId: string, chatId: string, messageLimit: number = 4): Promise<string> {
  try {
    const db = getFirestore();
    const messagesRef = collection(db, `users/${userId}/chats/${chatId}/messages`);
    const messagesQuery = query(messagesRef, orderBy("createdAt", "desc"), limit(messageLimit));
    const messagesSnapshot = await getDocs(messagesQuery);

    if (messagesSnapshot.empty) {
      return "";
    }

    // Format messages in chronological order
    const messages = messagesSnapshot.docs
      .map(doc => {
        const data = doc.data();
        return {
          role: data.role,
          text: data.text || "",
          timestamp: data.createdAt
        };
      })
      .sort((a, b) => {
        // Sort by timestamp (convert Firebase timestamp if needed)
        const timeA = a.timestamp?.toDate ? a.timestamp.toDate().getTime() : new Date(a.timestamp).getTime();
        const timeB = b.timestamp?.toDate ? b.timestamp.toDate().getTime() : new Date(b.timestamp).getTime();
        return timeA - timeB;
      });

    // Format the history as a string
    let formattedHistory = "Previous conversation history:\n";
    messages.forEach((msg) => {
      const role = msg.role === "user" ? "User" : "Assistant";
      formattedHistory += `${role}: ${msg.text}\n`;
    });

    return formattedHistory;
  } catch (error) {
    console.error("Error fetching chat history:", error);
    return ""; // Return empty string on error
  }
}

/**
 * Processes user messages, retrieves relevant context and chat history, and generates AI responses
 * with improved error handling and model fallback capabilities
 *
 * @param params - Processing parameters including userId, chatId, messageText and callbacks
 * @returns A promise that resolves when processing is complete
 */
export async function processMessage({
  userId,
  chatId,
  messageText,
  callbacks,
  isMounted
}: ProcessMessagesParams): Promise<void> {
  let streamedContent = "";
  let retryCount = 0;
  const maxRetries = 3;

  try {
    // Step 1: Convert user message to vector representation
    const queryVector = await convertPromptToVector(messageText);

    // Step 2: Get user namespace for Pinecone query
    const namespace = await getUserNamespace();
    if (!namespace) throw new Error("User namespace not found");

    // Step 3: Query Pinecone for relevant context
    const pineconeResponse = await pineconeIndex.namespace(namespace).query({
      vector: queryVector,
      topK: 5,
      includeValues: true,
      includeMetadata: true,
    });

    if (!pineconeResponse.matches || pineconeResponse.matches.length === 0) {
      throw new Error("No relevant context found");
    }

    // Step 4: Process matches and extract chunk IDs
    const mappedMatches = pineconeResponse.matches.map((match) => ({
      ...match,
      metadata: {
        ...match.metadata,
        chunkId: match.metadata?.chunk_id?.toString() || "",
      },
    }));

    const chunkIds = mappedMatches
      .map((match) => match.metadata?.chunkId as string)
      .filter(Boolean);

    if (chunkIds.length === 0) throw new Error("No valid chunk IDs retrieved");

    // Step 5: Fetch document chunks for context
    const chunks = await fetchDocumentChunksByChunkIds(chunkIds, userId);
    if (chunks.length === 0) throw new Error("No document chunks retrieved");

    // Step 6: Get user assessment summary for personalization
    const assessmentSummary = await getAssessmentSummary(userId);

    // Step 7: Fetch and filter recent chat history
    const rawChatHistory = await fetchChatHistory(userId, chatId);
    const relevantHistory = await getRelevantHistory(messageText, rawChatHistory);

    // Step 8: Fetch tracking, nutrition, and workout plan data
    let trackingData = "";
    let nutritionData = "";
    let workoutPlan = "";

    try {
      // Call the helper functions to fetch data
      trackingData = await fetchTrackingDataHelper(userId);
      nutritionData = await fetchNutritionDataHelper(userId);
      workoutPlan = await fetchUserWorkoutPlanHelper(userId);
    } catch (error) {
      console.error("Error fetching additional data:", error);
    }

    // Helper function to fetch tracking data
    async function fetchTrackingDataHelper(userEmail: string): Promise<string> {
      console.log("[WebRTCManager] Fetching tracking data for:", userEmail)
      if (!userEmail) {
        console.warn("[WebRTCManager] User email is required to fetch tracking data")
        return "No tracking data available (missing user ID)"
      }

      try {
        const db = getFirestore()
        const recordsRef = collection(db, "IF_users", userEmail, "tracking_records")
        // Limit the number of records to 30 to prevent overloading
        const q = query(recordsRef, orderBy("completedDate", "desc"), limit(30))
        const querySnapshot = await getDocs(q)

        console.log(`[WebRTCManager] Retrieved ${querySnapshot.size} tracking records`)

        if (querySnapshot.empty) {
          console.warn("[WebRTCManager] No tracking records found")
          return "User has not tracked any exercises yet"
        }

      let formattedTracking = "Exercise Tracking Summary:\n"
      formattedTracking += `\nTotal tracking records: ${querySnapshot.size}\n`

      if (querySnapshot.size > 100) {
        formattedTracking += "\nNote: Showing a summary of tracking data due to the large number of records.\n"
      }

      interface TrackingRecord {
        id: string
        exerciseId: string
        exerciseName: string
        libraryId: string
        workoutReference: string
        completedDate: { toDate: () => Date }
        difficultyLevel: number
        energyLevel: number
        timeToComplete: number
        sets?: number
        reps?: number
        weight?: number
        notes?: string
        day: number
        category: string
        [key: string]: unknown
      }

      const recordsByDay = new Map<number, TrackingRecord[]>()
      querySnapshot.forEach((doc) => {
        const record = doc.data() as TrackingRecord
        const day = record.day
        if (!recordsByDay.has(day)) {
          recordsByDay.set(day, [])
        }
        const dayRecords = recordsByDay.get(day)
        if (dayRecords) {
          const recordWithId = { ...record }
          recordWithId.id = doc.id
          dayRecords.push(recordWithId)
        }
      })

      recordsByDay.forEach((records, day) => {
        formattedTracking += `\nDay ${day}:\n`
        formattedTracking += `  Total exercises tracked: ${records.length}\n`

        const MAX_RECORDS_PER_DAY = 15 // Balanced limit to show more exercises while preventing overload
        let recordsToProcess = records

        // Define category names mapping
        const categoryNames: Record<string, string> = {
          "warm_up": "Warm-up",
          "resistance_training": "Resistance Training",
          "cardio_training": "Cardio Training",
          "cool_down": "Cool Down"
        }

        // Create a summary of exercise categories first
        const categoryCount: Record<string, number> = {}
        records.forEach(record => {
          const category = record.category || "uncategorized"
          categoryCount[category] = (categoryCount[category] || 0) + 1
        })

        // Add category summary to the output
        if (Object.keys(categoryCount).length > 0) {
          formattedTracking += `  Exercise categories:\n`
          Object.entries(categoryCount).forEach(([category, count]) => {
            const categoryName = categoryNames[category as keyof typeof categoryNames] || category
            formattedTracking += `    - ${categoryName}: ${count} exercises\n`
          })
        }

        // Limit detailed records if needed
        if (records.length > MAX_RECORDS_PER_DAY) {
          formattedTracking += `\n  Note: You completed ${records.length} exercises on this day. Showing details for the ${MAX_RECORDS_PER_DAY} most recent ones.\n`
          formattedTracking += `  To see all exercises, please ask specifically about Day ${day} exercises.\n`

          recordsToProcess = [...records]
            .sort((a, b) => {
              const dateA = a.completedDate?.toDate?.() || new Date(0)
              const dateB = b.completedDate?.toDate?.() || new Date(0)
              return dateB.getTime() - dateA.getTime()
            })
            .slice(0, MAX_RECORDS_PER_DAY)
        }

        const recordsByCategory = new Map<string, TrackingRecord[]>()
        recordsToProcess.forEach((record: TrackingRecord) => {
          if (!recordsByCategory.has(record.category)) {
            recordsByCategory.set(record.category, [])
          }
          const categoryRecords = recordsByCategory.get(record.category)
          if (categoryRecords) {
            categoryRecords.push(record)
          }
        })

        // Using the categoryNames defined above

        recordsByCategory.forEach((categoryRecords, category) => {
          const categoryName = categoryNames[category as keyof typeof categoryNames] || category
          formattedTracking += `  ${categoryName}:\n`

          categoryRecords.forEach((record: TrackingRecord) => {
            formattedTracking += `    - ${record.exerciseName}\n`
            formattedTracking += `      Difficulty: ${record.difficultyLevel}/5, Energy: ${record.energyLevel}/5\n`
            formattedTracking += `      Time: ${record.timeToComplete} minutes\n`

            if (category === "resistance_training") {
              if (record.sets) formattedTracking += `      Sets: ${record.sets}\n`
              if (record.reps) formattedTracking += `      Reps: ${record.reps}\n`
              if (record.weight) formattedTracking += `      Weight: ${record.weight} kg\n`
            }

            if (record.notes) formattedTracking += `      Notes: ${record.notes}\n`
          })
        })
      })

      formattedTracking += "\n\nSummary Statistics:\n"

      let totalDifficulty = 0
      let totalEnergy = 0
      let totalRecords = 0
      let resistanceTrainingCount = 0
      let cardioTrainingCount = 0
      let warmUpCount = 0
      let coolDownCount = 0
      let mostRecentDate: Date | null = null

      querySnapshot.forEach((doc) => {
        const record = doc.data() as TrackingRecord
        totalDifficulty += record.difficultyLevel || 0
        totalEnergy += record.energyLevel || 0
        totalRecords++

        if (record.category === "resistance_training") resistanceTrainingCount++
        else if (record.category === "cardio_training") cardioTrainingCount++
        else if (record.category === "warm_up") warmUpCount++
        else if (record.category === "cool_down") coolDownCount++

        if (record.completedDate) {
          let recordDate: Date
          if (typeof record.completedDate.toDate === "function") {
            recordDate = record.completedDate.toDate()
          } else if (record.completedDate instanceof Date) {
            recordDate = record.completedDate
          } else {
            try {
              recordDate = new Date(record.completedDate as unknown as string | number)
            } catch {
              recordDate = new Date()
            }
          }
          if (!mostRecentDate || recordDate > mostRecentDate) {
            mostRecentDate = recordDate
          }
        }
      })

      if (totalRecords > 0) {
        formattedTracking += `Average difficulty level: ${(totalDifficulty / totalRecords).toFixed(1)}/5\n`
        formattedTracking += `Average energy level: ${(totalEnergy / totalRecords).toFixed(1)}/5\n`
        formattedTracking += `\nExercise breakdown:\n`
        formattedTracking += `- Resistance training exercises: ${resistanceTrainingCount}\n`
        formattedTracking += `- Cardio training exercises: ${cardioTrainingCount}\n`
        formattedTracking += `- Warm-up exercises: ${warmUpCount}\n`
        formattedTracking += `- Cool-down exercises: ${coolDownCount}\n`

        if (mostRecentDate) {
          try {
            // Add type assertion to ensure TypeScript knows mostRecentDate is a Date
            formattedTracking += `\nMost recent tracking: ${(mostRecentDate as Date).toLocaleDateString()}\n`
          } catch {
            formattedTracking += `\nMost recent tracking: ${(mostRecentDate as Date).toString()}\n`
          }
        }
      }

      console.log("[WebRTCManager] Tracking data formatted, length:", formattedTracking.length)
      return formattedTracking
    } catch (error) {
      console.error("[WebRTCManager] Error fetching tracking data:", error)
      return "Error retrieving tracking data"
    }
  }

  async function fetchNutritionDataHelper(userEmail: string): Promise<string> {
    console.log("[WebRTCManager] Fetching nutrition data for:", userEmail)
    if (!userEmail) {
      console.warn("[WebRTCManager] User email is required to fetch nutrition data")
      return "No nutrition data available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const docRef = doc(db, "IF_users", userEmail, "Profile", "workoutplan")
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        console.warn("[WebRTCManager] Workout plan document does not exist")
        return "No nutrition advice available"
      }

      const data = docSnap.data()
      const nutritionAdvice = data?.nutritionAdvice

      if (!nutritionAdvice) {
        console.warn("[WebRTCManager] Nutrition advice field is missing")
        return "No nutrition advice available"
      }

      const sections: string[] = nutritionAdvice.split(";").map((section: string) => section.trim())
      let formattedAdvice = "Nutrition Advice:\n\n"

      sections.forEach((section: string) => {
        if (!section) return
        const colonIndex = section.indexOf(": ")
        if (colonIndex !== -1) {
          let key = section.substring(0, colonIndex).trim()
          let value = section.substring(colonIndex + 2).trim()
          if (key.startsWith('"')) key = key.substring(1)
          if (key.endsWith('"')) key = key.substring(0, key.length - 1)
          if (value.startsWith('"')) value = value.substring(1)
          if (value.endsWith('"')) value = value.substring(0, value.length - 1)
          const capitalizedKey = key.charAt(0).toUpperCase() + key.slice(1)
          formattedAdvice += `${capitalizedKey}: ${value}\n\n`
        } else {
          formattedAdvice += `${section}\n\n`
        }
      })

      console.log("[WebRTCManager] Nutrition data formatted, length:", formattedAdvice.length)
      return formattedAdvice
    } catch (error) {
      console.error("[WebRTCManager] Error fetching nutrition data:", error)
      return "Error retrieving nutrition data"
    }
  }

  async function fetchUserWorkoutPlanHelper(userEmail: string): Promise<string> {
    console.log("[WebRTCManager] Fetching workout plan for:", userEmail)
    if (!userEmail) {
      console.warn("[WebRTCManager] User email is required to fetch workout plan")
      return "No workout plan available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const collections = ["schedule", "shortTerm", "longTerm", "kick_starter"]

      for (const collectionName of collections) {
        console.log("[WebRTCManager] Checking collection:", collectionName)
        const docRef = doc(db, "IF_users", userEmail, "Profile", collectionName)
        const docSnap = await getDoc(docRef)

        if (docSnap.exists() && Object.keys(docSnap.data()).length > 0) {
          const data = docSnap.data()
          console.log(`[WebRTCManager] Found data in ${collectionName} collection`)

          let formattedPlan = `User's ${collectionName} workout plan:\n`

          if (collectionName === "schedule") {
            const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
            days.forEach((day) => {
              if (data[day.toLowerCase()]) {
                formattedPlan += `\n${day}:\n`
                const dayData = data[day.toLowerCase()] as DayData
                if (Array.isArray(dayData)) {
                  dayData.forEach((exercise: Exercise, index: number) => {
                    formattedPlan += `  ${index + 1}. ${exercise.name || "Exercise"}`
                    if (exercise.sets) formattedPlan += ` - ${exercise.sets} sets`
                    if (exercise.reps) formattedPlan += ` x ${exercise.reps} reps`
                    formattedPlan += "\n"
                  })
                } else if (typeof dayData === "object" && dayData !== null) {
                  const workoutData = dayData as DayWorkout
                  formattedPlan += `  Workout: ${workoutData.workoutType || "Not specified"}\n`
                  if (workoutData.exercises && Array.isArray(workoutData.exercises)) {
                    workoutData.exercises.forEach((exercise: Exercise, index: number) => {
                      formattedPlan += `  ${index + 1}. ${exercise.name || "Exercise"}`
                      if (exercise.sets) formattedPlan += ` - ${exercise.sets} sets`
                      if (exercise.reps) formattedPlan += ` x ${exercise.reps} reps`
                      formattedPlan += "\n"
                    })
                  }
                } else {
                  formattedPlan += `  ${dayData}\n`
                }
              } else {
                formattedPlan += `\n${day}: Rest day or no workout assigned\n`
              }
            })
          } else {
            Object.entries(data).forEach(([key, value]) => {
              if (key.startsWith("_") || key === "createdAt" || key === "updatedAt") return
              if (typeof value === "object" && value !== null) {
                formattedPlan += `\n${key}:\n`
                Object.entries(value as Record<string, unknown>).forEach(([subKey, subValue]) => {
                  formattedPlan += `  ${subKey}: ${JSON.stringify(subValue)}\n`
                })
              } else {
                formattedPlan += `${key}: ${value}\n`
              }
            })
          }

          console.log("[WebRTCManager] Workout plan formatted, length:", formattedPlan.length)
          return formattedPlan
        }
      }

      console.warn("[WebRTCManager] No workout plan found in any collection")
      return "No workout plan found in any collection"
    } catch (error) {
      console.error("[WebRTCManager] Error fetching workout plan:", error)
      return "Error retrieving workout plan data"
    }
  }

    // Step 8: Prepare messages for LLM with chat history context
    const systemMessage = {
      role: "system" as const,
      content: `You are a Personal Fitness Trainer and fitness coach
**Your Context:** You have been provided with the complete fitness assessment results for a client. This includes comprehensive data on their current fitness profile.
**Your Mandate:** For all subsequent questions from the user regarding this specific assessment:
1. **Adopt the Persona:** Respond *consistently* as an experienced, knowledgeable, objective and supportive fitness trainer. Your tone should be professional yet encouraging.
2. **Context is King:** Base your answers *strictly and exclusively* on the data provided or retrieved via functions. Do not introduce external assumptions or generic information not supported by the data.
3. **Interpret, Don't Just Report:** Go beyond simply stating the numbers. *Interpret* what the results mean in practical terms regarding the client's current fitness level, potential strengths, and areas needing focus.
4. **Clarity is Crucial:** Explain any fitness terminology or assessment metrics in simple, easy-to-understand language.
5. **Tracking:** Unless the user explicitly asks otherwise, provide a summary of the users workout tracking. Provide a more detailed response only on request.
6. **Identify Implications:** When relevant to the question, highlight the implications of the findings for the client's health, performance, or potential goal achievement (based *only* on the data).
7. **Maintain Scope:** Confine your analysis and explanations to the realm of fitness and wellness. Avoid making medical diagnoses or giving advice that requires medical expertise.
8. **Format:** Format your response in Markdown with proper paragraphs, headings, and lists where appropriate.

User's assessment summary:
${assessmentSummary || "No assessment summary available"}

${trackingData ? `User's exercise tracking summary:
${trackingData}` : ""}

${workoutPlan ? `User's workout plan:
${workoutPlan}` : ""}

${nutritionData ? `User's nutrition data:
${nutritionData}` : ""}

${relevantHistory ? `Relevant conversation history:
${relevantHistory}` : ""}

Relevant document chunks:
${chunks.map((chunk) => chunk.pageContent).join("\n") || "No relevant chunks available"}

Respond directly to the user's most recent message, maintaining conversation continuity.`,
    };

    const userMessageForLLM = {
      role: "user" as const,
      content: messageText,
      name: userId,
    };

    const messages = [systemMessage, userMessageForLLM];

    // Build list of models to try, ensuring PRIMARY_MODEL is first if not already in FALLBACK_MODELS
    const modelList = FALLBACK_MODELS.includes(PRIMARY_MODEL)
      ? [...FALLBACK_MODELS]
      : [PRIMARY_MODEL, ...FALLBACK_MODELS];

    // Remove duplicates while preserving order
    const uniqueModels = [...new Set(modelList)];

    console.log("Model fallback priority:", uniqueModels);

    // Step 9: Enhanced Groq call with proper model fallback strategy
    const attemptGroqCall = async (): Promise<void> => {
      let currentModelIndex = 0;
      let lastError: Error | null = null;

      while (currentModelIndex < uniqueModels.length) {
        const currentModel = uniqueModels[currentModelIndex];

        try {
          console.log(`Attempting with model: ${currentModel} (retry: ${retryCount})`);

          await callGroqAiEx({
            messages,
            stream: true,
            modelName: currentModel,
            onChunk: async (chunk: string) => {
              if (!isMounted.current) return;

              streamedContent += chunk;
              callbacks.onChunkReceived(streamedContent);
            },
          });

          // Successfully completed streaming
          console.log(`Success with model: ${currentModel}`);
          await callbacks.onComplete(streamedContent);
          return; // Exit on success
        } catch (modelError) {
          console.error(`Error with model ${currentModel}:`, modelError);
          lastError = modelError instanceof Error ? modelError : new Error(String(modelError));

          const errorMessage = lastError.message.toLowerCase();

          // Check if this is a model availability issue
          if (
            errorMessage.includes("service unavailable") ||
            errorMessage.includes("capacity") ||
            errorMessage.includes("rate limit")
          ) {
            // Try the next model
            currentModelIndex++;
            console.log(`Switching to next model: ${uniqueModels[currentModelIndex] || "None available"}`);
          } else {
            // For other errors, don't try different models, just retry current one
            break;
          }
        }
      }

      // If we've exhausted all models or encountered a non-availability error
      if (retryCount < maxRetries) {
        retryCount++;
        // Reset to first model for next retry with exponential backoff
        const backoffTime = 1000 * Math.pow(2, retryCount - 1);
        console.log(`Retrying with exponential backoff (${retryCount}/${maxRetries}) after ${backoffTime}ms`);
        await new Promise(resolve => setTimeout(resolve, backoffTime));
        return attemptGroqCall();
      }

      throw lastError || new Error("All models failed after multiple attempts");
    };

    // Step 10: Execute Groq call with enhanced retry handling
    try {
      await attemptGroqCall();
    } catch (streamError) {
      console.error("Final streaming error after all retries:", streamError);
      const errorMessage = streamError instanceof Error
        ? streamError.message
        : "Unknown error";

      // Handle partial or complete streaming failures
      if (streamedContent.trim()) {
        await callbacks.onComplete(
          `${streamedContent}\n\n**Error:** This message may be incomplete due to a service issue: ${errorMessage}`
        );
      } else {
        await callbacks.onComplete(
          `**Error:** Unable to generate response due to: ${errorMessage}`
        );
      }

      callbacks.onError(`Failed to complete response: ${errorMessage}`);
    }
  } catch (error) {
    // Handle general processing errors
    console.error("Error in processMessage:", error);
    const errorMessage = error instanceof Error
      ? error.message
      : "An unknown error occurred";

    callbacks.onError(errorMessage);

    // Return error message for assistant
    await callbacks.onComplete(
      `**Sorry, something went wrong:** ${errorMessage}`
    );
  }
}

export default processMessage;