"use client";

import { useState, useCallback } from "react";
import { ImageModel } from "./useGptImageGeneration";

interface MealImageGenerationResult {
  isGenerating: boolean;
  generateImage: (mealName: string, userId: string, model?: ImageModel) => Promise<string | null>;
  error: string | null;
  usedModel: string | null;
  isFallback: boolean;
}

interface GenerationResult {
  imageUrl?: string;
  jobId?: string;
  namespace?: string;
  model?: string;
  fallback?: boolean | string;
  error?: string;
}

/**
 * Custom hook for generating meal images using various AI models
 */
export default function useMealImageGeneration(): MealImageGenerationResult {
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [usedModel, setUsedModel] = useState<string | null>(null);
  const [isFallback, setIsFallback] = useState<boolean>(false);

  /**
   * Generate an image for a meal
   * @param mealName - The name of the meal
   * @param userId - The user ID
   * @param model - Optional model to use for image generation
   * @returns The URL of the generated image, or null if generation failed
   */
  const generateImage = useCallback(async (
    mealName: string,
    userId: string,
    model: ImageModel = 'gpt-image-1'
  ): Promise<string | null> => {
    setError(null);
    setIsGenerating(true);
    setUsedModel(null);
    setIsFallback(false);

    try {
      // Create a prompt for the image generator
      const prompt = `A professional food photography image of ${mealName}. High quality, well-lit image of the prepared dish on a beautiful plate. No text, no watermarks, photorealistic.`;

      console.log(`Generating meal image for "${mealName}" using model: ${model}`);

      // Call the API to generate the image
      const response = await fetch("/api/generate-image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          userId,
          model,
          size: '1024x1024',
          style: 'vivid',
          quality: 'hd'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: "Failed to generate image" }));
        throw new Error(errorData.message || "Failed to generate image");
      }

      const data: GenerationResult = await response.json();

      // Handle the case where imageUrl could be undefined
      if (!data.imageUrl) {
        console.warn("Image generation successful but no URL was returned");
        return null;
      }

      // Set the model that was actually used
      if (data.model) {
        setUsedModel(data.model);
      }

      // Check if a fallback model was used
      if (data.fallback) {
        setIsFallback(true);
        console.log(`Used fallback model: ${data.model || 'unknown'}`);
      }

      // Check if there was an error but we still got a placeholder image
      if (data.error) {
        setError(data.error);
      }

      console.log("Meal image generation successful, URL:", data.imageUrl.substring(0, 50) + "...");
      return data.imageUrl;
    } catch (error) {
      console.error(`Error generating image for meal "${mealName}":`, error);

      // Provide a more user-friendly error message
      let errorMessage = "Unknown error occurred";
      if (error instanceof Error) {
        // Check if it's an API key related error
        if (error.message.includes("API key")) {
          errorMessage = "API key issue. The system will try to use a fallback key.";
        } else if (error.message.includes("quota")) {
          errorMessage = "API quota exceeded. Please try again later.";
        } else {
          errorMessage = error.message;
        }
      }

      setError(errorMessage);
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  return { isGenerating, generateImage, error, usedModel, isFallback };
}
