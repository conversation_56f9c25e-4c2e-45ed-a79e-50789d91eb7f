import { NextRequest, NextResponse } from 'next/server';
import { generateImageTool, ImageModel, ImageSize, ImageStyle, ImageQuality, ImageFormat, ImageBackground } from '../../../lib/tools/generate-image';

// Define the types for the API parameters
interface ImageToolParams {
  prompt: string;
  refinePrompt?: boolean;
  model?: ImageModel;
  size?: ImageSize;
  style?: ImageStyle;
  quality?: ImageQuality;
  format?: ImageFormat;
  background?: ImageBackground;
  compression?: number;
  userId?: string;
}

/**
 * Process a request for the Image Generation tool
 * @param {ImageToolParams} params - Parameters for the image generation tool
 * @returns {Promise<NextResponse>} - The generated image result
 */
async function processImageTool(params: ImageToolParams): Promise<NextResponse> {
  const {
    prompt,
    refinePrompt = true,
    model = 'dall-e-3',
    size = '1024x1024',
    style = 'vivid',
    quality = 'auto',
    format = 'jpeg',
    background = 'auto',
    compression,
    userId
  } = params;

  if (!prompt) {
    return NextResponse.json({ error: 'Prompt is required for Image Generation tool' }, { status: 400 });
  }

  try {
    console.log(`[processImageTool] Received model parameter: ${model}`);
    console.log(`[processImageTool] Using model: ${model}`);
    console.log(`[processImageTool] Using size: ${size}`);
    console.log(`[processImageTool] Using style: ${style}`);
    console.log(`[processImageTool] Using quality: ${quality}`);
    console.log(`[processImageTool] Using format: ${format}`);

    // If userId is provided, use Firebase integration
    if (userId) {
      console.log(`Using Firebase integration for user: ${userId}`);

      try {
        // Generate the image with Firebase integration
        const result = await generateImageTool.generateImage({
          prompt,
          refinePrompt,
          model,
          size,
          style,
          quality,
          format,
          background,
          compression,
          userId
        });

        // Return the result immediately to the client
        return NextResponse.json(result);
      } catch (firebaseError) {
        console.error('Firebase image generation error:', firebaseError);
        // Fall back to direct generation if Firebase integration fails
        console.log('Falling back to direct image generation');
      }
    }

    // Generate the image directly without Firebase
    const result = await generateImageTool.generateImage({
      prompt,
      refinePrompt,
      model,
      size,
      style,
      quality,
      format,
      background,
      compression
    });

    // Return the result immediately to the client
    // The image will be saved to the gallery when the user clicks the "Save to Gallery" button
    return NextResponse.json(result);
  } catch (error) {
    console.error('Image generation error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to generate image';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}

/**
 * Main API route handler for the tools endpoint
 * @param {NextRequest} req - The incoming request
 * @returns {Promise<NextResponse>} - The response
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Parse the request body
    const body = await req.json();

    // Extract the tool name and parameters
    const { tool, params } = body;

    if (!tool) {
      return NextResponse.json({ error: 'Tool name is required' }, { status: 400 });
    }

    // Process the request based on the tool name
    switch (tool) {
      case 'generateImage':
        return await processImageTool(params as ImageToolParams);

      // Add more tools here as needed

      default:
        return NextResponse.json({ error: `Unknown tool: ${tool}` }, { status: 400 });
    }
  } catch (error) {
    console.error('API error:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}