import { NextRequest, NextResponse } from 'next/server';

interface YouTubeApiResponseItem {
  id: {
    videoId: string;
  };
  snippet: {
    title: string;
    description: string;
    thumbnails: {
      high?: { url: string };
      default?: { url: string };
    };
    channelTitle: string;
    publishedAt: string;
  };
}

interface YouTubeApiResponse {
  items: YouTubeApiResponseItem[];
}

// Mock data for development mode or when API fails
const generateMockData = (query: string, maxResults: number): YouTubeApiResponse => {
  const mockItems: YouTubeApiResponseItem[] = [];

  // Extract exercise name from the query (first few words before 'exercise')
  const exerciseName = query.split(' ').slice(0, 3).join(' ');

  // Define a set of static thumbnail URLs that are guaranteed to work
  // These are from the public directory in the Next.js app
  const thumbnailUrls = [
    '/images/mock-thumbnails/workout-1.jpg',
    '/images/mock-thumbnails/workout-2.jpg',
    '/images/mock-thumbnails/workout-3.jpg',
    '/images/mock-thumbnails/workout-4.jpg',
    '/images/mock-thumbnails/workout-5.jpg'
  ];

  for (let i = 0; i < maxResults; i++) {
    // Use modulo to cycle through available thumbnails if we need more than we have
    const thumbnailIndex = i % thumbnailUrls.length;

    mockItems.push({
      id: {
        videoId: `mock-video-id-${i}-${Date.now()}`
      },
      snippet: {
        title: `${exerciseName} - Proper Form and Technique (Mock Video ${i+1})`,
        description: `Learn how to perform ${exerciseName} with perfect form. This video demonstrates the proper technique and common mistakes to avoid.`,
        thumbnails: {
          high: { url: thumbnailUrls[thumbnailIndex] },
          default: { url: thumbnailUrls[thumbnailIndex] }
        },
        channelTitle: 'Fitness Training Channel (Mock)',
        publishedAt: new Date().toISOString()
      }
    });
  }

  return { items: mockItems };
};

/**
 * API route handler for YouTube search
 * This acts as a proxy to avoid referrer restrictions on the YouTube API
 * Falls back to mock data when in development mode or when API fails
 */
export async function GET(request: NextRequest) {
  // Get search parameters from the request
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('q');
  const maxResults = parseInt(searchParams.get('maxResults') || '3', 10);
  const useMock = searchParams.get('mock') === 'true';

  if (!query) {
    return NextResponse.json(
      { error: 'Search query is required' },
      { status: 400 }
    );
  }

  // If mock mode is explicitly enabled, return mock data immediately
  if (useMock) {
    console.log('Using mock YouTube data for query:', query);
    return NextResponse.json(generateMockData(query, maxResults));
  }

  // If mock parameter is explicitly set to false, we'll try to use the real API
  // even if we're in development mode

  try {
    // Get API key from environment
    const apiKey = process.env.YOUTUBE_API_KEY || 'AIzaSyA1Nc7BqtD3Y85joJtWxo3i2S0LMC3Y-Kw';
    if (!apiKey) {
      console.warn('YouTube API key is not configured, falling back to mock data');
      return NextResponse.json(generateMockData(query, maxResults));
    }

    // We'll create a proper URL object for the YouTube API request

    // Get the abort signal from the request if it exists
    const signal = request.signal;

    console.log('Fetching YouTube data for query:', query);

    // Create a new URL object to properly handle the YouTube API URL
    const apiUrl = new URL('https://www.googleapis.com/youtube/v3/search');

    // Add query parameters
    apiUrl.searchParams.append('part', 'snippet');
    apiUrl.searchParams.append('q', query);
    apiUrl.searchParams.append('maxResults', maxResults.toString());
    apiUrl.searchParams.append('type', 'video');
    apiUrl.searchParams.append('videoEmbeddable', 'true');
    apiUrl.searchParams.append('key', apiKey);

    // Log the full URL we're fetching (without the API key for security)
    const logUrl = new URL(apiUrl.toString());
    logUrl.searchParams.delete('key');
    console.log('Fetching from:', logUrl.toString());

    const response = await fetch(apiUrl.toString(), {
      headers: {
        'Accept': 'application/json',
        // We can't set the Referer header directly, but we can set the Origin
        'Origin': process.env.NEXT_PUBLIC_BASE_URL || 'https://intelligentfitness.ai'
      },
      signal // Pass the abort signal to the fetch request
    });

    if (!response.ok) {
      const errorBody = await response.text().catch(() => 'Could not read error response body');
      console.error(`YouTube API error: Status ${response.status}`, errorBody);

      // Fall back to mock data on API error
      console.log('Falling back to mock data due to API error');
      return NextResponse.json(generateMockData(query, maxResults));
    }

    // Parse and return the YouTube API response
    const data = await response.json() as YouTubeApiResponse;
    return NextResponse.json(data);

  } catch (error) {
    // Check if this was an abort error
    if (error instanceof DOMException && error.name === 'AbortError') {
      console.log('YouTube search request was aborted');
      return NextResponse.json({
        items: [],
        aborted: true,
        error: 'Search request was cancelled'
      });
    }

    console.error('YouTube Search API error:', error);

    // Fall back to mock data on any error
    console.log('Falling back to mock data due to exception');
    return NextResponse.json(generateMockData(query, maxResults));
  }
}
