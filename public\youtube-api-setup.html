<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube API Setup Guide</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #cc0000;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        h2 {
            color: #333;
            margin-top: 30px;
        }
        code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .note {
            background-color: #fffde7;
            border-left: 4px solid #ffeb3b;
            padding: 15px;
            margin: 20px 0;
        }
        .warning {
            background-color: #ffebee;
            border-left: 4px solid #f44336;
            padding: 15px;
            margin: 20px 0;
        }
        img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
        }
        ol, ul {
            padding-left: 25px;
        }
        li {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>YouTube API Setup Guide</h1>

    <div class="note">
        <strong>Note:</strong> This page is designed to help you configure your YouTube API key to work properly with your application.
    </div>

    <h2>The Problem: HTTP Referrer Restrictions</h2>
    <p>You're seeing errors like this because your YouTube API key has HTTP referrer restrictions that are blocking requests:</p>
    <pre>
YouTube API error: Status 403 {
  "error": {
    "code": 403,
    "message": "Requests from referer \u003cempty\u003e are blocked.",
    "errors": [
      {
        "message": "Requests from referer \u003cempty\u003e are blocked.",
        "domain": "global",
        "reason": "forbidden"
      }
    ],
    "status": "PERMISSION_DENIED"
  }
}</pre>

    <h2>Solution: Configure API Key Restrictions</h2>
    <p>Follow these steps to configure your YouTube API key properly:</p>

    <ol>
        <li>Go to the <a href="https://console.cloud.google.com/apis/credentials" target="_blank">Google Cloud Console Credentials page</a></li>
        <li>Find your YouTube API key (currently: <code>AIzaSyA1Nc7BqtD3Y85joJtWxo3i2S0LMC3Y-Kw</code>)</li>
        <li>Click on the key to edit its settings</li>
        <li>Under "API restrictions", make sure "YouTube Data API v3" is selected</li>
        <li>Under "Application restrictions", you have several options:
            <ul>
                <li><strong>None</strong>: Remove all restrictions (not recommended for production)</li>
                <li><strong>HTTP referrers</strong>: Add the following referrers:
                    <ul>
                        <li><code>http://localhost:3000/*</code> (for local development)</li>
                        <li><code>https://intelligentfitness.ai/*</code> (for production)</li>
                        <li>Any other domains where your app will be hosted</li>
                    </ul>
                </li>
                <li><strong>IP addresses</strong>: Restrict to your server's IP addresses (for server-side only usage)</li>
            </ul>
        </li>
        <li>Click "Save" to apply the changes</li>
    </ol>

    <div class="note">
        <p><strong>For development:</strong> The simplest approach is to add <code>http://localhost:3000/*</code> to the HTTP referrers list.</p>
    </div>

    <h2>Alternative: Create a New API Key</h2>
    <p>If you prefer, you can create a new API key with the proper restrictions:</p>
    <ol>
        <li>Go to the <a href="https://console.cloud.google.com/apis/credentials" target="_blank">Google Cloud Console Credentials page</a></li>
        <li>Click "Create Credentials" → "API key"</li>
        <li>Configure the restrictions as described above</li>
        <li>Update your <code>.env</code> file with the new API key</li>
    </ol>

    <h2>Testing Your Configuration</h2>
    <p>After making these changes, you should be able to use the YouTube API without the referrer errors. To test:</p>
    <ol>
        <li>Go back to your application</li>
        <li>Try using the "Add YouTube Videos" feature again</li>
        <li>Check the console logs to verify that the API requests are successful</li>
    </ol>

    <div class="warning">
        <p><strong>Important:</strong> Changes to API key settings may take a few minutes to propagate through Google's systems.</p>
    </div>

    <h2>Need More Help?</h2>
    <p>If you continue to experience issues, check the following:</p>
    <ul>
        <li>Verify that your API key is correctly set in your <code>.env</code> file</li>
        <li>Make sure the YouTube Data API v3 is enabled in your Google Cloud project</li>
        <li>Check for any quota limitations on your Google Cloud project</li>
        <li>Review the <a href="https://developers.google.com/youtube/v3/getting-started" target="_blank">YouTube API documentation</a> for more information</li>
    </ul>
</body>
</html>
