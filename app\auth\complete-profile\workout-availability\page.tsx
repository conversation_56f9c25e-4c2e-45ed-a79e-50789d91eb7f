'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function WorkoutAvailability() {
  const [workoutFrequency, setWorkoutFrequency] = useState('')
  const [availableTime, setAvailableTime] = useState('')
  const [equipmentAccess, setEquipmentAccess] = useState('')
  const router = useRouter()

  const handleContinue = () => {
    if (workoutFrequency && availableTime && equipmentAccess) {
      // Store the workout availability information
      localStorage.setItem('workoutFrequency', workoutFrequency)
      localStorage.setItem('availableTime', availableTime)
      localStorage.setItem('equipmentAccess', equipmentAccess)
      router.push('/auth/complete-profile/additional-info')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Workout Experience and Availability
          </h2>
        </div>
        <div className="mt-8 space-y-6">
          <div>
            <label htmlFor="workoutFrequency" className="block text-sm font-medium text-gray-700">
              How often do you exercise per week?
            </label>
            <select
              id="workoutFrequency"
              value={workoutFrequency}
              onChange={(e) => setWorkoutFrequency(e.target.value)}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">Select frequency</option>
              <option value="1-2">1-2 times</option>
              <option value="3-4">3-4 times</option>
              <option value="5+">5+ times</option>
            </select>
          </div>
          <div>
            <label htmlFor="availableTime" className="block text-sm font-medium text-gray-700">
              How many days per week can you dedicate to working out?
            </label>
            <select
              id="availableTime"
              value={availableTime}
              onChange={(e) => setAvailableTime(e.target.value)}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">Select available days</option>
              <option value="1-2">1-2 days</option>
              <option value="3-4">3-4 days</option>
              <option value="5-6">5-6 days</option>
              <option value="7">Every day</option>
            </select>
          </div>
          <div>
            <label htmlFor="equipmentAccess" className="block text-sm font-medium text-gray-700">
              Do you have access to a gym or workout equipment at home?
            </label>
            <select
              id="equipmentAccess"
              value={equipmentAccess}
              onChange={(e) => setEquipmentAccess(e.target.value)}
              className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="">Select option</option>
              <option value="gym">Access to a gym</option>
              <option value="home">Home equipment</option>
              <option value="both">Both gym and home equipment</option>
              <option value="none">No equipment access</option>
            </select>
          </div>
        </div>
        <button
          onClick={handleContinue}
          disabled={!workoutFrequency || !availableTime || !equipmentAccess}
          className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </div>
  )
}

