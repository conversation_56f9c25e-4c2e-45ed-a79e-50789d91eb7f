import OpenAI from "openai";
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";
import { Firestore } from "firebase/firestore";
import { getExerciseLibraryNamespaces, LibraryNamespace } from "@/utils/vectorLibraryMatch";

// ----------------------------------------------------------------------------
// 1. Define all necessary interfaces and Zod schemas for OpenAI requests/responses
// ----------------------------------------------------------------------------

export interface OpenAiMessage {
  role: "system" | "user" | "assistant" | "tool";
  content: string | null;
  tool_calls?: Array<{
    id: string;
    type: "function";
    function: {
      name: string;
      arguments: string;
    };
  }>;
  name?: string;
}

type OpenAiModel = "gpt-4o";

export interface OpenAiChatCompletion {
  choices: Array<{
    message: OpenAiMessage;
    finish_reason: string | null;
    index: number;
  }>;
  id: string;
  object: string;
  created: number;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// ----------------------------------------------------------------------------
// 2. Other types used in the file (and Zod schemas) with libraryId as mandatory
// ----------------------------------------------------------------------------

export const WorkoutDaySchema = z.object({
  day: z.string(),
  exercises: z.array(
    z.object({
      name: z.string(),
      libraryId: z.string(),
      specs: z.string(),
      instructions: z.string().optional(),
      mistakes: z.string().optional(),
      modifications: z.string().optional(),
    })
  ),
});

export type WorkoutDay = z.infer<typeof WorkoutDaySchema>;

export const RawWorkoutPlanSchema = z.object({
  workoutDays: z.array(WorkoutDaySchema),
});

export type RawWorkoutPlan = z.infer<typeof RawWorkoutPlanSchema>;

export const AgentResponsesSchema = z.object({
  userProfileSummary: z.string(),
  exerciseProgram: z.string(),
  formTechniqueGuidance: z.string(),
  longTermPlanning: z.string(),
  shortTermPlanning: z.string(),
  nutritionAdvice: z.string(),
  coachInstructions: z.string(),
  workoutPlan: z.object({
    shortTermPlan: z.string(),
    longTermPlan: z.string(),
    exerciseProgram: z.string(),
    formTechniqueGuidance: z.string(),
  }),
});

export type AgentResponses = z.infer<typeof AgentResponsesSchema>;

// ----------------------------------------------------------------------------
// 3. Constants
// ----------------------------------------------------------------------------

const MAX_TOKENS_SUMMARY = 4000;
const MAX_TOKENS_DEFAULT = 12000;
const BASE_SYSTEM_PROMPT = `You are an AI assistant at Intelligentfitness.ai specialized in analyzing user 
profiles for personalized fitness and nutrition recommendations.
Use the provided profile text and exercise library data to complete your task as concisely as possible.`; 

const Model: OpenAiModel = "gpt-4o";

// ----------------------------------------------------------------------------
// 4. Helper functions
// ----------------------------------------------------------------------------

function extractWorkoutEssentials(jsonString: string): string {
  try {
    const data = JSON.parse(jsonString);
    const essentials = {
      exercises: Array.isArray(data?.days)
        ? data.days.map((day: unknown) => {
            if (typeof day === "object" && day !== null) {
              const dayObj = day as { exercises?: unknown };
              const exercisesArray = Array.isArray(dayObj.exercises)
                ? dayObj.exercises
                : [];
              const firstExercise = exercisesArray[0];
              let name = "";
              let libraryId = "";
              let specs = "";
              if (typeof firstExercise === "object" && firstExercise !== null) {
                const exerciseObj = firstExercise as { name?: unknown; libraryId?: unknown; specs?: unknown };
                name = typeof exerciseObj.name === "string" ? exerciseObj.name : "";
                libraryId = typeof exerciseObj.libraryId === "string" ? exerciseObj.libraryId : "";
                specs = typeof exerciseObj.specs === "string" ? exerciseObj.specs : "";
              }
              return { name, libraryId, specs };
            }
            return { name: "", libraryId: "", specs: "" };
          })
        : [],
      duration: data?.duration || "",
      focus: data?.focus || "",
    };
    return JSON.stringify(essentials);
  } catch (e) {
    console.error("Error extracting workout essentials:", e);
    return "{}";
  }
}

function estimateTokenCount(text: string): number {
  return Math.ceil(text.length / 4);
}

async function fetchExerciseLibrary(db: Firestore): Promise<LibraryNamespace[]> {
  try {
    const namespaces = await getExerciseLibraryNamespaces(db);
    return namespaces;
  } catch (error) {
    console.error("Failed to fetch exercise library:", error);
    return [];
  }
}

// ----------------------------------------------------------------------------
// OpenAI Client Initialization
// ----------------------------------------------------------------------------

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// ----------------------------------------------------------------------------
// 5. AI Agents
// ----------------------------------------------------------------------------

// --- Summarization Agent ---
export async function summarizeProfileText(
  userEmail: string,
  profileText: string
): Promise<string> {
  console.log("Agent: summarizeProfileText called for user:", userEmail);

  const systemContent = `${BASE_SYSTEM_PROMPT}
You are a profile summarizer. Condense the following client profile text into a concise plain text 
summary that highlights key fitness and nutrition details.
Return only raw plain text without markdown formatting or any additional commentary.`;

  const response = await openai.chat.completions.create({
    model: Model,
    messages: [
      { role: "system", content: systemContent },
      { role: "user", content: profileText },
    ],
    temperature: 0.7,
    max_tokens: MAX_TOKENS_SUMMARY,
    response_format: { type: "text" },
  });

  const content = response.choices[0].message.content;
  if (content === null) {
    throw new Error("OpenAI returned null content");
  }
  return content.trim();
}

// --- Exercise Programming Specialist AI ---
const ExerciseProgramSchema = z.object({
  title: z.string().describe("Workout Plan Name"),
  days: z.array(
    z.object({
      day: z.number().int().min(1).max(7).describe("Day number (1-7)"),
      name: z.string().describe("Day Title"),
      duration: z.number().int().describe("Workout duration in minutes"),
      warm_up: z.array(z.object({
        exercise: z.string(),
        libraryId: z.string(),
        duration: z.number().optional(),
        details: z.string().optional()
      })),
      resistance_training: z.array(z.object({
        exercise: z.string(),
        libraryId: z.string(),
        sets: z.number(),
        reps: z.number(),
        notes: z.string()
      })),
      cardio_training: z.array(z.object({
        exercise: z.string(),
        libraryId: z.string(),
        duration: z.number(),
        notes: z.string()
      })),
      cool_down: z.array(z.object({
        exercise: z.string(),
        libraryId: z.string(),
        duration: z.number().optional(),
        details: z.string().optional()
      })),
      progressive_overload: z.object({
        increase_reps: z.string(),
        increase_sets: z.string(),
        notes: z.string()
      }),
      rest_and_recovery: z.object({
        stretching: z.string(),
        foam_rolling: z.string(),
        sleep: z.string()
      }),
      modifications: z.object({
        lower_back: z.string(),
        broken_toe: z.string()
      }),
    })
  ),
});
const exerciseProgramSchemaJson = JSON.stringify(
  zodToJsonSchema(ExerciseProgramSchema, { name: "ExerciseProgramSchema" }),
  null,
  2
);

export async function exerciseProgrammingSpecialistAI(
  userEmail: string,
  profileText: string,
  db: Firestore
): Promise<string> {
  console.log("Agent: exerciseProgrammingSpecialistAI called for user:", userEmail);

  const exerciseLibrary = await fetchExerciseLibrary(db);
  const libraryData = JSON.stringify(
    exerciseLibrary.map(ex => ({ name: ex.name, libraryId: ex.libraryId }))
  );

  const systemContent = `${BASE_SYSTEM_PROMPT}
You are an Exercise Programming Specialist AI. Your role is to design a structured workout plan 
aligned with the user's objectives and physical capabilities, based on the provided profile.
Please follow these instructions:
1. Review the client's profileText for goals (weight loss, endurance, muscle building), body metrics, and any constraints.
2. Use the provided exercise library data to select appropriate exercises. Each exercise MUST have a valid libraryId from the library.
3. Design a comprehensive workout plan with recommended frequency, intensity, exercise types, and progressive overload.
4. Include:
   - Resistance/Strength Training (exercises, sets, reps)
   - Cardiovascular Training (type, duration, intensity)
   - Warm-up & Cool-down guidelines
   - Rest & Recovery advice
5. Present your plan in clear, actionable language.

Exercise Library Data (use only these exercises and their libraryIds):
${libraryData}

You MUST return ONLY a JSON object containing a valid workout plan data instance. 
Do NOT include any text or markdown formatting before or after the JSON.
The JSON instance MUST conform to the following schema (do not output the schema itself):
${exerciseProgramSchemaJson}

Generate a valid JSON instance that exactly follows these requirements. Every key must be present with the correct type; no extra keys or null values are allowed. Ensure every exercise has a libraryId from the provided library.
`;

  const response = await openai.chat.completions.create({
    model: Model,
    messages: [
      { role: "system", content: systemContent },
      { role: "user", content: profileText },
    ],
    temperature: 0.7,
    max_tokens: MAX_TOKENS_DEFAULT,
    response_format: { type: "json_object" },
  });
  const content = response.choices[0].message.content;
  if (!content) {
    throw new Error("No content returned from OpenAI.");
  }
  try {
    const parsedContent = JSON.parse(content);
    ExerciseProgramSchema.parse(parsedContent);
    return JSON.stringify(parsedContent);
  } catch (error) {
    console.error("Invalid JSON response from exerciseProgrammingSpecialistAI:", error);
    console.error("Raw content:", content);
    return JSON.stringify({
      title: "Error Plan",
      days: [],
      error: "Failed to generate workout plan: Invalid JSON response from AI",
      details: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    }, null, 2);
  }
}

// --- Form Technique Coach AI ---
const FormTechniqueGuidanceSchema = z.object({
  exercise: z.string(),
  libraryId: z.string(),
  steps: z.array(z.string())
});
const formTechniqueSchemaJson = JSON.stringify(
  zodToJsonSchema(FormTechniqueGuidanceSchema, { name: "FormTechniqueGuidanceSchema" }),
  null,
  2
);

export async function formTechniqueCoachAI(
  userEmail: string,
  profileText: string,
  db: Firestore
): Promise<string> {
  console.log("Agent: formTechniqueCoachAI called for user:", userEmail);

  const exerciseLibrary = await fetchExerciseLibrary(db);
  const libraryData = JSON.stringify(
    exerciseLibrary.map(ex => ({ name: ex.name, libraryId: ex.libraryId }))
  );

  const systemContent = `${BASE_SYSTEM_PROMPT}
You are a Form & Technique Coach AI. Your role is to ensure exercises are performed safely and effectively. Please follow these instructions:

1. Review the client's profileText for relevant constraints or injuries.
2. Use the provided exercise library data to select an appropriate exercise. The exercise MUST have a valid libraryId from the library.
3. Provide detailed, step-by-step instructions on correct form for the selected exercise.
4. Highlight common mistakes and proper alignment cues.
5. Suggest alternatives or modifications if needed (e.g., limited mobility).
6. Output only your coaching recommendations.

Exercise Library Data (use only these exercises and their libraryIds):
${libraryData}

You MUST return ONLY a JSON object containing a valid form technique guidance instance. Do NOT include any extra text or markdown formatting.
The JSON instance MUST conform to the following requirements (do not output the schema itself):
${formTechniqueSchemaJson}

Generate a valid JSON instance that exactly follows these requirements. Every key must be present; no extra keys or null values are allowed.
`;

  const response = await openai.chat.completions.create({
    model: Model,
    messages: [
      { role: "system", content: systemContent },
      { role: "user", content: profileText },
    ],
    temperature: 0.7,
    max_tokens: MAX_TOKENS_DEFAULT,
    response_format: { type: "json_object" },
  });
  const content = response.choices[0].message.content;
  if (!content) {
    throw new Error("No content returned from OpenAI.");
  }
  try {
    const parsedContent = JSON.parse(content);
    FormTechniqueGuidanceSchema.parse(parsedContent);
    return JSON.stringify(parsedContent);
  } catch (error) {
    console.error("Invalid JSON response from formTechniqueCoachAI:", error);
    console.error("Raw content:", content);
    return JSON.stringify({
      error: "Failed to generate valid JSON",
      details: error instanceof Error ? error.message : "Unknown validation error",
      timestamp: new Date().toISOString()
    });
  }
}

// --- Long Term Planning AI ---
const LongTermPlanSchema = z.object({
  title: z.string().describe("Long Term Plan Title"),
  notes: z.array(z.string()).describe("Additional notes"),
  strategies: z.object({
    overcomePlateaus: z.string().describe("Plan to overcome plateaus"),
    maintainMotivation: z.string().describe("Plan to maintain motivation"),
    variety: z.string().describe("Plan to incorporate variety"),
  }),
  days: z.array(
    z.object({
      day: z.number().int().describe("Phase number"),
      name: z.string().describe("Phase Title"),
      location: z.string().describe("Gym or Home"),
      duration: z.number().int().describe("Duration in minutes"),
      exercises: z.array(
        z.object({
          order: z.number().int().describe("Exercise order"),
          name: z.string().describe("Exercise Name"),
          libraryId: z.string().describe("Exercise Unique Id"),
          duration: z.number().int().describe("Exercise duration"),
          details: z.string().describe("Exercise details"),
        })
      ),
      timeFrame: z.string().describe("Time frame (e.g., '0-3 months')"),
      workoutFrequency: z.string().describe("Sessions per week"),
      focus: z.string().describe("Focus areas"),
      coreWork: z.string().describe("Core work details"),
      strengthTraining: z.string().describe("Strength training details"),
      cardio: z.string().describe("Cardio details"),
      flexibility: z.string().describe("Flexibility details")
    })
  )
});

export async function longTermPlanningAI(
  userEmail: string,
  profileText: string,
  db: Firestore
): Promise<string> {
  console.log("Agent: longTermPlanningAI called for user:", userEmail);

  const exerciseLibrary = await fetchExerciseLibrary(db);
  const libraryData = JSON.stringify(
    exerciseLibrary.map(ex => ({ name: ex.name, libraryId: ex.libraryId }))
  );

  const systemContent = `${BASE_SYSTEM_PROMPT}
You are a Long-Term Planning & Goal-Setting AI. Your role is to help the user envision and plan their fitness journey beyond the short term. Please follow these instructions:

1. Review the client's profileText to understand current fitness level and goals.
2. Use the provided exercise library data to select appropriate exercises. Each exercise MUST have a valid libraryId from the library.
3. Propose a phased approach (e.g., monthly or quarterly targets) for progression in strength, endurance, or body composition.
4. Suggest strategies to overcome plateaus, maintain motivation, and introduce variety (cross-training, new activities).
5. Output only the long-term plan details, with clear milestones or timelines.

Exercise Library Data (use only these exercises and their libraryIds):
${libraryData}

You MUST return ONLY a JSON object containing a valid long-term plan data instance. Do NOT include any extra text or markdown formatting, and do NOT return the JSON schema definition.

The JSON object must have the following structure:
{
  "title": string,
  "notes": string[],
  "strategies": {
    "overcomePlateaus": string,
    "maintainMotivation": string,
    "variety": string
  },
  "days": [
    {
      "day": number,
      "name": string,
      "location": string,
      "duration": number,
      "exercises": [
        { "order": number, "name": string, "libraryId": string, "duration": number, "details": string }
      ],
      "timeFrame": string,
      "workoutFrequency": string,
      "focus": string,
      "coreWork": string,
      "strengthTraining": string,
      "cardio": string,
      "flexibility": string
    }
  ]
}

Generate a valid JSON instance that exactly follows the above structure. Every key must be present with the correct type and no additional keys or null values are allowed.
`;

  const response = await openai.chat.completions.create({
    model: Model,
    messages: [
      { role: "system", content: systemContent },
      { role: "user", content: profileText },
    ],
    temperature: 0.7,
    max_tokens: MAX_TOKENS_DEFAULT,
    response_format: { type: "json_object" },
  });
  const content = response.choices[0].message.content;
  if (!content) {
    throw new Error("No content returned from OpenAI.");
  }
  try {
    const parsedContent = JSON.parse(content);
    LongTermPlanSchema.parse(parsedContent);
    return JSON.stringify(parsedContent);
  } catch (error) {
    console.error("Invalid JSON response from LongTermPlanningAI:", error);
    console.error("Raw content:", content);
    return JSON.stringify({
      error: "Failed to generate valid JSON",
      details: error instanceof Error ? error.message : "Unknown validation error",
      timestamp: new Date().toISOString()
    });
  }
}

// --- Short Term Planning AI ---
const ShortTermPlanSchema = z.object({
  programMetadata: z.object({
    totalWeeks: z.number(),
    programGoals: z.string(),
  }),
  weeklyPlans: z.array(
    z.object({
      weekNumber: z.number(),
      focus: z.string(),
      workoutDays: z.array(
        z.object({
          day: z.string(),
          exercises: z.array(
            z.object({
              name: z.string(),
              libraryId: z.string(),
              location: z.string(),
              specs: z.string(),
              weight: z.string(),
              instructions: z.string(),
              mistakes: z.string(),
              modifications: z.string(),
            })
          ),
        })
      ),
    })
  ),
});

export async function shortTermPlanningAI(
  userEmail: string,
  profileText: string,
  exerciseProgram: string,
  formTechniqueGuidance: string,
  longTermPlan: string,
  db: Firestore
): Promise<string> {
  console.log("Agent: shortTermPlanningAI called for user:", userEmail);

  const exerciseLibrary = await fetchExerciseLibrary(db);
  const libraryData = JSON.stringify(
    exerciseLibrary.map(ex => ({ name: ex.name, libraryId: ex.libraryId }))
  );

  const systemContent = `${BASE_SYSTEM_PROMPT}
You are a Short-Term Training Planning AI.
Your role is to provide a day-by-day workout plan that fits the user's profile and immediate goals.
Please follow these instructions:

1. Review the client's profileText and any recommended exercises.
2. Use the provided exercise library data to select appropriate exercises. Each exercise MUST have a valid libraryId from the library.
3. Generate a 4-week day-by-day schedule. Each week (from weekNumber 1 to 4) must be represented in the JSON.
4. For each week, include a focus area and for each workout day, include the day and a list of exercises.
5. For every exercise, specify the following:
   - name (string)
   - libraryId (string, mandatory from the library)
   - location (string)
   - specs (string)
   - weight (string)
   - instructions (string)
   - mistakes (string)
   - modifications (string)
6. Do not include any extra keys, extra text, or markdown formatting.

Exercise Library Data (use only these exercises and their libraryIds):
${libraryData}

The JSON object MUST conform exactly to the following structure:
{
  "programMetadata": {
    "totalWeeks": number,
    "programGoals": string
  },
  "weeklyPlans": [
    {
      "weekNumber": number,
      "focus": string,
      "workoutDays": [
        {
          "day": string,
          "exercises": [
            {
              "name": string,
              "libraryId": string,
              "location": string,
              "specs": string,
              "weight": string,
              "instructions": string,
              "mistakes": string,
              "modifications": string
            }
          ]
        }
      ]
    }
  ]
}

Return only a valid JSON object that strictly follows the above structure.
`;

  const userContent = JSON.stringify({
    exerciseProgram,
    formTechniqueGuidance,
    longTermPlan,
    profileText,
  });

  const response = await openai.chat.completions.create({
    model: Model,
    messages: [
      { role: "system", content: systemContent },
      { role: "user", content: userContent },
    ],
    temperature: 0.7,
    max_tokens: MAX_TOKENS_DEFAULT,
    response_format: { type: "json_object" },
  });

  const content = response.choices[0].message.content;
  if (!content) {
    throw new Error("No content returned from OpenAI.");
  }

  try {
    const parsedContent = JSON.parse(content);
    ShortTermPlanSchema.parse(parsedContent);
    return JSON.stringify(parsedContent);
  } catch (error) {
    console.error("Invalid JSON response from shortTermPlanningAI:", error);
    console.error("Raw content:", content);
    return JSON.stringify({
      error: "Failed to generate valid JSON",
      details: error instanceof Error ? error.message : "Unknown validation error",
      timestamp: new Date().toISOString()
    });
  }
}

// --- Nutritional Advisor AI ---
const NutritionAdviceSchema = z.object({
  advice: z.string(),
  macroBalance: z.string(),
  portionControl: z.string()
});
const nutritionAdviceSchemaJson = JSON.stringify(
  zodToJsonSchema(NutritionAdviceSchema, { name: "NutritionAdviceSchema" }),
  null,
  2
);

export async function nutritionalAdvisorAI(
  profileText: string,
  userEmail: string = "<EMAIL>"
): Promise<string> {
  console.log("Agent: nutritionalAdvisorAI called for user:", userEmail);
  const systemContent = `${BASE_SYSTEM_PROMPT}
Your role is to give basic nutrition tips aligned to client's profileText. Please include:
  - Simple nutrition tips (macronutrient balance, specifiying the % Carbs, % Fat, % Protein.
  - Portion control, hydration, etc.).
  - Mention the importance of consulting foodKhare.com for personalized plans.
  - Output only dietary guidance and no additional commentary.

You MUST return ONLY a JSON object containing a valid nutrition advice instance.
Do NOT include any extra text or markdown formatting, and do NOT return the JSON schema definition.
The JSON object MUST conform to the following schema:
${nutritionAdviceSchemaJson}

Generate a valid JSON instance that exactly follows the above structure. Every key must be present with the correct data type and no extra keys.
`;

  const response = await openai.chat.completions.create({
    model: Model,
    messages: [
      { role: "system", content: systemContent },
      { role: "user", content: profileText },
    ],
    temperature: 0.7,
    max_tokens: MAX_TOKENS_DEFAULT,
    response_format: { type: "json_object" },
  });
  const content = response.choices[0].message.content;
  if (!content) {
    throw new Error("No content returned from OpenAI.");
  }
  try {
    const parsedContent = JSON.parse(content);
    NutritionAdviceSchema.parse(parsedContent);
    return JSON.stringify(parsedContent);
  } catch (error) {
    console.error("Invalid JSON response from nutritionalAdvisorAI:", error);
    console.error("Raw content:", content);
    return JSON.stringify({
      error: "Failed to generate valid JSON",
      details: error instanceof Error ? error.message : "Unknown validation error",
      timestamp: new Date().toISOString()
    });
  }
}

// --- Coach Instructions Generator AI ---
const CoachInstructionsSchema = z.object({
  workoutDays: z.array(
    z.object({
      day: z.string().describe("Day and Activity"),
      exercises: z.array(
        z.object({
          name: z.string().describe("Exercise Name"),
          libraryId: z.string().describe("Exercise Unique Id"),
          location: z.string().describe("Gym or Home"),
          specs: z.string().describe("Sets, reps, duration"),
          instructions: z.string().describe("Form guidance"),
          mistakes: z.string().describe("Common mistakes"),
          modifications: z.string().describe("Variations"),
        })
      ),
    })
  ),
});
const coachInstructionsSchemaJson = JSON.stringify(
  zodToJsonSchema(CoachInstructionsSchema, { name: "CoachInstructionsSchema" }),
  null,
  2
);

export async function coachInstructionsGeneratorAI(
  userEmail: string,
  exerciseProgram: string,
  formTechniqueGuidance: string,
  longTermPlan: string,
  shortTermPlan: string,
  db: Firestore
): Promise<string> {
  console.log("Agent: coachInstructionsGeneratorAI called for user:", userEmail);

  const exerciseLibrary = await fetchExerciseLibrary(db);
  const libraryData = JSON.stringify(
    exerciseLibrary.map(ex => ({ name: ex.name, libraryId: ex.libraryId }))
  );

  const systemContent = `${BASE_SYSTEM_PROMPT}
Your role is to analyze the exercise program and the suggested form technique guidance to create structured coaching instructions.

You will receive:
1. An exercise program containing workout days and exercises.
2. Form technique guidance with detailed instructions.
3. Exercise library data to ensure all exercises have valid libraryIds.

Your task:
1. Match each exercise with its corresponding form instructions.
2. Use the provided exercise library data to ensure every exercise has a valid libraryId.
3. Create a structured output that includes:
   - Workout preferences (training location, focus body parts, etc.)
   - Each workout day's exercises
   - Detailed form instructions for each exercise
   - Sets, reps, and other specifications
   - Common mistakes to avoid
   - Modifications if needed
4. Ensure there are exactly 7 workoutDays. For rest days, the exercises array should be empty.

Exercise Library Data (use only these exercises and their libraryIds):
${libraryData}

You MUST return ONLY a JSON object containing a valid coach instructions instance.
Do NOT include any extra text or markdown formatting, and do NOT return the JSON schema definition.
The JSON object MUST conform to the following schema:
${coachInstructionsSchemaJson}

Generate a valid JSON instance that exactly follows these requirements. Every key must be present with the correct data type and no additional keys or null values are allowed.
`;

  const summarizedProgram = extractWorkoutEssentials(exerciseProgram);
  const summarizedGuidance = extractWorkoutEssentials(formTechniqueGuidance);
  const summarizedLongPlan = extractWorkoutEssentials(longTermPlan);
  const summarizedShortPlan = extractWorkoutEssentials(shortTermPlan);
  const userContent = JSON.stringify({
    program: summarizedProgram,
    guidance: summarizedGuidance,
    longPlan: summarizedLongPlan,
    shortPlan: summarizedShortPlan,
  });

  if (estimateTokenCount(userContent) > 6000) {
    return JSON.stringify({
      workoutDays: [],
      error: "Input data exceeds token limit",
      details: "Please reduce the amount of input data",
      timestamp: new Date().toISOString(),
    }, null, 2);
  }

  const response = await openai.chat.completions.create({
    model: Model,
    messages: [
      { role: "system", content: systemContent },
      { role: "user", content: userContent },
    ],
    temperature: 0.7,
    max_tokens: MAX_TOKENS_DEFAULT,
    response_format: { type: "json_object" },
  });
  const content = response.choices[0].message.content;
  if (!content) {
    return JSON.stringify({
      workoutDays: [],
      error: "Empty response from AI model",
      timestamp: new Date().toISOString(),
    }, null, 2);
  }
  try {
    const parsedContent = JSON.parse(content);
    CoachInstructionsSchema.parse(parsedContent);
    return JSON.stringify(parsedContent);
  } catch (error) {
    console.error("Invalid JSON response from coachInstructionsGeneratorAI:", error);
    console.error("Raw content:", content);
    return JSON.stringify({
      workoutDays: [],
      error: "Failed to generate workout plan",
      details: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString()
    }, null, 2);
  }
}

// ----------------------------------------------------------------------------
// 8. Supervisor Agent
// ----------------------------------------------------------------------------

export async function supervisorAgent(
  userEmail: string,
  profileText: string,
  db: Firestore
): Promise<AgentResponses> {
  console.log("Agent: supervisorAgent called for user:", userEmail);
  try {
    const summarizedProfileText = await summarizeProfileText(userEmail, profileText);
    console.log("Summarized profile text:", summarizedProfileText);

    const [exerciseProgram, formTechniqueGuidance] = await Promise.all([
      exerciseProgrammingSpecialistAI(userEmail, summarizedProfileText, db),
      formTechniqueCoachAI(userEmail, summarizedProfileText, db),
    ]);

    const longTermPlan = await longTermPlanningAI(userEmail, summarizedProfileText, db);
    const shortTermPlan = await shortTermPlanningAI(
      userEmail,
      summarizedProfileText,
      exerciseProgram,
      formTechniqueGuidance,
      longTermPlan,
      db
    );

    const [nutritionAdvice, coachInstructions] = await Promise.all([
      nutritionalAdvisorAI(summarizedProfileText, userEmail),
      coachInstructionsGeneratorAI(userEmail, exerciseProgram, formTechniqueGuidance, longTermPlan, shortTermPlan, db),
    ]);

    const responses = {
      userProfileSummary: summarizedProfileText,
      exerciseProgram,
      formTechniqueGuidance,
      longTermPlanning: longTermPlan,
      shortTermPlanning: shortTermPlan,
      nutritionAdvice,
      coachInstructions,
      workoutPlan: {
        shortTermPlan,
        longTermPlan,
        exerciseProgram,
        formTechniqueGuidance,
      },
    };
    return responses;
  } catch (error) {
    console.error("Error in supervisorAgent:", error);
    throw error;
  }
}