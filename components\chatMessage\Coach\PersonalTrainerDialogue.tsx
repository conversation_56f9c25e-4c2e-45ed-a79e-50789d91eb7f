"use client"

// PersonalTrainerDialogue.tsx
import { useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react"
import ReactMarkdown from "react-markdown"

// Properly defined props interface
interface DialogueMessage {
  role: string
  content: string
}

// Explicit interface for component props
interface PersonalTrainerDialogueProps {
  dialogue: DialogueMessage[]
  currentResponse: string
  isSpeaking: boolean
  isListening: boolean
  connected: boolean
}

export default function PersonalTrainerDialogue({
  dialogue,
  currentResponse,
  isSpeaking,
  isListening,
  connected,
}: PersonalTrainerDialogueProps) {
  const dialogueEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to latest message
  useEffect(() => {
    dialogueEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [dialogue, currentResponse])

  // Add this effect to log received props for debugging
  useEffect(() => {
    console.log("[Dialogue] Props received - dialogue:", dialogue)
    console.log("[Dialogue] Props received - currentResponse:", currentResponse)
    console.log("[Dialogue] Props received - connected:", connected)
    console.log("[Dialogue] Props received - isSpeaking:", isSpeaking)
    console.log("[Dialogue] Props received - isListening:", isListening)
  }, [dialogue, currentResponse, connected, isSpeaking, isListening])

  // Connection status indicator
  const connectionStatus = () => {
    if (!connected) {
      return (
        <div className="text-center text-xs text-yellow-300 py-1 px-3 bg-yellow-900/20 rounded-md">
          Connect to start conversation with your Personal Trainer
        </div>
      )
    }

    if (isSpeaking) {
      return (
        <div className="text-center text-xs text-green-300 py-1 px-3 bg-green-900/20 rounded-md">
          Personal Trainer is speaking
        </div>
      )
    }

    return (
      <div className="text-center text-xs text-blue-300 py-1 px-3 bg-blue-900/20 rounded-md">
        Connection active - Ready for conversation
      </div>
    )
  }

  // Empty state display
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center h-full text-gray-400">
      <User className="w-12 h-12 mb-4 opacity-30" />
      <p className="text-lg">Conversation Transcript</p>
      <p className="text-sm mt-2 mb-4">
        {connected ? "Begin speaking to your Personal Trainer" : "Connect to start your session"}
      </p>

      {connectionStatus()}
    </div>
  )

  return (
    <div className="flex flex-col h-full">
      {/* Connection status bar */}
      {(dialogue.length > 0 || currentResponse) && (
        <div className="p-2 border-b border-white/10 bg-black/20">{connectionStatus()}</div>
      )}

      {/* Conversation display area */}
      <div className="flex-1 overflow-y-auto p-4 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
        {dialogue.length === 0 && !currentResponse ? (
          renderEmptyState()
        ) : (
          <div className="space-y-4">
            {/* Render historical dialogue messages */}
            {dialogue.map((message, index) => (
              <div key={index} className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}>
                <div
                  className={`max-w-[90%] rounded-lg p-3 ${
                    message.role === "user" ? "bg-purple-500/20 text-purple-100" : "bg-gray-700/50 text-gray-100"
                  }`}
                >
                  <div className="flex items-center mb-1">
                    <span className="text-xs font-medium">{message.role === "user" ? "You" : "Personal Trainer"}</span>
                    {message.role === "user" ? <User className="w-3 h-3 ml-1" /> : <Bot className="w-3 h-3 ml-1" />}
                  </div>

                  <div className="text-sm conversation-content">
                    <ReactMarkdown>{message.content}</ReactMarkdown>
                  </div>
                </div>
              </div>
            ))}

            {/* Current streaming response */}
            {currentResponse && (
              <div className="flex justify-start">
                <div
                  className={`max-w-[90%] rounded-lg p-3 bg-gray-700/50 text-gray-100 ${
                    isSpeaking ? "shadow-glow animate-subtle-pulse" : ""
                  }`}
                >
                  <div className="flex items-center mb-1">
                    <span className="text-xs font-medium">Personal Trainer</span>
                    <Bot className={`w-3 h-3 ml-1 ${isSpeaking ? "text-green-400" : ""}`} />
                  </div>

                  <div className="text-sm conversation-content">
                    <ReactMarkdown>{currentResponse}</ReactMarkdown>
                    {isSpeaking && <span className="inline-block ml-1 animate-pulse">▌</span>}
                  </div>
                </div>
              </div>
            )}

            {/* Auto-scroll reference element */}
            <div ref={dialogueEndRef} />
          </div>
        )}
      </div>

      {/* Informational footer */}
      <div className="p-3 border-t border-white/10 bg-black/20">
        <div className="text-xs text-gray-400 flex items-center justify-between">
          <span>
            {dialogue.length > 0
              ? `${dialogue.length} messages (${dialogue.reduce((sum, msg) => sum + (msg.content?.length || 0), 0)} total chars)`
              : "No messages yet"}
          </span>
          <span>{connected ? "Live conversation active" : "Disconnected"}</span>
        </div>
      </div>

      {/* Markdown styling */}
      <style jsx>{`
        .conversation-content :global(p) {
          margin-bottom: 0.5rem;
        }
        .conversation-content :global(ul),
        .conversation-content :global(ol) {
          margin-left: 1.5rem;
          margin-bottom: 0.5rem;
        }
        .conversation-content :global(li) {
          margin-bottom: 0.25rem;
        }
        .conversation-content :global(strong) {
          color: #f9a8d4;
          font-weight: bold;
        }
        .conversation-content :global(em) {
          color: #d1d5db;
          font-style: italic;
        }
        
        /* Custom animations for the glow effect */
        :global(.shadow-glow) {
          box-shadow: 0 0 15px 2px rgba(74, 222, 128, 0.2);
          transition: box-shadow 0.3s ease;
        }
        
        :global(.animate-subtle-pulse) {
          animation: subtle-pulse 2s infinite;
        }
        
        @keyframes subtle-pulse {
          0% {
            box-shadow: 0 0 15px 2px rgba(74, 222, 128, 0.2);
          }
          50% {
            box-shadow: 0 0 20px 4px rgba(74, 222, 128, 0.3);
          }
          100% {
            box-shadow: 0 0 15px 2px rgba(74, 222, 128, 0.2);
          }
        }
      `}</style>
    </div>
  )
}

