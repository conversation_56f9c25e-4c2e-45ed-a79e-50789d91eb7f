// components/SiteHeader.js
"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { LogOut, LogIn, LayoutDashboard } from "lucide-react";
import { useRouter } from "next/navigation";
import type { User } from "firebase/auth";
import { auth } from "@/components/firebase/config";
import { signInWithPopup, GoogleAuthProvider } from "firebase/auth";

export function SiteHeader() {
  const [user, setUser] = useState<User | null>(null);
  const router = useRouter();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser) => {
      setUser(currentUser);
    });
    return () => unsubscribe();
  }, []);

  const handleSignOut = async () => {
    try {
      await auth.signOut();
      router.push("/");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleGoogleSignIn = async () => {
    const provider = new GoogleAuthProvider();
    provider.setCustomParameters({
      prompt: "select_account",
    });
    try {
      await signInWithPopup(auth, provider);
      router.push("/"); // Redirect after successful sign-in/signup
    } catch (error) {
      console.error("Sign-in error:", error);
    }
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b border-white/10 bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 backdrop-blur">
      <div className="container mx-auto flex h-16 items-center justify-between px-4 pt-3">
        <div className="flex items-center gap-6 md:gap-10">
          <Link href="/" className="flex items-center space-x-2 ">
            <Image
              src="/images/logo2b.png"
              alt="IntelligentFitness Logo"
              width={36}
              height={32}
              className="object-contain"
              priority
            />
            <span className="text-xl font-bold text-white -ml-5">IntelligentFitness</span>
          </Link>
          <nav className="hidden md:flex gap-6">
            {["Community", "Rewards"].map((item) => (
              <Link
                key={item}
                href={`/${item.toLowerCase()}`}
                className="flex items-center text-sm font-medium text-gray-200 hover:text-white transition-colors"
              >
                {item}
              </Link>
            ))}
          </nav>
        </div>

        <div className="flex items-center space-x-4">
          <nav className="flex items-center space-x-2">
            {user ? (
              <>
                <Link
                  href="/dashboard"
                  className="md:hidden relative group"
                >
                  <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                    Dashboard
                  </span>
                  <LayoutDashboard className="h-5 w-5 text-gray-200 hover:text-white transition-colors" />
                </Link>
                <Link
                  href="/dashboard"
                  className="hidden md:block px-4 py-2 text-sm font-medium text-gray-200 hover:text-white transition-colors"
                >
                  Dashboard
                </Link>

                {user.photoURL && (
                  <Image
                    src={user.photoURL}
                    alt="Profile"
                    width={32}
                    height={32}
                    className="rounded-full border border-white/20"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/placeholder.svg";
                    }}
                  />
                )}

                <button
                  onClick={handleSignOut}
                  className="md:hidden relative group"
                >
                  <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                    Sign Out
                  </span>
                  <LogOut className="h-5 w-5 text-gray-200 hover:text-white transition-colors" />
                </button>
                <button
                  onClick={handleSignOut}
                  className="hidden md:block relative group"
                >
                  <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                    Sign Out
                  </span>
                  <LogOut className="h-5 w-5 text-gray-200 hover:text-white transition-colors" />
                </button>
              </>
            ) : (
              <>
                <Link
                  href="/auth/signin"
                  className="md:hidden relative group"
                >
                  <span className="absolute -top-8 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                    Sign In
                  </span>
                  <LogIn className="h-5 w-5 text-gray-200 hover:text-white transition-colors" />
                </Link>
                <Link
                  href="/auth/signin"
                  className="hidden md:block px-4 py-2 text-sm font-medium text-gray-200 hover:text-white transition-colors"
                >
                  Sign In
                </Link>

                <button
                  onClick={handleGoogleSignIn}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Sign Up
                </button>
              </>
            )}
          </nav>
        </div>
      </div>
    </header>
  );
}