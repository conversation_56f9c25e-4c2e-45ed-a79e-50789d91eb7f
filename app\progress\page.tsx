"use client";

import { useState, useEffect } from "react";
import { DashboardLayout } from "@/components/dashboard-layout";
import TrackingTab from "@/components/tracking-tab";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { collection, getDocs } from "firebase/firestore";
import { db } from "@/components/firebase/config";
import { motion } from "framer-motion";
import { BarChart3, AlertCircle } from "lucide-react";

export default function ProgressPage() {
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasTrackingRecords, setHasTrackingRecords] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if user is authenticated and get email
  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user && user.email) {
        setUserEmail(user.email);
        checkTrackingRecords(user.email);
      } else {
        setUserEmail(null);
        setIsLoading(false);
        setError("Please sign in to view your progress");
      }
    });

    return () => unsubscribe();
  }, []);

  // Check if user has tracking records
  const checkTrackingRecords = async (email: string) => {
    try {
      setIsLoading(true);

      // Check if tracking_records collection exists and has documents
      const recordsRef = collection(db, "IF_users", email, "tracking_records");
      const recordsSnapshot = await getDocs(recordsRef);

      setHasTrackingRecords(!recordsSnapshot.empty);
      setIsLoading(false);
    } catch (err) {
      console.error("Error checking tracking records:", err);
      setError("Failed to check tracking records");
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-lg text-white">Loading your progress data...</div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
          <div className="text-lg text-red-400">{error}</div>
        </div>
      </DashboardLayout>
    );
  }

  if (!userEmail) {
    return (
      <DashboardLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
          <AlertCircle className="h-12 w-12 text-yellow-500 mb-4" />
          <div className="text-lg text-white">Please sign in to view your progress</div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="p-6"
      >
        <div className="flex items-center mb-6">
          <BarChart3 className="h-8 w-8 text-green-400 mr-3" />
          <h1 className="text-3xl font-bold text-white">Exercise Progress Tracking</h1>
        </div>

        {!hasTrackingRecords ? (
          <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 backdrop-blur-sm shadow-2xl rounded-2xl p-6 border border-purple-500/20">
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <AlertCircle className="h-12 w-12 text-yellow-500 mb-4" />
              <h2 className="text-xl font-semibold text-white mb-2">No tracking data yet</h2>
              <p className="text-gray-300 max-w-md mb-6">
                You haven&apos;t tracked any exercises yet. Start tracking your workouts to see your progress here.
              </p>
              <a
                href="/workout-dashboard"
                className="px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
              >
                Go to Workouts
              </a>
            </div>
          </div>
        ) : (
          <div className="bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 backdrop-blur-sm shadow-2xl rounded-2xl p-6 border border-purple-500/20">
            <TrackingTab userEmail={userEmail} />
          </div>
        )}
      </motion.div>
    </DashboardLayout>
  );
}
