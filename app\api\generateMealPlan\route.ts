import { NextRequest, NextResponse } from "next/server";
import { generateMealPlan } from "@/lib/tools/mealPlanTool";
import { analyzeMealPlan } from "@/lib/tools/macroNutrientAnalyser";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { getAuth } from "firebase-admin/auth";
import { adminApp } from "@/components/firebase-admin";

interface RequestBody {
  dayCount?: number;
  includeSnacks?: boolean;
  specificDiet?: string;
  analyzeDishes?: boolean;
  userEmail?: string;
}

/**
 * API endpoint to generate a meal plan based on user preferences and nutritional advice
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse the request body first to get any provided userEmail
    const body = await request.json() as RequestBody;
    const { dayCount = 1, includeSnacks = true, specificDiet, analyzeDishes = false, userEmail: bodyUserEmail } = body;

    // Try to get user email from different auth methods
    let userEmail: string | null = null;

    // Method 1: Check NextAuth session
    const session = await getServerSession(authOptions);
    if (session?.user?.email) {
      userEmail = session.user.email;
      console.log("User authenticated via NextAuth:", userEmail);
    }

    // Method 2: Check Firebase auth token in header
    if (!userEmail) {
      const authHeader = request.headers.get("authorization");
      if (authHeader?.startsWith("Bearer ")) {
        const token = authHeader.split("Bearer ")[1];
        try {
          const adminAuth = getAuth(adminApp);
          const decodedToken = await adminAuth.verifyIdToken(token);
          userEmail = decodedToken.email || null;
          console.log("User authenticated via Firebase:", userEmail);
        } catch (authError) {
          console.error("Firebase auth error:", authError);
        }
      }
    }

    // Method 3: Use provided userEmail from request body (least secure, but needed for client-side auth)
    if (!userEmail && bodyUserEmail) {
      userEmail = bodyUserEmail;
      console.log("Using user email from request body:", userEmail);
    }

    // If no user email found, return unauthorized
    if (!userEmail) {
      return NextResponse.json(
        { success: false, error: "Unauthorized - No valid session or authentication" },
        { status: 401 }
      );
    }

    // Generate the meal plan
    console.log(`Generating meal plan for user: ${userEmail}`);
    const mealPlanResult = await generateMealPlan({
      userEmail,
      dayCount,
      includeSnacks,
      specificDiet
    });

    if (!mealPlanResult.success) {
      return NextResponse.json(
        { success: false, error: mealPlanResult.error || "Failed to generate meal plan" },
        { status: 500 }
      );
    }

    // If analyzeDishes is true, analyze the meal plan and save dishes to Firestore
    let analysisResult = null;
    let mealPlanId = null;
    if (analyzeDishes) {
      console.log("Analyzing meal plan and saving dishes to Firestore...");
      analysisResult = await analyzeMealPlan(
        userEmail,
        mealPlanResult.mealPlan,
        dayCount
      );

      // Extract the meal plan ID if available
      if (analysisResult.mealPlanId) {
        mealPlanId = analysisResult.mealPlanId;
        console.log(`Meal plan saved with ID: ${mealPlanId}`);
      }
    }

    // Log that we're returning the meal plan to the client
    console.log(`Successfully generated meal plan for user: ${userEmail}. Length: ${mealPlanResult.mealPlan.length} characters`);

    // Return the meal plan, analysis result, and meal plan ID
    return NextResponse.json({
      success: true,
      mealPlan: mealPlanResult.mealPlan,
      analysis: analysisResult,
      mealPlanId: mealPlanId
    });
  } catch (error) {
    console.error("Error generating meal plan:", error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : "Unknown error occurred" },
      { status: 500 }
    );
  }
}
