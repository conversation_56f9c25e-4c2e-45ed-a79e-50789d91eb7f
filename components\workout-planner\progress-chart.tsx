"use client"

import { useEffect, useRef } from "react"

interface WorkoutData {
  date: string
  duration: number
  intensity: number
}

const sampleData: WorkoutData[] = [
  { date: "2024-01-24", duration: 45, intensity: 7 },
  { date: "2024-01-25", duration: 60, intensity: 8 },
  { date: "2024-01-26", duration: 30, intensity: 6 },
  { date: "2024-01-27", duration: 45, intensity: 7 },
  { date: "2024-01-28", duration: 50, intensity: 8 },
  { date: "2024-01-29", duration: 40, intensity: 6 },
  { date: "2024-01-30", duration: 55, intensity: 9 },
]

export default function ProgressChart() {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas size
    canvas.width = canvas.offsetWidth * window.devicePixelRatio
    canvas.height = canvas.offsetHeight * window.devicePixelRatio
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio)

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw chart
    const padding = 40
    const width = canvas.width / window.devicePixelRatio - padding * 2
    const height = canvas.height / window.devicePixelRatio - padding * 2

    // Draw axes
    ctx.beginPath()
    ctx.moveTo(padding, padding)
    ctx.lineTo(padding, height + padding)
    ctx.lineTo(width + padding, height + padding)
    ctx.strokeStyle = "#e2e8f0"
    ctx.stroke()

    // Plot data points
    const maxDuration = Math.max(...sampleData.map((d) => d.duration))
    const xStep = width / (sampleData.length - 1)
    const yScale = height / maxDuration

    ctx.beginPath()
    ctx.moveTo(padding, height + padding - sampleData[0].duration * yScale)

    sampleData.forEach((data, i) => {
      const x = padding + i * xStep
      const y = height + padding - data.duration * yScale

      ctx.lineTo(x, y)
    })

    ctx.strokeStyle = "#3b82f6"
    ctx.lineWidth = 2
    ctx.stroke()

    // Add data points
    sampleData.forEach((data, i) => {
      const x = padding + i * xStep
      const y = height + padding - data.duration * yScale

      ctx.beginPath()
      ctx.arc(x, y, 4, 0, Math.PI * 2)
      ctx.fillStyle = "#3b82f6"
      ctx.fill()
    })

    // Add labels
    ctx.fillStyle = "#64748b"
    ctx.font = "12px system-ui"
    ctx.textAlign = "center"

    // X-axis labels
    sampleData.forEach((data, i) => {
      const x = padding + i * xStep
      const date = new Date(data.date)
      const label = date.toLocaleDateString("en-US", {
        weekday: "short",
      })
      ctx.fillText(label, x, height + padding + 20)
    })

    // Y-axis labels
    for (let i = 0; i <= 5; i++) {
      const y = height + padding - (i * height) / 5
      const label = Math.round((i * maxDuration) / 5).toString()
      ctx.textAlign = "right"
      ctx.fillText(label, padding - 10, y + 4)
    }
  }, [])

  return (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <h3 className="text-xl font-semibold mb-4">Workout Duration</h3>
      <div className="relative aspect-[2/1]">
        <canvas ref={canvasRef} className="w-full h-full" />
      </div>
      <div className="text-sm text-gray-600 text-center mt-4">Last 7 Days</div>
    </div>
  )
}

