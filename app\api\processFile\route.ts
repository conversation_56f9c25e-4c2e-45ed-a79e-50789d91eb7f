import { NextRequest, NextResponse } from "next/server";
import { setDoc, doc as firestoreDoc } from "firebase/firestore";
import { db } from "../../../components/firebase";
import { OpenAIEmbeddings } from "@langchain/openai";
import { Pinecone, RecordMetadata } from "@pinecone-database/pinecone";
import { FirestoreStore } from "@/lib/FirestoreStore";
import { processDocument } from "@/components/DocViewer/documentProcessors";
import { createGroqClient } from "@/lib/llms/groq";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";

// Type definitions
interface Document {
  pageContent: string;
  metadata: DocumentMetadata;
}

interface DocumentMetadata {
  doc_id: string;
  chunk_id: string;
  document_title: string;
  category: string;
  file_type: string;
  is_image: boolean;
  fileUrl?: string;
  processed_at?: string;
  sectionTitle: string;
  questions: string[];
  is_summary: boolean;
  content?: string;
  status?: string;
  is_error?: boolean;
  processing_stage?: string;
  error_message?: string;
  error_details?: string;
  created_at?: string;
}

interface ProcessingError extends Error {
  details?: unknown;
}

interface DocumentInfo {
  docId: string;
  fileName: string;
  fileType: string;
  category?: string;
  chunkId?: string;
}

interface RequestBody {
  docId: string;
  userId: string;
  fileName: string;
  fileType: string;
  category?: string;
  fileUrl: string;
  isImage: boolean;
}

interface GroqImageAnalysis {
  analysis: string;
  sectionTitle: string;
}

// Constants
const GROQ_VISION_MODEL = process.env.GROQ_VISION_MODEL || "llama-3.3-70b-versatile";
const DEFAULT_CHUNK_SIZE = 1500;
const DEFAULT_CHUNK_OVERLAP = 200;

// Helper functions
function cleanMetadata(metadata: Partial<DocumentMetadata>): DocumentMetadata {
  const defaultMetadata: DocumentMetadata = {
    doc_id: '',
    chunk_id: '',
    document_title: '',
    category: 'Uncategorized',
    file_type: '',
    is_image: false,
    sectionTitle: 'No Title',
    questions: [],
    is_summary: false,
    content: '',
  };

  return { ...defaultMetadata, ...metadata };
}

function createErrorMetadata(error: ProcessingError, docInfo: DocumentInfo): DocumentMetadata {
  return cleanMetadata({
    doc_id: docInfo.docId,
    chunk_id: docInfo.chunkId || docInfo.docId,
    document_title: docInfo.fileName,
    file_type: docInfo.fileType,
    category: docInfo.category || 'Uncategorized',
    error_message: error.message,
    error_details: error.details?.toString() || error.toString(),
    created_at: new Date().toISOString(),
    sectionTitle: 'Processing Error',
    status: 'failed',
    is_error: true,
    processing_stage: 'document_processing',
    is_image: false,
    questions: []
  });
}

function convertToRecordMetadata(metadata: DocumentMetadata): RecordMetadata {
  const recordMetadata: Record<string, string | number | boolean> = {
    doc_id: metadata.doc_id,
    chunk_id: metadata.chunk_id,
    document_title: metadata.document_title,
    category: metadata.category,
    file_type: metadata.file_type,
    is_image: metadata.is_image,
    sectionTitle: metadata.sectionTitle,
    is_summary: metadata.is_summary,
    questions: JSON.stringify(metadata.questions)
  };

  // Add optional fields if they exist
  if (metadata.fileUrl) recordMetadata.fileUrl = metadata.fileUrl;
  if (metadata.processed_at) recordMetadata.processed_at = metadata.processed_at;
  if (metadata.content) recordMetadata.content = metadata.content;
  if (metadata.status) recordMetadata.status = metadata.status;
  if (metadata.is_error) recordMetadata.is_error = metadata.is_error;
  if (metadata.processing_stage) recordMetadata.processing_stage = metadata.processing_stage;
  if (metadata.error_message) recordMetadata.error_message = metadata.error_message;
  if (metadata.error_details) recordMetadata.error_details = metadata.error_details;
  if (metadata.created_at) recordMetadata.created_at = metadata.created_at;

  return recordMetadata;
}

async function processImageWithGroq(
  groq: ReturnType<typeof createGroqClient>,
  imageUrl: string
): Promise<GroqImageAnalysis> {
  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `IMAGE TEXT CAPTURE:

MAIN TEXT:
[Verbatim capture of headlines, titles, main body text]

CHART/GRAPH TEXT:
- Axes labels: [x-axis, y-axis labels]
- Values/Data points: [numerical values, labels]
- Legend text: [categories, keys]
- Annotations: [notes, callouts]

SUPPLEMENTARY TEXT:
- Captions/Subtitles: [verbatim]
- Watermarks/Credits: [verbatim]
- UI elements: [buttons, menu items]

TITLE: (2-3 words capturing essence)

CORE ELEMENTS:
- Composition: Layout, focal points
- Visuals: Colors, lighting, quality
- Content: Main subjects, setting
- Impact: Mood, context

If people present:
- Appearance, expressions, interactions

TEXT PROPERTIES:
- Typography styles/hierarchy
- Text placement/flow
- Legibility/contrast`
            },
            {
              type: "image_url",
              image_url: {
                url: imageUrl
              }
            }
          ]
        }
      ],
      model: GROQ_VISION_MODEL,
      temperature: 0.7,
      max_tokens: 1024,
    });

    if (!chatCompletion?.choices?.[0]?.message?.content) {
      throw new Error("Invalid response format from Groq API");
    }

    const response = chatCompletion.choices[0].message.content;
    const titleMatch = response.match(/TITLE:\s*(.*?)\s*\n/i);
    const analysisMatch = response.match(/ANALYSIS:\s*([\s\S]*)/i);

    return {
      analysis: analysisMatch?.[1]?.trim() || response.trim(),
      sectionTitle: titleMatch?.[1] || "Image Analysis"
    };
  } catch (error) {
    const processError = error as Error;
    throw new Error(`Image processing failed: ${processError.message}`);
  }
}

export async function POST(req: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  
  if (!session?.user?.email) {
    return NextResponse.json(
      { success: false, error: "Unauthorized - No valid session" },
      { status: 401 }
    );
  }

  try {
    const body = await req.json() as RequestBody;
    const { docId, userId, category = "Uncategorized", fileName, fileType, fileUrl, isImage } = body;

    if (!docId || !userId || !fileName || !fileType || !fileUrl) {
      throw new Error("Missing required parameters");
    }

    const groq = createGroqClient({ userEmail: session.user.email });
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY!,
    });
    
    const pinecone = new Pinecone();
    const pineconeIndex = pinecone.Index(process.env.PINECONE_INDEX!);
    const byteCollection = `users/${userId}/byteStoreCollection`;
    const firestoreStore = new FirestoreStore({ collectionPath: byteCollection });

    let processedContent: Document[];
    
    try {
      if (isImage) {
        const { analysis, sectionTitle } = await processImageWithGroq(groq, fileUrl);
        processedContent = [{
          pageContent: analysis,
          metadata: cleanMetadata({
            doc_id: docId,
            chunk_id: `${docId}_1`,
            sectionTitle,
            is_summary: true,
            document_title: fileName,
            file_type: fileType,
            is_image: true
          })
        }];
      } else {
        const fileResponse = await fetch(fileUrl);
        if (!fileResponse.ok) {
          throw new Error(`Failed to fetch file: ${fileResponse.statusText}`);
        }
        
        const fileBlob = await fileResponse.blob();
        const file = new File([fileBlob], fileName, { type: fileType });
        const documentChunks = await processDocument(
          file,
          docId,
          fileType,
          fileName,
          userId,
          category,
          '',
          DEFAULT_CHUNK_SIZE,
          DEFAULT_CHUNK_OVERLAP
        );
        processedContent = documentChunks.map(chunk => ({
          pageContent: chunk.pageContent,
          metadata: cleanMetadata(chunk.metadata as unknown as DocumentMetadata)
        }));
      }

      if (!processedContent?.length) {
        throw new Error('No content was extracted from the document');
      }

      const byteStoreData: [string, Document][] = processedContent.map(doc => [
        doc.metadata.chunk_id,
        {
          pageContent: doc.pageContent,
          metadata: cleanMetadata({
            ...doc.metadata,
            document_title: fileName,
            category,
            file_type: fileType,
            is_image: isImage,
            fileUrl,
            processed_at: new Date().toISOString()
          })
        }
      ]);

      await firestoreStore.mset(byteStoreData);

      for (const doc of processedContent) {
        try {
          const embedding = await embeddings.embedQuery(doc.pageContent);
          
          await pineconeIndex.namespace(docId).upsert([
            {
              id: doc.metadata.chunk_id,
              values: embedding,
              metadata: convertToRecordMetadata(cleanMetadata({
                ...doc.metadata,
                document_title: fileName,
                category,
                file_type: fileType,
                is_image: isImage
              }))
            },
          ]);
        } catch (error) {
          const chunkError = error as ProcessingError;
          console.error(`Error processing chunk ${doc.metadata.chunk_id}:`, chunkError);
          
          await setDoc(
            firestoreDoc(db, "users", userId, "MetadataFallback", doc.metadata.chunk_id),
            createErrorMetadata(chunkError, {
              docId,
              fileName,
              fileType,
              category,
              chunkId: doc.metadata.chunk_id
            })
          );
        }
      }

      return NextResponse.json({ success: true });
      
    } catch (error) {
      const processingError = error as ProcessingError;
      console.error("Error in document processing:", processingError);
      
      await setDoc(
        firestoreDoc(db, "users", userId, "MetadataFallback", docId),
        createErrorMetadata(processingError, { docId, fileName, fileType, category })
      );

      throw processingError;
    }
    
  } catch (error) {
    const finalError = error as ProcessingError;
    console.error("Error processing file:", finalError);
    
    return NextResponse.json(
      { success: false, error: finalError.message || "Unknown error occurred" },
      { status: 500 }
    );
  }
}

export async function OPTIONS(_req: NextRequest): Promise<NextResponse> {
  return NextResponse.json({}, { status: 200 });
}