/**
 * Meal Plan Tool
 *
 * This tool generates personalized meal plans based on user preferences and nutritional advice.
 * It uses the Groq AI model via the processWithGroq function to generate detailed meal plans.
 */

import { doc, getDoc, collection, getDocs, query, orderBy, limit } from "firebase/firestore";
import { db } from "@/components/firebase/config";
import { processWithGroq } from "@/components/tools/groq-ai";

// Define interfaces for user preferences
interface UserPreferences {
  dietaryPreferences: string[];
  allergies: string[];
  cuisinePreferences: string[];
  mealComplexity: string;
  cookingTime: string;
}

// Define interface for meal plan generation options
export interface MealPlanOptions {
  userEmail: string;
  dayCount?: number;
  includeSnacks?: boolean;
  specificDiet?: string;
}

/**
 * Fetches the user's nutritional preferences from Firestore
 * @param userEmail - The user's email address
 * @returns The user's nutritional preferences
 */
async function fetchUserPreferences(userEmail: string): Promise<UserPreferences | null> {
  try {
    // Get the collection reference
    const preferencesCollectionRef = collection(db, "foodKhare_users", userEmail, "Preferences");

    // Query to get the most recent preferences document
    const q = query(preferencesCollectionRef, orderBy("updatedAt", "desc"), limit(1));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      console.log("No preferences found for user:", userEmail);
      return null;
    }

    // Get the first (most recent) document
    const prefDoc = querySnapshot.docs[0];
    const prefData = prefDoc.data();

    return {
      dietaryPreferences: prefData.dietaryPreferences || [],
      allergies: prefData.allergies || [],
      cuisinePreferences: prefData.cuisinePreferences || [],
      mealComplexity: prefData.mealComplexity || "Medium",
      cookingTime: prefData.cookingTime || "30-60"
    };
  } catch (error) {
    console.error("Error fetching user preferences:", error);
    return null;
  }
}

/**
 * Fetches the user's nutritional advice from Firestore
 * @param userEmail - The user's email address
 * @returns The user's nutritional advice
 */
async function fetchNutritionAdvice(userEmail: string): Promise<string> {
  try {
    const docRef = doc(db, "IF_users", userEmail, "Profile", "workoutplan");
    const docSnap = await getDoc(docRef);

    if (!docSnap.exists()) {
      console.warn("Workout plan document does not exist for user:", userEmail);
      return "No nutrition advice available";
    }

    const data = docSnap.data();
    const nutritionAdvice = data?.nutritionAdvice;

    if (!nutritionAdvice) {
      console.warn("Nutrition advice field is missing for user:", userEmail);
      return "No nutrition advice available";
    }

    // Check if the advice is in the format {"advice":"..."}
    if (nutritionAdvice.includes('"advice"')) {
      try {
        // Try to extract the content from the advice field
        const adviceMatch = nutritionAdvice.match(/"advice"\s*:\s*"([^"]+)"/);
        if (adviceMatch && adviceMatch[1]) {
          const adviceContent = adviceMatch[1]
            .replace(/\\"/g, '"') // Replace escaped quotes
            .replace(/\\n/g, '\n'); // Replace escaped newlines
          return adviceContent;
        }
      } catch (parseError) {
        console.error("Error parsing advice JSON:", parseError);
      }
    }

    // If we couldn't parse it as JSON, just return the raw advice
    return nutritionAdvice;
  } catch (error) {
    console.error("Error fetching nutrition advice:", error);
    return "Error retrieving nutrition data";
  }
}

/**
 * Formats user preferences into a string for the prompt
 * @param preferences - The user's preferences
 * @returns A formatted string of preferences
 */
function formatPreferences(preferences: UserPreferences | null): string {
  if (!preferences) {
    return "No specific preferences provided.";
  }

  let formattedPrefs = "";

  if (preferences.dietaryPreferences && preferences.dietaryPreferences.length > 0) {
    formattedPrefs += `Dietary Preferences: ${preferences.dietaryPreferences.join(", ")}\n`;
  }

  if (preferences.allergies && preferences.allergies.length > 0) {
    formattedPrefs += `Allergies/Intolerances: ${preferences.allergies.join(", ")}\n`;
  }

  if (preferences.cuisinePreferences && preferences.cuisinePreferences.length > 0) {
    formattedPrefs += `Preferred Cuisines: ${preferences.cuisinePreferences.join(", ")}\n`;
  }

  formattedPrefs += `Meal Complexity: ${preferences.mealComplexity}\n`;

  const cookingTimeFormatted = preferences.cookingTime === "<30"
    ? "Less than 30 minutes"
    : preferences.cookingTime === "30-60"
      ? "30-60 minutes"
      : "More than 60 minutes";

  formattedPrefs += `Cooking Time: ${cookingTimeFormatted}`;

  return formattedPrefs;
}

/**
 * Generates a meal plan based on user preferences and nutritional advice
 * @param options - Options for meal plan generation
 * @returns The generated meal plan
 */
export async function generateMealPlan(options: MealPlanOptions): Promise<{
  success: boolean;
  mealPlan: string;
  error?: string;
}> {
  try {
    const { userEmail, dayCount = 1, includeSnacks = true } = options;

    // Fetch user preferences and nutritional advice
    const [preferences, nutritionAdvice] = await Promise.all([
      fetchUserPreferences(userEmail),
      fetchNutritionAdvice(userEmail)
    ]);

    // Format preferences for the prompt
    const formattedPreferences = formatPreferences(preferences);

    // Create the prompt for the AI
    const prompt = `You are a personal trainer with expertise in planning meals for your clients. Generate a detailed ${dayCount}-day meal plan for a fitness enthusiast following these specific macronutrient requirements:

${nutritionAdvice}

User Preferences:
${formattedPreferences}

Please provide:

Complete recipes for breakfast, Actual dishes for lunch, and dishes for dinner (not just ingredient lists)
${includeSnacks ? 'Snack options throughout the day' : ''}
Pre-workout meal or snack with timing recommendation
Post-workout recovery meal or shake with timing recommendation
Approximate macronutrient breakdown for each meal
Total daily caloric estimate
Hydration recommendations throughout the day
At least one meal prep tip per day

Please ensure variety in protein sources (animal and plant-based), complex carbohydrates, and healthy fats. Include portion sizes in standard measurements (grams/ounces). The plan should be practical for someone with moderate cooking skills and regular gym attendance.`;

    console.log("Generating meal plan with prompt:", prompt);

    // Use Groq to generate the meal plan
    const result = await processWithGroq({
      prompt,
      model: "deepseek-r1-distill-llama-70b", // Using DeepSeek model for detailed meal plans
      modelOptions: {
        temperature: 0.7,
        maxTokens: 4000 // This will be converted to max_tokens in processWithGroq
      }
    });

    // Log the generated meal plan
    console.log("AI-Generated Meal Plan:", result);

    return {
      success: true,
      mealPlan: result
    };
  } catch (error) {
    console.error("Error generating meal plan:", error);
    return {
      success: false,
      mealPlan: "",
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}
