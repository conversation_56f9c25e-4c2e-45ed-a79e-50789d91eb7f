"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON> } from "lucide-react"
import Link from "next/link"
import type React from "react"

export default function WorkoutPlannerLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isScrolled, setIsScrolled] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0)
    }
    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sticky Header */}
      <header
        className={`sticky top-0 w-full z-50 transition-all duration-200 ${
          isScrolled ? "bg-white shadow-md" : "bg-transparent"
        }`}
      >
        <div className={`container mx-auto px-4 transition-all duration-200`}>
          <div className="flex flex-col items-center">
            {/* Top Navigation */}
            <nav className="hidden lg:flex items-center justify-center gap-6 h-6 pt-12 transition-all duration-200">
              <Link href="/workout-planner/today" className="text-gray-900 hover:text-blue-600 font-medium">
                Today&apos;s Workout
              </Link>
              <Link href="/workout-planner/progress" className="text-gray-900 hover:text-blue-600 font-medium">
                Progress
              </Link>
              <Link href="/workout-planner/library" className="text-gray-900 hover:text-blue-600 font-medium">
                Exercise Library
              </Link>
              <div className="flex items-center gap-4 transition-all duration-200 ml-auto text-gray-900">
                <button className="p-2 hover:bg-gray-100 rounded-full relative">
                  <Bell className="w-6 h-6" />
                  <span
                    className={`absolute top-1 right-1 w-2 h-2 rounded-full ${
                      isScrolled ? "bg-red-500" : "bg-gray-900"
                    }`}
                  ></span>
                </button>
              </div>
            </nav>

            {/* Bottom Bar */}
            <div className={`w-full flex items-center h-16 ${isScrolled ? "justify-start gap-8" : "justify-between"}`}>
              <div className="flex items-center gap-4">
                <button className="lg:hidden p-2 hover:bg-gray-100 rounded-lg">
                  <Menu className="w-6 h-6" />
                </button>
                <div className="relative hidden lg:block w-96"></div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="pt-30">{children}</main>

      {/* Floating Action Button */}
      <button
        className="fixed bottom-6 right-6 bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors"
        onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
      >
        <Menu className="w-6 h-6" />
      </button>
    </div>
  )
}

