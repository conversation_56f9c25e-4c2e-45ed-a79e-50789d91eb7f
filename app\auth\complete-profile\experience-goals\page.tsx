'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { CheckCircle } from 'lucide-react'

export default function ExperienceAndGoals() {
  const [experienceLevel, setExperienceLevel] = useState<string | null>(null)
  const [fitnessGoals, setFitnessGoals] = useState<string[]>([])
  const router = useRouter()

  const experienceLevels = ['Beginner', 'Intermediate', 'Advanced', 'Professional']
  const goalOptions = ['Weight Loss', 'Muscle Gain', 'Endurance', 'Strength', 'Flexibility', 'Other']

  const handleGoalToggle = (goal: string) => {
    setFitnessGoals(prev => 
      prev.includes(goal) ? prev.filter(g => g !== goal) : [...prev, goal]
    )
  }

  const handleContinue = () => {
    if (experienceLevel && fitnessGoals.length > 0) {
      // Store the selected experience level and fitness goals
      localStorage.setItem('experienceLevel', experienceLevel)
      localStorage.setItem('fitnessGoals', JSON.stringify(fitnessGoals))
      router.push('/auth/complete-profile/workout-availability')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Your Experience and Goals
          </h2>
        </div>
        <div className="mt-8 space-y-6">
          <div>
            <label className="text-sm font-medium text-gray-700">Experience Level</label>
            <div className="mt-2 space-y-2">
              {experienceLevels.map((level) => (
                <button
                  key={level}
                  onClick={() => setExperienceLevel(level)}
                  className={`relative w-full flex items-center justify-start px-4 py-2 border ${
                    experienceLevel === level ? 'border-blue-600' : 'border-gray-300'
                  } text-sm font-medium rounded-md ${
                    experienceLevel === level ? 'bg-blue-50 text-blue-700' : 'bg-white text-gray-700'
                  } hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                >
                  {level}
                </button>
              ))}
            </div>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Fitness Goals (Select all that apply)</label>
            <div className="mt-2 space-y-2">
              {goalOptions.map((goal) => (
                <button
                  key={goal}
                  onClick={() => handleGoalToggle(goal)}
                  className={`relative w-full flex items-center justify-start px-4 py-2 border ${
                    fitnessGoals.includes(goal) ? 'border-blue-600' : 'border-gray-300'
                  } text-sm font-medium rounded-md ${
                    fitnessGoals.includes(goal) ? 'bg-blue-50 text-blue-700' : 'bg-white text-gray-700'
                  } hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
                >
                  {goal}
                  {fitnessGoals.includes(goal) && (
                    <CheckCircle className="h-5 w-5 text-blue-600 absolute right-2" />
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
        <button
          onClick={handleContinue}
          disabled={!experienceLevel || fitnessGoals.length === 0}
          className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          Continue
        </button>
      </div>
    </div>
  )
}

