/**
 * Groq integration for the LLM tool
 *
 * This file contains all Groq AI functionality for the application, including:
 * - General purpose Groq processing with SDK
 * - DeepSeek model integration for fitness summaries
 * - Utility functions for tracking data
 */

// Import the Groq SDK
import Groq from "groq-sdk";

// Define the Groq API endpoint for direct API calls
const GROQ_API_ENDPOINT = "https://api.groq.com/openai/v1/chat/completions";

// Define interfaces for Groq processing

export interface GroqModelOptions {
  temperature?: number;
  maxTokens?: number;
  top_p?: number;
  reasoning_format?: string;
  response_format?: { type: string };
  [key: string]: unknown;
}

export interface GroqProcessingOptions {
  prompt: string;
  model?: string;
  modelOptions?: GroqModelOptions;
}

/**
 * Process content with Groq
 * @param options - Processing options
 * @param options.prompt - The prompt to send to the LLM
 * @param options.model - The model to use (default: "llama-3.3-70b-versatile")
 * @param options.modelOptions - Additional model-specific options
 * @returns The generated content
 */
export async function processWithGroq(options: GroqProcessingOptions): Promise<string> {
  try {
    const {
      prompt,
      model = "llama-3.3-70b-versatile",
      modelOptions = {}
    } = options;

    console.log(`Processing with Groq model: ${model}`);

    // Get the API key from environment variables
    const groqApiKey = process.env.GROQ_API_KEY;

    if (!groqApiKey) {
      console.error('GROQ_API_KEY environment variable is not set');
      throw new Error('GROQ_API_KEY environment variable is not set');
    }

    // Initialize the Groq client
    const groq = new Groq({ apiKey: groqApiKey });

    // Check if it's a DeepSeek model to add specific parameters
    const isDeepSeek = model.toLowerCase().includes('deepseek');

    // Log the model being used
    console.log(`Using Groq model: ${model} (DeepSeek: ${isDeepSeek})`);

    // Validate model
    const availableModels = getGroqModels();
    if (!availableModels.includes(model)) {
      console.warn(`Model ${model} not in available Groq models list. Available models: ${availableModels.join(', ')}`);
      // Continue anyway as Groq might have added new models
    }

    // Create the parameters object with the correct types for Groq SDK
    const messages = [
      {
        role: "user" as const,
        content: prompt
      }
    ];

    // Combine all parameters
    // Use a more specific type for the parameters
    const combinedParams: Record<string, unknown> = {
      messages,
      model,
      temperature: modelOptions.temperature !== undefined ? modelOptions.temperature : 0.4,
      max_tokens: modelOptions.maxTokens !== undefined ? modelOptions.maxTokens : 7000,
      ...(isDeepSeek ? { top_p: 0.95 } : {})
    };

    // Handle special parameter conversions
    if (modelOptions.response_format) {
      // Check if the prompt contains the word 'json' when using json_object response format
      if (modelOptions.response_format.type === 'json_object' &&
          !prompt.toLowerCase().includes('json')) {
        console.log("Adding JSON instruction to prompt for json_object response format");
        // Don't modify the original prompt, but add a note in the logs
      }
      combinedParams.response_format = modelOptions.response_format;
    }

    // Remove maxTokens from modelOptions to avoid conflicts with max_tokens
    // Use underscore prefix to indicate it's intentionally unused
    const { maxTokens: _maxTokens, ...restModelOptions } = modelOptions;

    // Add remaining model options
    Object.assign(combinedParams, restModelOptions);

    // Add reasoning_format only for DeepSeek models
    if (isDeepSeek) {
      combinedParams.reasoning_format = "hidden";
    } else if (combinedParams.reasoning_format) {
      delete combinedParams.reasoning_format; // Remove if present from modelOptions for non-DeepSeek models
    }

    // Get chat completion using the Groq API
    // We need to cast here because the Groq SDK has complex typing requirements
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const chatCompletion = await groq.chat.completions.create(combinedParams as any);

    // Return the text content from the completion
    // Handle both stream and non-stream responses
    if ('choices' in chatCompletion && Array.isArray(chatCompletion.choices) && chatCompletion.choices.length > 0) {
      return chatCompletion.choices[0]?.message?.content || "";
    }

    return "";
  } catch (error: unknown) {
    console.error("Error processing content with Groq:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return `Error from Groq: ${errorMessage}`;
  }
}

/**
 * Get available Groq models
 * @returns List of available models
 */
export function getGroqModels(): string[] {
  return [
    "llama-3.3-70b-versatile",
    "meta-llama/llama-4-maverick-17b-128e-instruct",
    "deepseek-r1-distill-llama-70b",
    process.env.Deep_Seek_Model || "deepseek-r1-distill-llama-70b"
  ];
}

/**
 * Generate a summary using the deepSeek model via Groq API
 * @param prompt The prompt to send to the model
 * @returns The generated summary
 */
export async function generateSummaryWithDeepSeek(prompt: string): Promise<string> {
  try {
    // Get the API key from environment variables
    const apiKey = process.env.GROQ_API_KEY;

    if (!apiKey) {
      console.error('GROQ_API_KEY environment variable is not set');
      throw new Error("GROQ_API_KEY environment variable is not set");
    }

    // Prepare the request to the Groq API
    const response = await fetch(GROQ_API_ENDPOINT, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        model: process.env.Deep_Seek_Model || "deepseek-r1-distill-llama-70b",
        messages: [
          {
            role: "system",
            content: `You are a fitness expert analyzing workout tracking data. Provide concise, insightful summaries
            focused on the main takeaways from the analysis and advice for moving forward rather than dwelling on detailed specifics.`
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.5,
        max_tokens: 2000,
        reasoning_format: "hidden"
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Groq API error: ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();

    // Extract the summary from the response
    const summary = data.choices[0]?.message?.content;

    if (!summary) {
      throw new Error("No summary generated from the model");
    }

    return summary;
  } catch (error) {
    console.error("Error generating summary with deepSeek:", error);
    throw error;
  }
}

/**
 * Fetch tracking data as JSON
 * @param userEmail The user's email
 * @returns The tracking data as a JSON string
 */
export async function fetchTrackingDataAsJSON(userEmail: string): Promise<string> {
  try {
    // This function would typically fetch tracking data from Firebase
    // For now, we'll just return a placeholder
    console.log(`Fetching tracking data for user: ${userEmail}`);
    return "[]";
  } catch (error) {
    console.error("Error fetching tracking data:", error);
    throw error;
  }
}