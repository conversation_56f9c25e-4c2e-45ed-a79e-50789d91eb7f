
// Coach Instructions
export interface CoachInstructions {
  instructions: string;
  mistakes: string;
  modifications: string;
}

// Exercise Specifications
export interface ExerciseSpecs {
  reps: string;
  sets: string;
  weight?: string;
  duration: string; // Required
  intensity?: string;
}

export interface ScheduleExercise {
  exerciseName: string;
  libraryId: string;
  coachInstructions: CoachInstructions;
  specs: ExerciseSpecs;
  completed: boolean;
}

// Existing type definitions remain the same
export interface SevenDaySchedule {
  schedule: {
    [day: string]: {
      focus: string
      location: string
      duration: string
      intensity: string
      coreWork: string
      cardio: string
      exercises: {
        exerciseName: string
        specs: {
          duration: string
          weight?: string
        }
        completed: boolean
        coachInstructions: {
          instructions: string
          mistakes: string
          modifications: string
        }
      }[]
    }
  }
}

// Exercise
export interface Exercise {
  exerciseName: string;
  libraryId: string;  // Required
  coachInstructions: CoachInstructions;
  specs: ExerciseSpecs;
  completed: boolean;
  details?: string;   // Optional details
}

// Day Schedule
export interface DaySchedule {
  name: string;     // Added name
  exercises: Exercise[];
  duration: string;
  intensity: string;
  focus: string;
  completed: boolean; // Added
  location: string;  //Added
  timeFrame: string;//Added
  workoutFrequency: string; //Added
  coreWork: string; //Added
  strengthTraining: string;//Added
  cardio: string; //Added
  flexibility: string; //Added
}

// Schedule
export interface Schedule {
  [key: string]: DaySchedule;
}

// Long Term Schedule
export interface LongTermSchedule {
  phases: {
    [phaseKey: `Phase ${number}`]: {
      [dayKey: string]: DaySchedule;
    };
  };
  notes?: string[];
  strategies?: {
    maintainMotivation: string;
    overcomePlateaus: string;
    variety: string;
  };
}

// Kick Starter Schedule (Not directly used in this component, but kept for completeness)
export interface KickStarterSchedule {
  lastUpdated?: string;
  schedule?: {
    [key: string]: DaySchedule;
  };
  goals?: {
    primary?: string;
    secondary?: string;
  };
}


// Update PersonalTrainerSchedule interface
export interface PersonalTrainerSchedule {
  schedule: {
    [dayKey: string]: {
      exercises: Array<{
        exerciseName: string
        libraryId: string
        coachInstructions: {
          instructions: string
          mistakes: string
          modifications: string
        }
        specs: string
        weight: string
        completed: boolean
        location: string
      }>
    }
  }
  trainerNotes?: {
    general: string
    form: string
    progression: string
  }
  assessments?: {
    initial: {
      strength: string
      endurance: string
      flexibility: string
    }
    goals: string[]
  }
}



// Workout Schedule from Firebase
export interface WorkoutSchedule {
  workoutDays: DaySchedule[]
  lastUpdated?: Date
  scheduleId?: string
  workoutReference?: string
}

// Seven Day Schedule
export interface SevenDaySchedule {
  schedule: {
    [day: string]: {
      focus: string
      location: string
      duration: string
      intensity: string
      coreWork: string
      cardio: string
      exercises: {
        exerciseName: string
        specs: {
          duration: string
          weight?: string
        }
        completed: boolean
        coachInstructions: {
          instructions: string
          mistakes: string
          modifications: string
        }
      }[]
    }
  }
  weeklyGoals: {
    primary: string
    secondary: string[]
  }
  recovery: {
    restDays: string[]
    stretchingRoutine: string
    coolDown: string
  }
  nutrition: {
    preworkout: string
    postworkout: string
    hydration: string
  }
}


