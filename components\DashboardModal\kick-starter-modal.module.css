/* kick-starter-modal.module.css */

/* Overlay style for modal background */
.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-start; /* Align from the top */
    justify-content: center;
    z-index: 1000; /* High z-index to overlay other content */
    padding-top: 5rem; /* Push content down below the header */
  }
  
  /* Container for modal content */
  .modalContent {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 0.5rem;
    width: 90%;
    max-width: 800px;
    max-height: calc(90vh - 5rem); /* Adjust height to account for header padding */
    overflow-y: auto;
    position: relative;
    /* Use a darker base text color throughout the modal content */
    color: #2d3748;
  }
  
  /* Header section of the modal */
  .modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .modalHeader h2 {
    /* Dark heading color */
    color: #1a202c;
  }
  
  /* Close button style */
  .closeButton {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #2d3748; /* Ensure close icon is visible against white background */
  }
  
  /* Modal body container */
  .modalBody {
    padding: 1rem 0;
  }
  
  /* Optional tab list (if you add tabs in the future) */
  .tabList {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 1rem;
  }
  
  .tabButton {
    padding: 0.5rem 1rem;
    border: none;
    background: none;
    cursor: pointer;
    font-weight: 600;
    /* Darker text color for contrast */
    color: #2d3748;
  }
  
  .tabButton.active {
    color: #3182ce;
    border-bottom: 2px solid #3182ce;
  }
  
  /* General card style */
  .card {
    background-color: #f7fafc;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  /* Specific style for day cards within the schedule */
  .dayCard {
    background-color: #f7fafc;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
  }
  
  /* Badge style */
  .badge {
    background-color: #e2e8f0;
    color: #2d3748;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
  }
  
  /* Progress bar container */
  .progressBar {
    height: 0.5rem;
    background-color: #e2e8f0;
    border-radius: 9999px;
    overflow: hidden;
  }
  
  /* Filled portion of the progress bar */
  .progressBarFill {
    height: 100%;
    background-color: #3182ce;
    transition: width 0.3s ease-in-out;
  }
  
  /* Container for exercises within a day */
  .exercises {
    margin-top: 1rem;
  }
  
  /* Exercise style */
  .exercise {
    background-color: #edf2f7;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 0.5rem;
  }
  
  /* Exercise header */
  .exerciseHeader {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }
  
  /* Details for each exercise */
  .exerciseDetails {
    font-size: 0.875rem;
    color: #2d3748;
  }
  