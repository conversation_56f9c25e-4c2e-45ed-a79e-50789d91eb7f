"use client"

import type React from "react" // Import React
import { <PERSON><PERSON>ircle, Loader2, AlertCircle, X } from "lucide-react"
import type { ProgressStep } from "@/types/progress"

interface ProgressPopupProps {
  currentStep: ProgressStep
  steps: ProgressStep[]
  error?: string
  onClose?: () => void
}

export const ProgressPopup: React.FC<ProgressPopupProps> = ({ currentStep, steps, error, onClose }) => {
  const currentIndex = steps.indexOf(currentStep)
  const progress = ((currentIndex + 1) / steps.length) * 100

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 relative">
        {onClose && (
          <button onClick={onClose} className="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        )}

        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-2">Generating Your Plan</h3>
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-blue-600 transition-all duration-500 ease-out"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        <div className="space-y-4">
          {steps.map((step, index) => (
            <div
              key={step}
              className={`flex items-center gap-3 ${
                index === currentIndex ? "text-blue-600" : index < currentIndex ? "text-gray-400" : "text-gray-300"
              }`}
            >
              {index < currentIndex ? (
                <CheckCircle className="w-5 h-5 flex-shrink-0" />
              ) : index === currentIndex ? (
                <Loader2 className="w-5 h-5 flex-shrink-0 animate-spin" />
              ) : (
                <div className="w-5 h-5 border-2 rounded-full flex-shrink-0" />
              )}
              <span className="text-sm">{step}</span>
            </div>
          ))}
        </div>

        {error && (
          <div className="mt-4 p-4 bg-red-50 text-red-600 rounded-md flex items-start gap-2">
            <AlertCircle className="w-5 h-5 flex-shrink-0 mt-0.5" />
            <span className="text-sm">{error}</span>
          </div>
        )}
      </div>
    </div>
  )
}

