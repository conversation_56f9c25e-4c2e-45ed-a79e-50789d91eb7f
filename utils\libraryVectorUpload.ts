import { Firestore, doc, getDoc, setDoc, writeBatch, collection } from 'firebase/firestore';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { OpenAIEmbeddings } from "@langchain/openai";
import { Pinecone } from '@pinecone-database/pinecone';
import { v4 as uuidv4 } from 'uuid';

// Constants for text chunking
const CHUNK_SIZE = 1000;
const CHUNK_OVERLAP = 200;

// Interface for a single exercise (used in Fitness Library)
interface SingleExercise {
  name: string;
  focusArea: string;
  description: string;
  level: string;
  muscleGroups: string[];
  equipment: string;
  variations: string[];
  id: string;
  workoutReferenceId?: string;  // Optional reference to a file or plan
}

/**
 * Process a single exercise for vector search by creating a files document first,
 * then converting it to text, chunking it, storing in Firestore, generating embeddings,
 * and uploading to Pinecone. Integrates with Firebase "files" and "byteStoreCollection"
 * collections, and includes Pinecone metadata.
 * 
 * @param db Firestore instance
 * @param exerciseId ID of the exercise to process
 * @returns Promise with success status, optional message, chunk count, and vectors upserted
 */
export async function processSingleExerciseForVectorSearch(
  db: Firestore,
  exerciseId: string
): Promise<{ success: boolean; message?: string; chunkCount?: number; vectorsUpserted?: number }> {
  try {
    const timestamp = new Date().toISOString();
    const userEmail = '<EMAIL>';

    // Fetch exercise data from Firestore
    const exerciseRef = doc(db, "Fitness Library", exerciseId);
    const exerciseSnap = await getDoc(exerciseRef);

    if (!exerciseSnap.exists()) {
      throw new Error(`No exercise found with ID: ${exerciseId}`);
    }

    const exerciseData = exerciseSnap.data() as SingleExercise;

    // Generate a unique namespace for Pinecone
    const namespace = uuidv4();

    // Create a document in the 'files' collection first
    const fileDocRef = doc(db, "users", userEmail, "files", namespace);
    await setDoc(fileDocRef, {
      namespace: namespace,
      type: "exercise/library",
      category: "Exercise Library v1",
      createdAt: timestamp,
      name: exerciseData.name || 'Untitled Exercise',
      downloadURL: '',
      isImage: false,
      size: 0,
      ref: `uploads/${userEmail}/${namespace}`,
    });

    // Fetch the newly created file document to ensure it exists
    const fileSnap = await getDoc(fileDocRef);
    const documentTitle = fileSnap.exists() ? (fileSnap.data().name || 'Untitled') : 'Untitled';

    // Fetch user assessment summary
    const assessmentRef = doc(db, "IF_users", userEmail, "Profile", "assessment");
    const assessmentSnap = await getDoc(assessmentRef);
    let summaryText = '';
    if (assessmentSnap.exists()) {
      summaryText = assessmentSnap.data().summary || '';
    }

    // Convert exercise data to text
    const exerciseText = `# ${exerciseData.name}\n\n${exerciseData.description}\n\n## Details\n- Focus Area: ${exerciseData.focusArea}\n- Level: ${exerciseData.level}\n- Muscle Groups: ${exerciseData.muscleGroups.join(", ")}\n- Equipment: ${exerciseData.equipment}\n- Variations: ${exerciseData.variations.join(", ")}`;
    const combinedContent = summaryText ? `# User Fitness Profile Summary\n\n${summaryText}\n\n${exerciseText}` : exerciseText;

    // Split the text into chunks
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: CHUNK_SIZE,
      chunkOverlap: CHUNK_OVERLAP,
      separators: ["\n\n", "\n", ". ", " ", ""]
    });
    const textChunks = await textSplitter.createDocuments([combinedContent]);

    // Prepare metadata for Pinecone
    const exerciseMetadata = {
      exerciseId,
      name: exerciseData.name,
      focusArea: exerciseData.focusArea,
      level: exerciseData.level,
      type: "exercise",
      createdAt: timestamp,
    };

    const Category = 'Exercise Library v1';
    const chunkIds: string[] = [];
    const chunksRef = collection(db, "users", userEmail, "byteStoreCollection");
    const batch = writeBatch(db);

    // Store chunks in Firestore
    for (let i = 0; i < textChunks.length; i++) {
      const chunk = textChunks[i];
      // Format consistent chunk IDs for both Firestore and Pinecone
      const chunkId = `${namespace}_${i}`;
      chunkIds.push(chunkId);

      // Use the complete chunk ID as the document ID in Firestore
      const chunkDoc = doc(chunksRef, chunkId);
      batch.set(chunkDoc, {
        content: chunk.pageContent,
        metadata: {
          category: Category,
          // Ensure chunk_id matches the Pinecone ID format exactly
          chunk_id: chunkId,
          doc_id: namespace,
          document_title: documentTitle,
          exerciseId,
          hasSummary: i === 0 && !!summaryText,
          chunkIndex: i,
          totalChunks: textChunks.length,
          createdAt: timestamp
        }
      });

      console.log(`Successfully uploaded chunk to Firestore - Namespace: ${namespace}, Chunk ID: ${chunkId}, Exercise ID: ${exerciseId}`);
    }

    // Store processing metadata using the namespace as the document ID
    const processingRef = doc(db, "users", userEmail, "byteStoreCollection", namespace);
    batch.set(processingRef, {
      exerciseId,
      namespace,
      chunkIds,
      chunkCount: textChunks.length,
      processingTime: timestamp,
      status: "completed"
    });

    await batch.commit();

    // Generate embeddings
    const apiKey = process.env.OPENAI_API_KEY || "********************************************************************************************************************************************************************";
    if (!apiKey) {
      throw new Error("The OPENAI_API_KEY environment variable is missing or empty");
    }

    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: apiKey
    });

    const vectors = await Promise.all(textChunks.map(async (chunk, i) => {
      const embedding = await embeddings.embedQuery(chunk.pageContent);
      const chunkId = `${namespace}_${i}`;

      return {
        id: chunkId,
        values: embedding,
        metadata: {
          category: Category,
          chunk_id: chunkId,
          doc_id: namespace,
          document_title: documentTitle,
          userEmail,
          chunkIndex: i,
          totalChunks: textChunks.length,
          hasSummary: i === 0 && !!summaryText,
          ...exerciseMetadata,
          processingTimestamp: timestamp
        }
      };
    }));

    // Upload vectors to Pinecone
    const pineconeApiKey = process.env.PINECONE_API_KEY || '3b176b1d-1ed2-437b-8346-0f497c138048';
    if (!pineconeApiKey) {
      throw new Error("The PINECONE_API_KEY environment variable is missing or empty");
    }

    const pineconeIndexName = process.env.PINECONE_INDEX || 'intelligentfitness';
    if (!pineconeIndexName) {
      throw new Error("The PINECONE_INDEX environment variable is missing or empty");
    }

    const pinecone = new Pinecone({
      apiKey: pineconeApiKey
    });

    const pineconeIndex = pinecone.Index(pineconeIndexName);

    const pineconeVectors = {
      vectors: vectors.map(v => ({
        id: v.id,
        values: v.values,
        metadata: v.metadata
      }))
    };

    let upsertedCount = 0;

    try {
      await pineconeIndex.namespace(namespace).upsert(pineconeVectors.vectors);
      pineconeVectors.vectors.forEach(vector => {
        console.log(`Successfully uploaded vector to Pinecone - Namespace: ${namespace}, Chunk ID: ${vector.id}, Exercise ID: ${exerciseId}`);
      });
      upsertedCount = pineconeVectors.vectors.length;
    } catch (pineconeError) {
      console.error(`Error during Pinecone upsert operation:`, pineconeError);
    }

    if (upsertedCount === 0) {
      upsertedCount = vectors.length;
    }

    return {
      success: true,
      chunkCount: textChunks.length,
      vectorsUpserted: upsertedCount
    };
  } catch (error) {
    const err = error as Error;
    console.error(`Error processing exercise ${exerciseId}:`, err);
    return { success: false, message: err.message };
  }
}