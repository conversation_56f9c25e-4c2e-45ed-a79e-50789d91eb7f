import { type Firestore, collection, query, where, getDocs } from "firebase/firestore"
import { OpenAIEmbeddings } from "@langchain/openai"
import { Pinecone } from "@pinecone-database/pinecone"

// Constants for configuration
const DEFAULT_CONFIG = {
  openAiApiKey:
    process.env.OPENAI_API_KEY ||
    "********************************************************************************************************************************************************************",
  pineconeApiKey: process.env.PINECONE_API_KEY || "3b176b1d-1ed2-437b-8346-0f497c138048",
  pineconeIndexName: process.env.PINECONE_INDEX || "intelligentfitness",
  userEmail: "<EMAIL>",
  confidenceThreshold: 0.7,
  topK: 1,
  verbose: false,
  retryAttempts: 2,
}

export interface LibraryNamespace {
  namespace: string
  name: string
  description: string
  libraryId: string  
}

interface VectorSearchResult {
  libraryId: string
  confidence: number
  exerciseName: string
  matchDetails?: any
}

/**
 * Retrieves all exercise library namespaces from Firestore
 *
 * @param userEmail User email (typically the admin email for the exercise library)
 */
/**
 * Retrieves all exercise library namespaces from the global Fitness Library in Firestore
 *
 * @param db Firestore instance
 * @returns Array of namespace objects conforming to LibraryNamespace interface
 */
export async function getExerciseLibraryNamespaces(
  db: Firestore
): Promise<LibraryNamespace[]> {
  try {
    console.log("Fetching exercise library namespaces from /Fitness Library");

    // Construct the top-level collection reference
    const filesCollectionRef = collection(db, "Fitness Library");

    // Query all documents in the Fitness Library collection
    const libraryQuery = query(filesCollectionRef);

    const querySnapshot = await getDocs(libraryQuery);

    if (querySnapshot.empty) {
      console.warn("No exercise library namespaces found in /Fitness Library");
      return [];
    }

    const namespaces: LibraryNamespace[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();

      // Validate and extract required fields with fallbacks
      const namespace = typeof data.namespace === "string" ? data.namespace : doc.id;
      const name = typeof data.name === "string" ? data.name : "Unnamed Exercise";
      const description = typeof data.description === "string" ? data.description : "No description found";

      namespaces.push({
        namespace,
        name,
        description,
        libraryId: doc.id, // Firestore document ID is always a string
      });
    });

    console.log(`Found ${namespaces.length} exercise library namespaces`);
    return namespaces;
  } catch (error) {
    console.error("Error fetching exercise library namespaces from /Fitness Library:", error);
    throw new Error(
      `Failed to fetch exercise library namespaces: ${
        error instanceof Error ? error.message : String(error)
      }`
    );
  }
}

/**
 * Initializes the OpenAI embeddings model
 *
 * @returns Initialized OpenAIEmbeddings instance
 */
function initializeEmbeddings(): OpenAIEmbeddings {
  const apiKey = DEFAULT_CONFIG.openAiApiKey

  if (!apiKey) {
    throw new Error("The OPENAI_API_KEY environment variable is missing or empty")
  }

  return new OpenAIEmbeddings({
    openAIApiKey: apiKey,
  })
}

/**
 * Initializes the Pinecone client
 *
 * @returns Initialized Pinecone instance
 */
function initializePinecone(): { pinecone: Pinecone; indexName: string } {
  const pineconeApiKey = DEFAULT_CONFIG.pineconeApiKey
  if (!pineconeApiKey) {
    throw new Error("The PINECONE_API_KEY environment variable is missing or empty")
  }

  const pineconeIndexName = DEFAULT_CONFIG.pineconeIndexName
  if (!pineconeIndexName) {
    throw new Error("The PINECONE_INDEX environment variable is missing or empty")
  }

  const pinecone = new Pinecone({
    apiKey: pineconeApiKey,
  })

  return { pinecone, indexName: pineconeIndexName }
}

/**
 * Search for the best matching exercise across all namespaces in Pinecone
 *
 * @param exerciseName The name of the exercise to match
 * @param namespaces Array of library namespaces to search within
 * @param topK Number of results to return from Pinecone (default: 1)
 * @returns Promise with the best matching library ID and confidence score
 */
export async function findBestMatchingExercise(
  exerciseName: string,
  namespaces: LibraryNamespace[],
  topK: number = DEFAULT_CONFIG.topK,
): Promise<VectorSearchResult> {
  try {
    console.log(`Finding best match for exercise: "${exerciseName}" across ${namespaces?.length || 0} namespaces`)

    if (!exerciseName || !exerciseName.trim()) {
      throw new Error("Exercise name cannot be empty")
    }

    if (!namespaces || namespaces.length === 0) {
      throw new Error("No namespaces provided for search")
    }

    // Ensure topK is a valid number
    if (typeof topK !== "number" || topK < 1) {
      topK = DEFAULT_CONFIG.topK
    }

    // Initialize OpenAI embeddings
    const embeddings = initializeEmbeddings()

    // Generate embedding for the exercise name
    const exerciseEmbedding = await embeddings.embedQuery(exerciseName)

    // Initialize Pinecone
    const { pinecone, indexName } = initializePinecone()
    const pineconeIndex = pinecone.Index(indexName)

    // Perform parallel searches across all namespaces
    const searchPromises = namespaces.map(async (ns) => {
      try {
        const result = await pineconeIndex.namespace(ns.namespace).query({
          vector: exerciseEmbedding,
          topK,
          includeMetadata: true,
        })

        // Return null if no matches found
        if (!result.matches || result.matches.length === 0) {
          return null
        }

        // Return the top match with its namespace
        return {
          namespace: ns.namespace,
          match: result.matches[0],
          libraryId: ns.libraryId,
          name: ns.name,
        }
      } catch (error) {
        console.error(`Error querying namespace ${ns.namespace}:`, error)
        return null
      }
    })

    // Wait for all searches to complete
    const searchResults = (await Promise.all(searchPromises)).filter(Boolean)

    if (searchResults.length === 0) {
      console.warn(`No matches found for exercise: "${exerciseName}"`)
      return {
        libraryId: "",
        confidence: 0,
        exerciseName: exerciseName,
      }
    }

    // Find the result with the highest score across all namespaces
    let bestMatch = searchResults.length > 0 ? searchResults[0] : null;
  
    for (const result of searchResults) {
      if (!result) continue;

      if (
        !bestMatch ||
        (result.match?.score != null &&
          (bestMatch.match?.score == null || result.match.score > bestMatch.match.score))
      ) {
        bestMatch = result;
      }
    }

    // Extract the exerciseId from the metadata if available
    const metadata = bestMatch?.match?.metadata as any
    const libraryId = metadata?.exerciseId || bestMatch?.libraryId || ""

    console.log(
      `Best match for "${exerciseName}": ${metadata?.name || bestMatch?.name} (ID: ${libraryId}) with confidence ${bestMatch?.match?.score}`,
    )

    return {
      libraryId,
      confidence: bestMatch?.match?.score || 0,
      exerciseName: metadata?.name || bestMatch?.name || exerciseName,
      matchDetails: bestMatch?.match,
    }
  } catch (error) {
    console.error(`Error finding match for exercise "${exerciseName}":`, error)

    // Return empty result on error
    return {
      libraryId: "",
      confidence: 0,
      exerciseName: exerciseName,
    }
  }
}

/**
 * Find best matching exercise using vector search across Pinecone namespaces
 * This is the main function that replaces the previous matchExerciseToLibrary
 *
 * @param db Firestore instance
 * @param exerciseName Name of the exercise to match
 * @param options Optional configuration and pre-fetched namespaces
 * @returns Promise with the matched library ID and confidence
 */
export async function matchExerciseToLibraryVectors(
  db: Firestore,
  exerciseName: string,
  options: { namespaces?: LibraryNamespace[]; topK?: number } = {},
): Promise<{ libraryId: string; confidence: number; explanation: string }> {
  try {
    if (!exerciseName || !exerciseName.trim()) {
      return {
        libraryId: "",
        confidence: 0,
        explanation: "Empty exercise name provided",
      }
    }

    // Fetch namespaces if not provided
    const searchNamespaces = options.namespaces || (await getExerciseLibraryNamespaces(db))

    if (!searchNamespaces || searchNamespaces.length === 0) {
      console.warn("No exercise library namespaces available for searching")
      return {
        libraryId: "",
        confidence: 0,
        explanation: "No exercise library namespaces available",
      }
    }

    // Ensure topK is a valid number
    const topK = typeof options.topK === "number" && options.topK > 0 ? options.topK : DEFAULT_CONFIG.topK

    const result = await findBestMatchingExercise(exerciseName, searchNamespaces, topK)

    if (!result.libraryId) {
      return {
        libraryId: "",
        confidence: 0,
        explanation: `No match found for "${exerciseName}"`,
      }
    }

    return {
      libraryId: result.libraryId,
      confidence: result.confidence,
      explanation: `Vector match: "${exerciseName}" → "${result.exerciseName}" (score: ${result.confidence.toFixed(4)})`,
    }
  } catch (error) {
    console.error(`Error in vector matching for exercise "${exerciseName}":`, error)
    return {
      libraryId: "",
      confidence: 0,
      explanation: `Error: ${error instanceof Error ? error.message : String(error)}`,
    }
  }
}

/**
 * Utility function to batch process multiple exercises for better performance
 *
 * @param db Firestore instance
 * @param exerciseNames Array of exercise names to match
 * @param config Optional configuration parameters
 * @returns Promise with array of match results
 */
export async function batchMatchExercisesToLibrary(
  db: Firestore,
  exerciseNames: string[],
  config: { topK?: number; batchSize?: number } = {},
): Promise<
  Array<{
    exerciseName: string
    libraryId: string
    confidence: number
    explanation: string
  }>
> {
  try {
    if (!exerciseNames || exerciseNames.length === 0) {
      return []
    }

    // Fetch namespaces once for all exercises
    const namespaces = await getExerciseLibraryNamespaces(db)

    if (!namespaces || namespaces.length === 0) {
      return exerciseNames.map((name) => ({
        exerciseName: name,
        libraryId: "",
        confidence: 0,
        explanation: "No exercise library namespaces available",
      }))
    }

    // Process exercises in batches to avoid rate limiting
    const BATCH_SIZE = config.batchSize || 3
    const topK = typeof config.topK === "number" && config.topK > 0 ? config.topK : DEFAULT_CONFIG.topK

    const results: Array<{
      exerciseName: string
      libraryId: string
      confidence: number
      explanation: string
    }> = []

    for (let i = 0; i < exerciseNames.length; i += BATCH_SIZE) {
      const batch = exerciseNames.slice(i, i + BATCH_SIZE)

      const batchPromises = batch.map(async (name) => {
        if (!name || typeof name !== "string") {
          return {
            exerciseName: String(name || ""),
            libraryId: "",
            confidence: 0,
            explanation: "Invalid exercise name",
          }
        }

        const result = await matchExerciseToLibraryVectors(db, name, {
          namespaces,
          topK,
        })

        return {
          exerciseName: name,
          ...result,
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Add a small delay between batches
      if (i + BATCH_SIZE < exerciseNames.length) {
        await new Promise((resolve) => setTimeout(resolve, 500))
      }
    }

    return results
  } catch (error) {
    console.error("Error in batch exercise matching:", error)

    // Return minimal results with error explanations
    return exerciseNames.map((name) => ({
      exerciseName: String(name || ""),
      libraryId: "",
      confidence: 0,
      explanation: `Batch processing error: ${error instanceof Error ? error.message : String(error)}`,
    }))
  }
}

