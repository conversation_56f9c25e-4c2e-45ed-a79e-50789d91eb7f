//utils/firebase.ts

import { doc, setDoc, getDoc, collection, getDocs, query } from 'firebase/firestore';
import { db } from '@/components/firebase/config';
import type { AccountData, ProfileData } from '@/types/user';
import { getAuth } from "firebase/auth";

export type Exercise = {
  exerciseName: string
  libraryId: string
  specs: {
    reps: string
    sets: string
    weight?: string
  }
  coachInstructions: {
    instructions: string
    mistakes: string
    modifications: string
  }
  completed: boolean
}

export type DaySchedule = {
  exercises: Exercise[]
}

export async function createUserAccount(email: string, accountData: AccountData) {
  await setDoc(doc(db, 'Accounts', email), {
    ...accountData,
    IF: true,
    createdAt: new Date().toISOString(),
    city: '',
    country: '',
  });
}

export async function getUserAccount(email: string): Promise<AccountData | null> {
  const accountRef = doc(db, 'Accounts', email);
  const accountSnap = await getDoc(accountRef);
  return accountSnap.exists() ? accountSnap.data() as AccountData : null;
}

export async function updateUserAccount(email: string, accountData: Partial<AccountData>) {
  const accountRef = doc(db, 'Accounts', email);
  await setDoc(accountRef, accountData, { merge: true });
}

export async function getUserProfile(email: string) {
  const profileRef = doc(db, 'IF_users', email, 'Profile', 'fitness');
  const profileSnap = await getDoc(profileRef);
  return profileSnap.exists() ? profileSnap.data() as ProfileData : null;
}

export async function getProfileStatus(email: string) {
  const profileStatus = doc(db, 'IF_users', email, 'Profile', 'assessment');
  const profileStatusSnap = await getDoc(profileStatus);
  return profileStatusSnap.exists() ? profileStatusSnap.data() as ProfileData : null;
}

export async function updateUserProfile(email: string, profileData: Partial<ProfileData> & { profileComplete?: boolean, lastUpdated?: string }) {
  const profileRef = doc(db, 'IF_users', email, 'Profile', 'fitness');
  await setDoc(profileRef, profileData, { merge: true });
}

// Utility function for assessment summary (if not already in utils/firebase.ts)
export async function getAssessmentSummary(userId: string): Promise<string> {
  const assessmentRef = doc(db, "IF_users", userId, "Profile", "assessment")
  const assessmentSnap = await getDoc(assessmentRef)
  return assessmentSnap.exists() ? assessmentSnap.data().summary || "" : ""
}

export async function checkWorkoutPlanExists(userEmail: string): Promise<boolean> {
  try {
    // Array of workout plan document names to check
    const workoutPlanDocuments = ["schedule", "shortTerm", "longTerm", "kick_starter"];

    // Check each document for existence
    for (const documentName of workoutPlanDocuments) {
      const workoutPlanRef = doc(db, `IF_users/${userEmail}/Profile`, documentName);
      const workoutPlanSnap = await getDoc(workoutPlanRef);

      // If the document exists, return true
      if (workoutPlanSnap.exists()) {
        return true;
      }
    }

    // If no workout plans are found, return false
    return false;
  } catch (error) {
    console.error("Error checking workout plan existence:", error);
    return false;
  }
}

// Check if the workoutplan subcollection exists under IF_users/{userEmail}/Profile
export async function checkWorkoutPlanDocumentExists(userEmail: string): Promise<boolean> {
  try {
    // Reference workoutplan as a document within the Profile collection
    const workoutPlanRef = doc(db, "IF_users", userEmail, "Profile", "workoutplan");
    const docSnapshot = await getDoc(workoutPlanRef);
    
    // Check if the document exists and return the result
    return docSnapshot.exists();
  } catch (error) {
    console.error("Error checking workout plan document:", error);
    // Return false on error to prevent UI crashes
    return false;
  }
}




export async function getUserNamespace(): Promise<string | null> {
  try {
    const auth = getAuth();
    const currentUser = auth.currentUser;
    if (!currentUser || !currentUser.email) {
      console.log("No user is currently logged in");
      return null;
    }
    const userEmail = currentUser.email;
    const workoutPlanRef = doc(db, "IF_users", userEmail, "Profile", "workoutplan");
    const docSnapshot = await getDoc(workoutPlanRef);
    if (docSnapshot.exists()) {
      const data = docSnapshot.data();
      return data.WorkoutReference || null;
    } else {
      console.log("Workout plan document does not exist for user:", userEmail);
      return null;
    }
  } catch (error) {
    console.error("Error retrieving user namespace:", error);
    return null;
  }
}



export async function getSchedule(userId: string) {
  const scheduleRef = collection(db, "IF_users", userId, "schedule")
  const snapshot = await getDocs(scheduleRef)

  const schedule: Record<string, DaySchedule> = {}

  snapshot.forEach((doc) => {
    schedule[doc.id] = doc.data() as DaySchedule
  })

  return schedule
}

export async function getSelectedPlanName(userEmail: string): Promise<string | null> {
    try {
      const planRefs = {
        kickStarter: doc(db, "IF_users", userEmail, "Profile", "kick_starter"),
        shortTerm: doc(db, "IF_users", userEmail, "Profile", "shortTerm"),
        longTerm: doc(db, "IF_users", userEmail, "Profile", "longTerm"),
        schedule: doc(db, "IF_users", userEmail, "Profile", "schedule"),
      };

      const [kickStarterDoc, shortTermDoc, longTermDoc, scheduleDoc] = await Promise.all([
        getDoc(planRefs.kickStarter),
        getDoc(planRefs.shortTerm),
        getDoc(planRefs.longTerm),
        getDoc(planRefs.schedule),
      ]);

      if (kickStarterDoc.exists()) return "kick_starter";
      if (shortTermDoc.exists()) return "shortTerm";
      if (longTermDoc.exists()) return "longTerm";
      if (scheduleDoc.exists()) return "schedule";
      return null;
    } catch (error: unknown) {
      console.error("Error fetching selected plan name:", error);
      return null;
    }
  };