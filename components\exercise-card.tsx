// components/exercise-card.tsx
"use client"

import Image from "next/image"
import { useState } from "react"
import EnhancedMarkdownContent from "@/components/chatMessage/EnhancedMarkdownContent"
import { Play } from "lucide-react"

interface Exercise {
  name: string
  focusArea: string
  description: string
  level: string
  muscleGroups: string[]
  equipment: string
  variations: string[]
  image?: string
  video?: string
  urlVideo?: string
  videoId?: string
  videoThumbnail?: string
  id: string
}

interface ExerciseCardProps {
  exercise: Exercise
  onClick: () => void
}

export default function ExerciseCard({ exercise, onClick }: ExerciseCardProps) {
  const [imageError, setImageError] = useState(false);

  // Determine if we have video content
  const hasVideo = Boolean(exercise.urlVideo || exercise.videoId);
  
  // Determine what to show in the thumbnail area
  const thumbnailSrc = exercise.videoThumbnail || exercise.image;
  
  // Handle image loading errors
  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div
      className="bg-slate-800 rounded-2xl overflow-hidden hover:bg-slate-700 transition-all duration-300 cursor-pointer"
      onClick={onClick}
    >
      <div className="aspect-video relative bg-gradient-to-br from-slate-700 to-slate-800">
        {thumbnailSrc && !imageError ? (
          <>
            <Image 
              src={thumbnailSrc} 
              alt={exercise.name} 
              fill 
              className="object-cover" 
              onError={handleImageError}
            />
            {/* Show play button overlay for videos */}
            {hasVideo && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/30 hover:bg-black/50 transition-colors">
                <div className="w-16 h-16 rounded-full bg-blue-500/70 flex items-center justify-center">
                  <Play className="w-8 h-8 text-white fill-white" />
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="w-12 h-12 rounded-full bg-slate-600" />
          </div>
        )}
      </div>
      
      <div className="p-6">
        <div className="flex items-start justify-end mb-2 w-full">
          
        <span className="px-3 py-1 bg-blue-900/50 rounded-full text-xs font-medium text-blue-300 text-right shrink-0">
      {exercise.level}
    </span>
        </div>
        
        <div className="flex items-center gap-2 mb-3">
          <span className="text-sm text-gray-300 line-clamp-3"> 
            <EnhancedMarkdownContent
              content={
                exercise.description.length > 150
                  ? `${exercise.description.slice(0, 150)}...`
                  : exercise.description
              }
            />
          </span>
        </div>
        
        <div className="flex flex-wrap gap-1 mt-3">
          <span className="px-2 py-1 bg-slate-700/50 rounded-md text-xs text-blue-300">
            {exercise.focusArea}
          </span>
          <span className="px-2 py-1 bg-slate-700/50 rounded-md text-xs text-purple-300">
            {exercise.equipment}
          </span>
          {hasVideo && (
            <span className="px-2 py-1 bg-blue-900/30 rounded-md text-xs text-blue-300 flex items-center gap-1">
              <Play className="w-3 h-3" /> Video
            </span>
          )}
        </div>
      </div>
    </div>
  );
}