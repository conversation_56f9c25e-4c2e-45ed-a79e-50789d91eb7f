import { ReactNode } from "react";
import Link from "next/link";
import { Home, Dumbbell, TrendingUp, Users, User, FileSearch2Icon } from "lucide-react";
import Image from "next/image";
interface DashboardLayoutProps {
  children: ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="max-h-screen">
      <div className="container mx-auto px-4 py-8">
      <Image
          src="/gym.png"
          alt="Fitness background"
          width={1920}
          height={1080}
          className="absolute  inset-0 w-full h-full object-cover rounded-3xl opacity-60 z-[-10]"
          priority
        />
        <div className="flex flex-col md:flex-row gap-6">
          <aside className="hidden md:block w-full md:w-64 bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 backdrop-blur-sm shadow-2xl rounded-2xl p-4 border border-purple-500/20">
            <nav>
              <ul className="space-y-2">
                <li>
                  <Link
                    href="/dashboard"
                    className="group flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                  >
                    <Home className="mr-3 h-5 w-5 text-blue-400 group-hover:text-white" />
                    Dashboard
                  </Link>
                </li>
                <li>
                  <Link
                    href="/workout-dashboard"
                    className="group flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                  >
                    <Dumbbell className="mr-3 h-5 w-5 text-red-400 group-hover:text-white" />
                    My Workouts
                  </Link>
                </li>
                <li>
                  <Link
                    href="/progress"
                    className="group flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                  >
                    <TrendingUp className="mr-3 h-5 w-5 text-green-400 group-hover:text-white" />
                    Progress
                  </Link>
                </li>
                <li>
                  <Link
                    href="/dashboard/community"
                    className="group flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                  >
                    <Users className="mr-3 h-5 w-5 text-yellow-400 group-hover:text-white" />
                    Community
                  </Link>
                </li>
                <li>
                  <Link
                    href="/library"
                    className="group flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                  >
                    <FileSearch2Icon className="mr-3 h-5 w-5 text-pink-400 group-hover:text-white" />
                    Exercise Library
                  </Link>
                </li>
                <li>
                  <Link
                    href="/profile/complete"
                    className="group flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                  >
                    <User className="mr-3 h-5 w-5 text-teal-400 group-hover:text-white" />
                    Edit Profile
                  </Link>
                </li>
              </ul>
            </nav>
          </aside>
          <main className="flex-1">{children}</main>
        </div>
      </div>
    </div>
  );
}