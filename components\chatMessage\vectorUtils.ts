// vectorUtils.ts
import { OpenAIEmbeddings } from "@langchain/openai";

/**
 * Converts a text prompt to vector embedding using OpenAI's embedding service
 * 
 * @param prompt - The text prompt to convert to a vector
 * @returns A promise that resolves to an array of numbers representing the vector embedding
 * @throws Error if prompt is empty or vector conversion fails
 */
export async function convertPromptToVector(prompt: string): Promise<number[]> {
  try {
    if (!prompt || prompt.trim() === "") {
      throw new Error("Prompt cannot be empty");
    }

    const apiKey = '********************************************************************************************************************************************************************'; // Consider moving to env variables
    if (!apiKey) {
      throw new Error("OpenAI API key is missing.");
    }

    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: apiKey,
    });

    const queryVector = await embeddings.embedQuery(prompt);
    if (!queryVector || !Array.isArray(queryVector)) {
      throw new Error("Invalid vector returned from OpenAI embeddings");
    }

    return queryVector;
  } catch (error) {
    console.error("Error converting prompt to vector:", error);
    throw new Error(`Failed to convert prompt to vector: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

export default { convertPromptToVector };