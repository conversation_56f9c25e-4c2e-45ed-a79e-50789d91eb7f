// getRelevantHistory.ts
import { OpenAIEmbeddings } from "@langchain/openai";

/**
 * Converts text to vector embeddings using OpenAI API
 * 
 * @param text - Text to be converted to embeddings
 * @returns Promise resolving to embedding vector
 */
async function getEmbedding(text: string): Promise<number[]> {
  try {
    // Use the same API key as in vectorUtils for consistency
    const apiKey = process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************';
    
    const embeddings = new OpenAIEmbeddings({
      openAIApiKey: apiKey,
    });
    
    const vector = await embeddings.embedQuery(text);
    if (!vector || !Array.isArray(vector)) {
      throw new Error("Invalid vector returned from OpenAI embeddings");
    }
    
    return vector;
  } catch (error) {
    console.error("Error generating embeddings:", error);
    throw new Error(`Failed to generate embeddings: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

/**
 * Calculates the cosine similarity between two vectors
 * 
 * @param vecA - First vector
 * @param vecB - Second vector
 * @returns Cosine similarity value between -1 and 1
 */
export function calculateCosineSimilarity(vecA: number[], vecB: number[]): number {
  if (vecA.length !== vecB.length) {
    throw new Error("Vectors must have the same dimensions for cosine similarity calculation");
  }
  
  // Calculate dot product
  const dotProduct = vecA.reduce((acc, cur, i) => acc + cur * vecB[i], 0);
  
  // Calculate magnitudes
  const magnitudeA = Math.sqrt(vecA.reduce((acc, cur) => acc + cur * cur, 0));
  const magnitudeB = Math.sqrt(vecB.reduce((acc, cur) => acc + cur * cur, 0));
  
  // Handle edge case of zero magnitude
  if (magnitudeA === 0 || magnitudeB === 0) {
    return 0;
  }
  
  // Return cosine similarity
  return dotProduct / (magnitudeA * magnitudeB);
}

/**
 * Determines if chat history is relevant to the current query and returns only relevant parts
 * 
 * @param currentQuery - The user's current message
 * @param chatHistory - The complete chat history as a formatted string
 * @param similarityThreshold - Minimum similarity score to consider history relevant (default: 0.65)
 * @returns Promise resolving to relevant chat history or empty string if not relevant
 */
export async function getRelevantHistory(
  currentQuery: string, 
  chatHistory: string, 
  similarityThreshold: number = 0.65
): Promise<string> {
  try {
    // If no chat history, return empty string immediately
    if (!chatHistory || chatHistory.trim() === "") {
      return "";
    }
    
    // Clean and prepare the query
    const cleanedQuery = currentQuery.trim().toLowerCase();
    
    // If the query is too short, include all history as relevant
    if (cleanedQuery.length < 5) {
      return chatHistory;
    }
    
    // Generate embeddings for the current query
    const queryEmbedding = await getEmbedding(cleanedQuery);
    
    // Process chat history into conversation segments
    const segments = chatHistory
      .split('\n')
      .filter(line => line.trim() !== '')
      .reduce((acc: string[], line) => {
        // Group by user/assistant turns
        if (line.startsWith('User:') || line.startsWith('Assistant:')) {
          acc.push(line);
        } else if (acc.length > 0) {
          // Append to the last segment if it's a continuation
          acc[acc.length - 1] += '\n' + line;
        }
        return acc;
      }, []);
      
    // If there are fewer than 2 segments, return all history
    if (segments.length < 2) {
      return chatHistory;
    }
    
    // Group segments into context windows (pairs of user-assistant exchanges)
    const contextWindows: string[] = [];
    for (let i = 0; i < segments.length - 1; i += 2) {
      if (i + 1 < segments.length) {
        // Create a context window from a user-assistant pair
        contextWindows.push(`${segments[i]}\n${segments[i+1]}`);
      } else {
        // Handle odd number of segments
        contextWindows.push(segments[i]);
      }
    }
    
    // Calculate relevance scores for each context window
    const relevanceScores = await Promise.all(
      contextWindows.map(async (window) => {
        const windowEmbedding = await getEmbedding(window);
        return calculateCosineSimilarity(queryEmbedding, windowEmbedding);
      })
    );
    
    // Select relevant windows based on threshold
    const relevantWindows = contextWindows.filter(
      (_, index) => relevanceScores[index] >= similarityThreshold
    );
    
    // If no windows are relevant enough, return the most recent conversation turn
    if (relevantWindows.length === 0) {
      // Include just the most recent exchange to maintain minimal continuity
      return contextWindows[contextWindows.length - 1];
    }
    
    // Join relevant windows into a single history string
    return relevantWindows.join('\n\n');
  } catch (error) {
    console.error("Error filtering relevant history:", error);
    
    // On error, return all history to ensure conversation continuity
    // Rather than fail completely, we default to including all history
    return chatHistory;
  }
}

export default getRelevantHistory;