import {
  doc,
  getDoc,
  setDoc,
  Firestore,
  DocumentData
} from "firebase/firestore";
import { v4 as uuidv4 } from "uuid";
import type { ScheduleExercise } from "../types/schedule";

// ----------------------------------------------------------------------
// Complete Type Definitions
// ----------------------------------------------------------------------

export interface DaySchedule {
  name: string;
  location: string;
  duration: string;
  timeFrame: string;
  workoutFrequency: string;
  focus: string;
  coreWork: string;
  strengthTraining: string;
  cardio: string;
  flexibility: string;
  exercises: ScheduleExercise[];
  completed?: boolean;
}

export interface Schedule {
  [key: `Day ${number}`]: DaySchedule;
}

interface Strategies {
  overcomePlateaus: string;
  maintainMotivation: string;
  variety: string;
}

interface LongTermPlanData {
  title: string;
  notes: string[];
  strategies: Strategies;
  days: LongTermDayData[];
}

// The flexibility field is optional because the JSON may not include it.
interface LongTermDayData {
  day: number;
  name: string;
  location: string;
  duration: string | number;
  exercises: LongTermExerciseData[];
  timeFrame: string;
  workoutFrequency: string;
  focus: string;
  coreWork: string;
  strengthTraining: string;
  cardio: string;
  flexibility?: string;
}

interface LongTermExerciseData {
  order: number;
  name: string;
  libraryId?: string; // libraryId is now expected in the input
  duration: string | number;
  details: string;
}

interface WorkoutPlanData extends DocumentData {
  WorkoutReference?: string;
}

interface FileData extends DocumentData {
  longTermPlanning?: string;
}

// ----------------------------------------------------------------------
// Type Guards and Validation
// ----------------------------------------------------------------------

function isStrategies(obj: unknown): obj is Strategies {
  if (!obj || typeof obj !== 'object') return false;
  const strategies = obj as Record<string, unknown>;
  return (
    typeof strategies.overcomePlateaus === 'string' &&
    typeof strategies.maintainMotivation === 'string' &&
    typeof strategies.variety === 'string'
  );
}

function isLongTermExerciseData(obj: unknown): obj is LongTermExerciseData {
  if (!obj || typeof obj !== 'object') return false;
  const exercise = obj as Record<string, unknown>;
  return (
    typeof exercise.order === 'number' &&
    typeof exercise.name === 'string' &&
    (typeof exercise.libraryId === 'string' || typeof exercise.libraryId === 'undefined') &&
    (typeof exercise.duration === 'string' || typeof exercise.duration === 'number') &&
    typeof exercise.details === 'string'
  );
}

function isLongTermDayData(obj: unknown): obj is LongTermDayData {
  if (!obj || typeof obj !== 'object') return false;
  const day = obj as Record<string, unknown>;
  return (
    typeof day.day === 'number' &&
    typeof day.name === 'string' &&
    typeof day.location === 'string' &&
    (typeof day.duration === 'string' || typeof day.duration === 'number') &&
    Array.isArray(day.exercises) &&
    day.exercises.every(isLongTermExerciseData) &&
    typeof day.timeFrame === 'string' &&
    typeof day.workoutFrequency === 'string' &&
    typeof day.focus === 'string' &&
    typeof day.coreWork === 'string' &&
    typeof day.strengthTraining === 'string' &&
    typeof day.cardio === 'string' &&
    (typeof day.flexibility === 'undefined' || typeof day.flexibility === 'string')
  );
}

function isLongTermPlanData(obj: unknown): obj is LongTermPlanData {
  if (!obj || typeof obj !== 'object') return false;
  const plan = obj as Record<string, unknown>;
  return (
    typeof plan.title === 'string' &&
    Array.isArray(plan.notes) &&
    plan.notes.every(note => typeof note === 'string') &&
    isStrategies(plan.strategies) &&
    Array.isArray(plan.days) &&
    plan.days.every(isLongTermDayData)
  );
}

// ----------------------------------------------------------------------
// Helper Functions with Enhanced Type Safety
// ----------------------------------------------------------------------

function sanitizeString(str: string | number | undefined): string {
  if (typeof str === "undefined") return "";
  return String(str).replace(/[\r\n\t]/g, " ").trim();
}

function safeParseJSON(jsonString: string): LongTermPlanData | null {
  try {
    let cleanedString = jsonString.trim();

    if (cleanedString.startsWith("```")) {
      const lines = cleanedString.split("\n");
      if (lines[0].startsWith("```")) lines.shift();
      if (lines[lines.length - 1].startsWith("```")) lines.pop();
      cleanedString = lines.join("\n");
    }

    const parsed = JSON.parse(cleanedString);

    if (!isLongTermPlanData(parsed)) {
      console.error("Invalid data structure - failed type validation");
      return null;
    }

    // Ensure flexibility is set, even if missing, and sort exercises by order.
    return {
      title: parsed.title,
      notes: parsed.notes,
      strategies: parsed.strategies,
      days: parsed.days.map(day => ({
        ...day,
        flexibility: day.flexibility || "",
        exercises: day.exercises.sort((a, b) => a.order - b.order)
      }))
    };
  } catch (error) {
    console.error("Error parsing JSON:", error);
    return null;
  }
}

function createExercise(exerciseData: LongTermExerciseData): ScheduleExercise {
  const sanitizedName = sanitizeString(exerciseData.name);
  const detailsStr = exerciseData.details || "";
  const setsRepsMatch = detailsStr.match(/(\d+)\s*sets?\s*of\s*(\d+)\s*reps?/i);

  return {
    exerciseName: sanitizedName,
    libraryId: exerciseData.libraryId ? sanitizeString(exerciseData.libraryId) : "",
    completed: false, // Default value set to false
    coachInstructions: {
      instructions: sanitizeString(exerciseData.details),
      mistakes: "Watch for proper form and technique",
      modifications: "Adjust intensity and movements as needed based on comfort level"
    },
    specs: {
      sets: setsRepsMatch ? sanitizeString(setsRepsMatch[1]) : "",
      reps: setsRepsMatch ? sanitizeString(setsRepsMatch[2]) : "",
      duration: sanitizeString(exerciseData.duration)
    }
  };
}

// ----------------------------------------------------------------------
// Main Function with Enhanced Type Safety
// ----------------------------------------------------------------------

export async function generateAndStoreLongTermSchedule(
  db: Firestore,
  userEmail: string,
  _documentId: string
): Promise<void> {
  try {
    const workoutPlanRef = doc(db, "IF_users", userEmail, "Profile", "workoutplan");
    const workoutPlanSnap = await getDoc(workoutPlanRef);

    if (!workoutPlanSnap.exists()) {
      throw new Error("No workout plan found");
    }

    const workoutPlanData = workoutPlanSnap.data() as WorkoutPlanData;
    const workoutReference = workoutPlanData?.WorkoutReference;

    if (!workoutReference) {
      throw new Error("No workout reference found in workout plan");
    }

    const fileRef = doc(db, "users", userEmail, "files", workoutReference);
    const fileSnap = await getDoc(fileRef);

    if (!fileSnap.exists()) {
      throw new Error("No workout file found in Firestore");
    }

    const fileData = fileSnap.data() as FileData;
    if (!fileData?.longTermPlanning || typeof fileData.longTermPlanning !== "string") {
      throw new Error("Invalid long term planning data");
    }

    const longTermPlan = safeParseJSON(fileData.longTermPlanning);
    if (!longTermPlan?.days || !longTermPlan.title) {
      throw new Error("Invalid long term planning structure");
    }

    const schedule: Schedule = {};

    for (const dayData of longTermPlan.days) {
      const dayKey = `Day ${dayData.day}` as keyof Schedule;
      
      const daySchedule: DaySchedule = {
        name: dayData.name,
        location: dayData.location,
        duration: String(dayData.duration),
        timeFrame: dayData.timeFrame,
        workoutFrequency: dayData.workoutFrequency,
        focus: dayData.focus,
        coreWork: dayData.coreWork,
        strengthTraining: dayData.strengthTraining,
        cardio: dayData.cardio,
        flexibility: dayData.flexibility || "",
        exercises: [],
        completed: false // Default value set to false
      };

      daySchedule.exercises = dayData.exercises.map(exercise =>
        createExercise(exercise)
      );

      schedule[dayKey] = daySchedule;
    }

    const scheduleRef = doc(db, "IF_users", userEmail, "Profile", "longTerm");
    const scheduleData = {
      scheduleId: uuidv4(),
      schedule,
      lastUpdated: new Date().toISOString(),
      workoutReference,
      title: longTermPlan.title,
      notes: longTermPlan.notes,
      strategies: longTermPlan.strategies
    };

    await setDoc(scheduleRef, scheduleData);
    console.log("Long term workout schedule successfully stored in Firestore");
  } catch (error) {
    console.error("Error generating long term workout schedule:", error);
    throw error;
  }
}