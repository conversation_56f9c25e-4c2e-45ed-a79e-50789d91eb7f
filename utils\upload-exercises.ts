import { collection, addDoc, getDocs, query, where } from "firebase/firestore"
import { db } from "@/components/firebase/config"
import { exercise_library } from "@/lib/exercise-library"
import { getGroqClient, callGroqAi } from "@/components/groqClient"
import { getAuth } from "firebase/auth"

interface UploadProgress {
  total: number
  uploaded: number
  skipped: number
  failed: number
}

interface SingleExerciseResult {
  success: boolean
  libraryId?: string
  message: string
  error?: string
}

const EXERCISE_ENHANCEMENT_PROMPT = `You are a fitness expert. For any given workout name provided, please output a detailed guide formatted in markdown that includes the following sections:

1. **Definition:**
   Provide a brief, informal definition of the workout, including the primary muscles targeted and its main purpose.

2. **How to Perform:**
   Outline a step-by-step guide on how to perform the workout correctly in an informal tone. Include details such as setup, proper form, recommended repetitions or techniques, and any relevant equipment information.

3. **Benefits of:**
   List the key benefits of the workout in a casual, straightforward manner. Highlight what areas it improves (e.g., muscle strength, endurance, flexibility) and any additional advantages it may offer.

4. **Common Mistakes to Avoid:**
   Provide a list of common errors people might make when performing this workout, using informal language. Offer tips on how to prevent these mistakes and maintain proper form.`.trim()

async function generateExerciseDescription(
  exerciseName: string,
  userEmail: string
): Promise<string> {
  try {
    // Initialize Groq client
    getGroqClient({ userEmail })

    const response = await callGroqAi({
      messages: [
        {
          role: "system",
          content: EXERCISE_ENHANCEMENT_PROMPT,
          name: userEmail
        },
        {
          role: "user",
          content: exerciseName,
          name: userEmail
        }
      ],
      temperature: 0.7,
      maxTokens: 2000
    })

    if (!response?.choices?.[0]?.message?.content) {
      throw new Error("Invalid response from Groq")
    }

    return response.choices[0].message.content
  } catch (error) {
    console.error("Error generating exercise description:", error)
    throw error
  }
}

async function checkExistingExercise(exerciseName: string) {
  const exerciseCollectionRef = collection(db, "Fitness Library")
  const q = query(exerciseCollectionRef, where("name", "==", exerciseName))
  const querySnapshot = await getDocs(q)
  return !querySnapshot.empty
}

/**
 * Creates a single exercise entry in the Fitness Library
 * @param exerciseName - Name of the exercise to create
 * @returns Promise<SingleExerciseResult> - Result of the creation attempt
 */
export async function createSingleExercise(
  exerciseName: string
): Promise<SingleExerciseResult> {
  try {
    // Get current authenticated user
    const auth = getAuth()
    const currentUser = auth.currentUser
    
    if (!currentUser?.email) {
      return {
        success: false,
        message: "No authenticated user found",
        error: "Authentication required"
      }
    }

    const userEmail = currentUser.email

    // Check if exercise already exists
    const exists = await checkExistingExercise(exerciseName)
    if (exists) {
      return {
        success: false,
        message: `Exercise "${exerciseName}" already exists in the library`,
        error: "Exercise already exists"
      }
    }

    // Generate enhanced description
    console.log(`Generating description for: ${exerciseName}`)
    const enhancedDescription = await generateExerciseDescription(exerciseName, userEmail)

    // Prepare exercise data with enhanced description
    const exerciseWithMetadata = {
      name: exerciseName,
      description: enhancedDescription,
      focusArea: "", // These fields can be updated later
      level: "beginner",
      muscleGroups: [],
      equipment: "",
      variations: [],
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      uploadedBy: userEmail,
      hasEnhancedDescription: true,
      version: "1.0"
    }

    // Upload to Firestore
    const docRef = await addDoc(collection(db, "Fitness Library"), exerciseWithMetadata)
    console.log(`Successfully created exercise: ${exerciseName}`)

    return {
      success: true,
      libraryId: docRef.id,
      message: `Successfully created exercise "${exerciseName}"`
    }

  } catch (error) {
    console.error(`Error creating exercise ${exerciseName}:`, error)
    return {
      success: false,
      message: `Failed to create exercise "${exerciseName}"`,
      error: error instanceof Error ? error.message : "Unknown error"
    }
  }
}

export async function uploadExerciseLibrary(
  progressCallback: (progress: UploadProgress) => void
) {
  console.log("Starting exercise library upload process")
  
  // Get current authenticated user
  const auth = getAuth()
  const currentUser = auth.currentUser
  
  if (!currentUser?.email) {
    throw new Error("No authenticated user found")
  }

  const userEmail = currentUser.email
  const exerciseCollectionRef = collection(db, "Fitness Library")
  
  let progress: UploadProgress = {
    total: exercise_library.length,
    uploaded: 0,
    skipped: 0,
    failed: 0
  }

  progressCallback(progress) // Initial progress update

  try {
    for (const exercise of exercise_library) {
      try {
        console.log(`Processing exercise: ${exercise.name}`)
        
        // Check if exercise exists
        const exists = await checkExistingExercise(exercise.name)
        
        if (exists) {
          console.log(`Exercise ${exercise.name} already exists, skipping...`)
          progress.skipped++
          progressCallback(progress)
          continue
        }

        // Generate enhanced description for new exercise
        console.log(`Generating description for: ${exercise.name}`)
        const enhancedDescription = await generateExerciseDescription(exercise.name, userEmail)
          
        // Prepare exercise data with enhanced description
        const exerciseWithMetadata = {
          ...exercise,
          description: enhancedDescription,
          createdAt: new Date().toISOString(),
          lastUpdated: new Date().toISOString(),
          uploadedBy: userEmail,
          hasEnhancedDescription: true,
          version: "1.0"
        }

        // Upload to Firestore
        await addDoc(exerciseCollectionRef, exerciseWithMetadata)
        console.log(`Successfully uploaded: ${exercise.name}`)
        progress.uploaded++
        progressCallback(progress)

      } catch (exerciseError) {
        console.error(`Error processing exercise ${exercise.name}:`, exerciseError)
        progress.failed++
        progressCallback(progress)
        // Continue with next exercise
      }
    }

    const summary = {
      success: true,
      message: `Upload complete:
        - ${progress.uploaded} exercises uploaded
        - ${progress.skipped} existing exercises skipped
        - ${progress.failed} exercises failed`,
      stats: { ...progress }
    }

    console.log(summary.message)
    return summary

  } catch (error) {
    console.error("Error in exercise library upload:", error)
    return {
      success: false,
      message: "Failed to upload exercise library",
      error: error instanceof Error ? error.message : "Unknown error",
      stats: { ...progress }
    }
  }
}