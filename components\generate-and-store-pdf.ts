import { jsPDF } from "jspdf"
import { ref, uploadBytes } from "firebase/storage"
import { storage } from "@/components/firebase/config"
import { db } from "@/components/firebase/config"
import { doc, getDoc, setDoc, updateDoc } from "firebase/firestore"

interface GeneratePDFOptions {
  userEmail: string
  profileText: string
}

function formatMarkdownForPDF(text: string): {
  content: string[]
  fontSize: number[]
  isBold: boolean[]
} {
  const lines = text.split("\n")
  const formattedContent: string[] = []
  const fontSizes: number[] = []
  const isBold: boolean[] = []

  lines.forEach((line) => {
    // Handle section headers (##)
    if (line.startsWith("## ")) {
      formattedContent.push(line.replace("## ", "").replace(/\*\*/g, ""))
      fontSizes.push(14)
      isBold.push(true)
    }
    // Handle paragraph headers with bold text
    else if (line.startsWith("**") && line.includes(":**")) {
      // Add an empty line before the paragraph header
      formattedContent.push("")
      fontSizes.push(12)
      isBold.push(false)

      // Add the paragraph header
      formattedContent.push(line.replace(/\*\*/g, ""))
      fontSizes.push(12)
      isBold.push(true)
    }
    // Handle other bold text
    else if (line.startsWith("**") && line.endsWith("**")) {
      formattedContent.push(line.replace(/\*\*/g, ""))
      fontSizes.push(12)
      isBold.push(true)
    }
    // Handle bullet points
    else if (line.startsWith("* ")) {
      formattedContent.push(`  • ${line.substring(2)}`)
      fontSizes.push(12)
      isBold.push(false)
    }
    // Regular text
    else if (line.trim()) {
      formattedContent.push(line)
      fontSizes.push(12)
      isBold.push(false)
    }
  })

  return { content: formattedContent, fontSize: fontSizes, isBold }
}

export async function generateAndDownloadPDF({ userEmail, profileText }: GeneratePDFOptions): Promise<void> {
  const assessmentRef = doc(db, "IF_users", userEmail, "Profile", "assessment")

  try {
    // 1) Check the assessment document to see if HasPdf is true or false
    const assessmentDocSnapshot = await getDoc(assessmentRef)
    const assessmentData = assessmentDocSnapshot.exists() ? assessmentDocSnapshot.data() : null

    // 2) Generate the PDF (always do this, regardless of HasPdf)
    const pdfDoc = new jsPDF()
    const timestamp = new Date().toISOString().split("T")[0]
    const fileName = `ProfileSummary_${timestamp}.pdf`

    // PDF Header
    pdfDoc.setFontSize(16)
    pdfDoc.setFont("", "bold")
    pdfDoc.text("IntelligentFitness.ai Profile Summary", 20, 20)

    // PDF Metadata
    pdfDoc.setFontSize(12)
    pdfDoc.setFont("", "normal")
    pdfDoc.text(`Profile Analysis for: ${userEmail}`, 20, 40)
    pdfDoc.text(`Generation Date: ${timestamp}`, 20, 50)

    // Format markdown content
    const { content, fontSize, isBold } = formatMarkdownForPDF(profileText)

    // Write the content
    let yPos = 70
    const margin = 20
    const pageHeight = pdfDoc.internal.pageSize.height

    content.forEach((line, idx) => {
      // If near the bottom of the page, add a new one
      if (yPos > pageHeight - 20) {
        pdfDoc.addPage()
        yPos = 20
      }

      pdfDoc.setFontSize(fontSize[idx])
      pdfDoc.setFont("", isBold[idx] ? "bold" : "normal")

      // Split long lines to fit page width
      const textLines = pdfDoc.splitTextToSize(line, pdfDoc.internal.pageSize.width - 2 * margin)

      // Write the text and update position
      pdfDoc.text(textLines, margin, yPos)
      yPos += textLines.length * fontSize[idx] * 0.3715 + 4
    })

    // Always allow the user to download the PDF locally
    pdfDoc.save(fileName)

    // 3) Conditionally upload PDF to Storage and update Firestore *only* if HasPdf is false or the doc doesn't exist
    if (!assessmentData || assessmentData.HasPdf === false) {
      console.log("HasPdf is false (or doc missing). Uploading to Storage and updating Firestore...")

      // Upload PDF to Firebase Storage
      const pdfBlob = pdfDoc.output("blob")
      const storageRef = ref(storage, `users/${userEmail}/fitness/${fileName}`)
      await uploadBytes(storageRef, pdfBlob)
      console.log("PDF successfully uploaded to Firebase Storage.")

      // Update Firestore document
      const updateData = {
        HasPdf: true,
        lastPdfGenerated: timestamp,
        pdfPath: `users/${userEmail}/fitness/${fileName}`,
        error: null,
        status: "completed",
      }

      if (!assessmentDocSnapshot.exists()) {
        await setDoc(assessmentRef, updateData)
      } else {
        await updateDoc(assessmentRef, updateData)
      }

      console.log("Firestore updated with new PDF metadata.")
    } else {
      // If HasPdf is already true, do nothing more
      console.log("HasPdf is true; PDF was not uploaded, Firestore not updated.")
    }
  } catch (error) {
    console.error("Error in PDF generation or upload:", error)

    try {
      // Ensure the assessment document exists before updating
      const assessmentDoc = await getDoc(assessmentRef)
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred during PDF generation"

      if (!assessmentDoc.exists()) {
        await setDoc(assessmentRef, {
          HasPdf: true,
          error: errorMessage,
          status: "error",
          lastUpdated: new Date().toISOString(),
        })
      } else {
        await updateDoc(assessmentRef, {
          HasPdf: true,
          error: errorMessage,
          status: "error",
          lastUpdated: new Date().toISOString(),
        })
      }
    } catch (updateError) {
      console.error("Error updating Firestore error status:", updateError)
    }

    throw error
  }
}
