// hooks/useSaveAgentResponses.ts
import { useState } from "react";
import { doc, setDoc } from "firebase/firestore";
import { db } from "@/components/firebase/config";
import { v4 as uuidv4 } from "uuid";

interface WorkoutPlan {
    shortTermPlan: string;
    longTermPlan: string;
    exerciseProgram: string;
    formTechniqueGuidance: string;
}

interface AgentResponses {
    userProfileSummary: string;
    workoutPlan: WorkoutPlan;
    exerciseProgram: string;
    formTechniqueGuidance: string;
    longTermPlanning: string;
    shortTermPlanning: string;
    nutritionAdvice: string;
}

interface UseSaveAgentResponsesResult {
    isSavingResponses: boolean;
    saveAgentResponses: (responseData: AgentResponses, userEmail: string) => Promise<void>;
}

export const useSaveAgentResponses = (): UseSaveAgentResponsesResult => {
    const [isSavingResponses, setIsSavingResponses] = useState(false);

    const saveAgentResponses = async (
        responseData: AgentResponses,
        userEmail: string
    ): Promise<void> => {
        setIsSavingResponses(true);
        try {
            if (!userEmail) {
                throw new Error("No email associated with user");
            }
            const newDocumentId = uuidv4();
            // Save only the required fields to IF_users collection
            const workoutPlanRef = doc(db, `IF_users/${userEmail}/Profile/workoutplan`);
            const workoutPlanSummary = {
                shortTermPlan: responseData.workoutPlan.shortTermPlan,
                longTermPlan: responseData.workoutPlan.longTermPlan,
                exerciseProgram: responseData.workoutPlan.exerciseProgram,
                formTechniqueGuidance: responseData.workoutPlan.formTechniqueGuidance,
                WorkoutReference: newDocumentId
                //nutritionAdvice: responseData.nutritionAdvice
            };
            await setDoc(workoutPlanRef, workoutPlanSummary, { merge: true });
            
            console.log("Workout plan saved to IF_users collection");

            //Save detailed response to users collection

            const filesRef = doc(db, `users/${userEmail}/files/${newDocumentId}`);
            await setDoc(
                filesRef,
                {
                    category: "fitnessintelligentai",
                    userProfileSummary: responseData.userProfileSummary,
                    exerciseProgram: responseData.exerciseProgram,
                    formTechniqueGuidance: responseData.formTechniqueGuidance,
                    longTermPlanning: responseData.longTermPlanning,
                    shortTermPlanning: responseData.shortTermPlanning,
                    nutritionAdvice: responseData.nutritionAdvice,
                    createdAt: new Date().toISOString(),
                    name: "Workout planning",
                    namespace: newDocumentId,
                },
                { merge: true }
            );
            console.log("Workout Ref :  ", newDocumentId);

        } catch (error: any) {
            console.error("Error saving agent responses:", error);
            throw new Error(`Failed to save agent responses. ${error?.message}`);
        } finally {
            setIsSavingResponses(false);
        }
    };

    return { isSavingResponses, saveAgentResponses };
};