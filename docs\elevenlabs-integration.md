# ElevenLabs Voice Assistant Integration

This document provides instructions on how to set up and use the ElevenLabs Voice Assistant integration in the IntelligentFitness application.

## Prerequisites

1. An ElevenLabs account with API access
2. An ElevenLabs Agent ID for the voice assistant

## Setup

1. Install the ElevenLabs React SDK:

```bash
npm install @11labs/react
```

2. Ensure your `.env.local` file contains the necessary environment variables:

```
# ElevenLabs API Configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
NEXT_PUBLIC_ELEVENLABS_AGENT_ID=your_elevenlabs_agent_id_here
```

3. Replace `your_elevenlabs_api_key_here` with your actual ElevenLabs API key
4. Replace `your_elevenlabs_agent_id_here` with your ElevenLabs Agent ID

## How It Works

The Voice Assistant integration uses the ElevenLabs API to provide a conversational voice interface for users. The integration consists of:

1. An ElevenLabsVoiceAssistant component that handles the UI and user interactions
2. An API route for secure communication with the ElevenLabs API
3. Integration with the main application layout

## Implementation

The current implementation in `components/ElevenLabsVoiceAssistant.tsx` is a placeholder that demonstrates the UI and interaction flow. To fully implement the ElevenLabs voice assistant, you need to:

1. Replace the mock implementation with the actual ElevenLabs React SDK
2. Update the conversation handling functions to use the actual ElevenLabs API
3. Implement proper error handling and user feedback

## Usage

The Voice Assistant can be accessed in two ways:

1. By clicking the "Speak" button in the main hero section
2. By clicking the "Speak to Us" button in the chat interface

When activated, the Voice Assistant will:

1. Request microphone permissions from the user
2. Connect to the ElevenLabs API
3. Allow the user to have a voice conversation with the AI assistant

## Customization

You can customize the Voice Assistant by:

1. Modifying the `ElevenLabsVoiceAssistant.tsx` component for UI changes
2. Updating the ElevenLabs Agent configuration in your ElevenLabs dashboard
3. Adjusting the API route for different authentication or session management

## Troubleshooting

If you encounter issues with the Voice Assistant:

1. Check that your ElevenLabs API key and Agent ID are correctly set in the `.env.local` file
2. Ensure that your ElevenLabs account has sufficient credits
3. Check the browser console for any error messages
4. Verify that the user has granted microphone permissions

## Resources

- [ElevenLabs Documentation](https://elevenlabs.io/docs)
- [ElevenLabs React SDK Documentation](https://elevenlabs.io/docs/conversational-ai/libraries/react)
