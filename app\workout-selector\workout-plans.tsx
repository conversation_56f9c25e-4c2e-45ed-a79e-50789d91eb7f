"use client";

import { Act<PERSON>, Dumbbell, Target, Timer, Loader } from "lucide-react";
import Image from "next/image";
import { JSX, useState } from "react";
import { db } from "@/components/firebase/config";
import { generateAndStoreSchedule } from "@/lib/generate_shortTerm_Schedule";
import { generateAndStoreLongTermSchedule } from "@/lib/generate_longTerm_Schedule";
import { generateAndStoreKickstarterSchedule } from "@/lib/generate_KickerStarter_Schedule";
import { useCoachInstructions } from "@/lib/useCoachInstructions";
import { useRouter } from "next/navigation";
import { auth } from "@/components/firebase/config";
import { Firestore, doc, getDoc } from "firebase/firestore";

// Base type definitions
const PLAN_TYPES = {
  KICK_STARTER: "Kick Starter",
  PERSONAL_TRAINER: "Personal Trainer",
  SEVEN_DAY: "7-Day Workout Plan",
  FITNESS_JOURNEY: "Fitness Journey Plan",
} as const;

type PlanType = (typeof PLAN_TYPES)[keyof typeof PLAN_TYPES];

// Notification system types
interface Notification {
  type: "success" | "error";
  message: string;
  id: number;
}

// Plan generation types
type GeneratablePlanType = PlanType;

interface PlanGenerationStatus {
  [key: string]: boolean;
}

// Generator function type with proper Firestore typing
type PlanGenerator = (
  db: Firestore,
  email: string,
  docId: string
) => Promise<void>;

// Type-safe generator mapping
const PLAN_GENERATORS: Record<GeneratablePlanType, PlanGenerator> = {
  [PLAN_TYPES.KICK_STARTER]: generateAndStoreKickstarterSchedule,
  [PLAN_TYPES.SEVEN_DAY]: generateAndStoreSchedule,
  [PLAN_TYPES.FITNESS_JOURNEY]: generateAndStoreLongTermSchedule,
  [PLAN_TYPES.PERSONAL_TRAINER]: async (db: Firestore, email: string) => {
    // Fetch the workout plan document from Firestore
    const workoutPlanRef = doc(db, "IF_users", email, "Profile", "workoutplan");
    const workoutPlanSnap = await getDoc(workoutPlanRef);
    if (!workoutPlanSnap.exists()) {
      throw new Error("Workout plan document not found. Please update your profile.");
    }
    const workoutPlanData = workoutPlanSnap.data();
    if (!workoutPlanData.coachInstructions) {
      throw new Error("No coach instructions found in your workout plan. Please update your profile.");
    }
    // Convert the coachInstructions to a JSON string if necessary.
    const coachInstructionsStr =
      typeof workoutPlanData.coachInstructions === "string"
        ? workoutPlanData.coachInstructions
        : JSON.stringify(workoutPlanData.coachInstructions);

    // Pass the workoutPlanRef so that useCoachInstructions can use it to retrieve a workoutReference.
    await useCoachInstructions(email, coachInstructionsStr, workoutPlanRef);
  },
};

// Plan interface
interface WorkoutPlan {
  title: PlanType;
  icon: JSX.Element;
  iconBg: string;
  description: string;
  features: string[];
  buttonColor: string;
  dotColor: string;
  popular?: boolean;
  detailedDescription: string;
}

// Success messages type
type SuccessMessages = {
  [K in GeneratablePlanType]: string;
};

export default function WorkoutPlans() {
  const [expandedImage, setExpandedImage] = useState<number | null>(null);
  const [generatingPlans, setGeneratingPlans] = useState<PlanGenerationStatus>({});
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const router = useRouter();

  const showNotification = (type: "success" | "error", message: string): void => {
    const id = Date.now();
    const newNotification: Notification = { type, message, id };
    setNotifications((prev) => [...prev, newNotification]);
    setTimeout(() => {
      setNotifications((prev) => prev.filter((notification) => notification.id !== id));
    }, 3000);
  };

  const isPlanType = (title: string): title is PlanType => {
    return Object.values(PLAN_TYPES).includes(title as PlanType);
  };

  const handleStartPlan = async (planTitle: string): Promise<void> => {
    if (!isPlanType(planTitle)) {
      showNotification("error", "Invalid plan selection");
      return;
    }

    try {
      setGeneratingPlans((prev) => ({ ...prev, [planTitle]: true }));

      const user = auth.currentUser;
      if (!user?.email) {
        showNotification("error", "Please sign in to start a workout plan");
        router.push("/login");
        return;
      }

      const tempDocId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const generator = PLAN_GENERATORS[planTitle];
      await generator(db, user.email, tempDocId);

      const successMessages: SuccessMessages = {
        [PLAN_TYPES.KICK_STARTER]: "Your Kick Starter plan has been generated!",
        [PLAN_TYPES.SEVEN_DAY]: "Your 7-Day Workout plan has been generated!",
        [PLAN_TYPES.FITNESS_JOURNEY]: "Your long-term transformation plan has been generated!",
        [PLAN_TYPES.PERSONAL_TRAINER]: "Your Personal Trainer plan has been generated!",
      };

      showNotification("success", successMessages[planTitle]);
      router.push("/dashboard");
    } catch (error) {
      console.error("Error generating workout plan:", error);
      const errorMessage =
        error instanceof Error
          ? `Failed to generate plan: ${error.message}`
          : "Failed to generate workout plan. Please try again.";
      showNotification("error", errorMessage);
    } finally {
      setGeneratingPlans((prev) => ({ ...prev, [planTitle]: false }));
    }
  };

  const plans: WorkoutPlan[] = [
    {
      title: PLAN_TYPES.KICK_STARTER,
      icon: <Activity className="w-6 h-6" />,
      iconBg: "bg-orange-500",
      description: "Kickstart Your Fitness: Tailored 7-Day Workout Plan",
      features: ["Quick-start routine", "Balanced exercises", "Easy implementation", "Immediate guidance"],
      buttonColor: "bg-orange-600 hover:bg-orange-700",
      dotColor: "text-orange-500",
      detailedDescription:
        "Start your fitness journey with our carefully crafted 7-day program. Perfect for beginners and those looking to get back into exercise.",
    },
    {
      title: PLAN_TYPES.PERSONAL_TRAINER,
      icon: <Dumbbell className="w-6 h-6" />,
      iconBg: "bg-blue-500",
      description: "Get Expert Guidance: View Exercise Instructions, Common Mistakes, and Modifications",
      features: [
        "Detailed exercise instructions",
        "Common mistake prevention",
        "Exercise modifications",
        "Day-by-day breakdown",
      ],
      buttonColor: "bg-blue-600 hover:bg-blue-700",
      dotColor: "text-blue-500",
      popular: true,
      detailedDescription:
        "Get personalized guidance with detailed instructions for each exercise. Our expert trainers provide insights on common mistakes.",
    },
    {
      title: PLAN_TYPES.SEVEN_DAY,
      icon: <Timer className="w-6 h-6" />,
      iconBg: "bg-green-500",
      description: "Unlock Your 7-Day Workout Plan: Warm-Up, Main Workout, and Recovery",
      features: ["Clear daily sections", "Balanced workout mix", "Progressive overload guide", "Self-guided format"],
      buttonColor: "bg-green-600 hover:bg-green-700",
      dotColor: "text-green-500",
      detailedDescription:
        "A comprehensive week-long program featuring structured warm-ups, targeted workouts, and essential recovery periods.",
    },
    {
      title: PLAN_TYPES.FITNESS_JOURNEY,
      icon: <Target className="w-6 h-6" />,
      iconBg: "bg-purple-500",
      description: "Transform Your Body: Long-Term Fitness goals with Phased Approach",
      features: ["Phased progression", "Clear milestone targets", "Comprehensive approach", "Mental wellness focus"],
      buttonColor: "bg-purple-600 hover:bg-purple-700",
      dotColor: "text-purple-500",
      detailedDescription:
        "A long-term transformation program designed to help you achieve sustainable results.",
    },
  ];

  return (
    <div className="py-2 px-4 relative">
      <div className="fixed top-4 right-4 z-50 space-y-2">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={`px-4 py-2 rounded-lg text-white ${
              notification.type === "success" ? "bg-green-500" : "bg-red-500"
            } shadow-lg transform transition-all duration-300 animate-fade-in`}
            role="alert"
            aria-live="polite"
          >
            {notification.message}
          </div>
        ))}
      </div>

      <div className="max-w-7xl mx-auto animate-fade-in-slide">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 items-stretch">
          {plans.map((plan, index) => (
            <div
              key={index}
              className="bg-gray-800 bg-opacity-80 rounded-xl p-6 hover:bg-opacity-100 transition-colors border border-gray-700 flex flex-col min-h-[700px] relative"
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-600 text-white px-3 py-1 rounded-full text-sm">
                    Most Popular
                  </span>
                </div>
              )}
              <div className="flex-1">
                <div className={`flex items-center justify-center w-12 h-12 ${plan.iconBg} rounded-lg mb-4`}>
                  {plan.icon}
                </div>
                <h2 className="text-xl font-bold mb-4 text-white">{plan.title}</h2>
                <p className="text-gray-300 mb-6">{plan.description}</p>

                <div
                  className="relative w-full h-32 mb-6 group"
                  onMouseEnter={() => setExpandedImage(index)}
                  onMouseLeave={() => setExpandedImage(null)}
                >
                  <Image
                    src="/ThumbNails-1.png?height=200&width=250"
                    alt={`${plan.title} preview`}
                    fill
                    className="object-cover object-center rounded-lg"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                    priority={index === 0}
                  />
                  {expandedImage === index && (
                    <div className="absolute top-0 left-0 w-full h-32 bg-black bg-opacity-75 rounded-lg overflow-hidden z-50 transition-all duration-300 ease-in-out">
                      <Image
                        src="/ThumbNails-1.png?height=200&width=250"
                        alt={`${plan.title} expanded preview`}
                        fill
                        className="object-cover object-center transition-transform duration-300 ease-in-out"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 p-4 transition-all duration-300 ease-in-out">
                        <p className="text-white text-sm">{plan.detailedDescription}</p>
                      </div>
                    </div>
                  )}
                </div>

                <ul className="space-y-3 text-sm text-gray-400">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <span className={`${plan.dotColor} mr-2`}>•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              <button
                onClick={() => handleStartPlan(plan.title)}
                disabled={!!generatingPlans[plan.title]}
                className={`w-full ${plan.buttonColor} text-white rounded-lg py-3 px-4 transition-colors mt-6 flex items-center justify-center ${
                  generatingPlans[plan.title] ? "opacity-50 cursor-not-allowed" : ""
                }`}
                aria-busy={!!generatingPlans[plan.title]}
              >
                {generatingPlans[plan.title] ? (
                  <>
                    <span>Generating Plan...</span>
                    <Loader className="ml-2 w-4 h-4 animate-spin" />
                  </>
                ) : (
                  "Start Now"
                )}
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}