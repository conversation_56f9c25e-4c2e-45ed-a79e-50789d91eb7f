// schemas/workout.ts
import { z } from "zod";

// Base Exercise Schema - for incoming exercises from LLM
export const BaseExerciseSchema = z.object({
  name: z.string().min(1, "Exercise name is required"),
  specs: z.string().min(1, "Exercise specifications are required"),
  instructions: z.string().optional(),
  mistakes: z.string().optional(),
  modifications: z.string().optional(),
});

// Library Exercise Schema - for exercises in the fitness library
export const LibraryExerciseSchema = z.object({
    name: z.string(),
    focusArea: z.string().optional(),
    description: z.string().optional(),
    level: z.string().optional(),
    muscleGroups: z.array(z.string()).optional().default([]),
    equipment: z.string().optional(),
    variations: z.array(z.string()).optional().default([]),
}).passthrough();

// Processed Exercise Schema - after library matching
export const ProcessedExerciseSchema = BaseExerciseSchema.extend({
  libraryId: z.string().nullable(),
  libraryMatch: LibraryExerciseSchema.nullable(),
  matchConfidence: z.number().min(0).max(1),
  matchExplanation: z.string().nullable(),
});

// Short Term Exercise Schema
export const ShortTermExerciseSchema = z.object({
  order: z.number(),
  name: z.string(),
  duration: z.number(),
  details: z.string()
});

// Short Term Day Schema
export const ShortTermDaySchema = z.object({
  day: z.number(),
  name: z.string(),
  location: z.string(),
  duration: z.number(),
  exercises: z.array(ShortTermExerciseSchema)
});

// Short Term Planning Schema
export const ShortTermPlanningSchema = z.object({
  title: z.string(),
  notes: z.array(z.string()),
  days: z.array(ShortTermDaySchema)
});

// Workout Day Schema with optional exercises for rest days
export const WorkoutDaySchema = z.object({
  day: z.string().regex(/^Day \d+:.*/, "Day must be in format 'Day X: Description'"),
  exercises: z.array(BaseExerciseSchema).optional(),
});

// Processed Workout Day Schema
export const ProcessedWorkoutDaySchema = z.object({
  day: z.string(),
  exercises: z.array(ProcessedExerciseSchema),
});

// Complete Workout Plan Schema
export const WorkoutPlanSchema = z.object({
  workoutDays: z.array(WorkoutDaySchema),
});

// Complete Processed Workout Plan Schema
export const ProcessedWorkoutPlanSchema = z.object({
  workoutDays: z.array(ProcessedWorkoutDaySchema),
});

// Response from LLM Schema
export const LLMResponseSchema = z.object({
  userProfileSummary: z.string(),
  workoutPlan: z.object({
    shortTermPlan: z.string(),
    longTermPlan: z.string(),
    exerciseProgram: z.string(),
    formTechniqueGuidance: z.string(),
  }),
  nutritionAdvice: z.string(),
  coachInstructions: z.string(),
});

// Export TypeScript types
export type BaseExercise = z.infer<typeof BaseExerciseSchema>;
export type LibraryExercise = z.infer<typeof LibraryExerciseSchema>;
export type ProcessedExercise = z.infer<typeof ProcessedExerciseSchema>;
export type ShortTermExercise = z.infer<typeof ShortTermExerciseSchema>;
export type ShortTermDay = z.infer<typeof ShortTermDaySchema>;
export type ShortTermPlanning = z.infer<typeof ShortTermPlanningSchema>;
export type WorkoutDay = z.infer<typeof WorkoutDaySchema>;
export type ProcessedWorkoutDay = z.infer<typeof ProcessedWorkoutDaySchema>;
export type WorkoutPlan = z.infer<typeof WorkoutPlanSchema>;
export type ProcessedWorkoutPlan = z.infer<typeof ProcessedWorkoutPlanSchema>;
export type LLMResponse = z.infer<typeof LLMResponseSchema>;

// Validation helper functions
export const validateWorkoutPlan = (data: unknown): WorkoutPlan => {
  return WorkoutPlanSchema.parse(data);
};

export const validateProcessedWorkoutPlan = (data: unknown): ProcessedWorkoutPlan => {
  return ProcessedWorkoutPlanSchema.parse(data);
};

export const validateLLMResponse = (data: unknown): LLMResponse => {
  return LLMResponseSchema.parse(data);
};

export const validateShortTermPlanning = (data: unknown): ShortTermPlanning => {
  return ShortTermPlanningSchema.parse(data);
};

// Safety wrapper for exercise processing
export const safeParseLLMResponse = (data: unknown) => {
  const result = LLMResponseSchema.safeParse(data);
  if (!result.success) {
    return {
      success: false,
      error: result.error.format(),
      data: null,
    };
  }
  return {
    success: true,
    data: result.data,
    error: null,
  };
};

// Safety wrapper for short term planning
export const safeParseShortTermPlanning = (data: unknown) => {
  const result = ShortTermPlanningSchema.safeParse(data);
  if (!result.success) {
    return {
      success: false,
      error: result.error.format(),
      data: null,
    };
  }
  return {
    success: true,
    data: result.data,
    error: null,
  };
};