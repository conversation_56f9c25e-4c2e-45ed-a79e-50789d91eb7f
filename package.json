{"name": "intelligent-fitness", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@11labs/react": "^0.1.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@google/genai": "^0.7.0", "@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@langchain/community": "^0.3.25", "@langchain/core": "^0.2.36", "@langchain/groq": "^0.0.2", "@langchain/openai": "^0.0.16", "@pinecone-database/pinecone": "^4.1.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "elevenlabs": "^1.56.0", "firebase": "^11.2.0", "firebase-admin": "^12.0.0", "firestore-store": "^2.0.2", "framer-motion": "^12.4.7", "groq-sdk": "^0.12.0", "jspdf": "^2.5.2", "langchain": "^0.3.11", "lucide-react": "^0.469.0", "next": "15.1.4", "next-auth": "^4.24.11", "openai": "^4.85.2", "pdf-parse": "^1.1.1", "pinecone": "^0.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-firebase-hooks": "^5.1.1", "react-markdown": "^9.1.0", "react-pdf": "^9.2.1", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@emotion/babel-plugin": "^11.13.5", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}