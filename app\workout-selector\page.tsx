"use client"

import { useEffect, useState } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { auth } from "@/components/firebase/config"
import { onAuthStateChanged } from "firebase/auth"
import WorkoutPlans from "./workout-plans"
import "../styles/animations.css"

export default function WorkoutPlanner() {

  const [user, setUser] = useState(auth.currentUser)
  const router = useRouter()

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        setUser(user)
      } else {
        router.push("/login")
      }
    })

    return () => unsubscribe()
  }, [router])

  // useEffect(() => {
  //   async function fetchSchedule() {
  //     if (user) {
  //       try {
  //         const data = await getSchedule(user.email!)
  //         setSchedule(data)
  //       } catch (error) {
  //         console.error("Error fetching schedule:", error)
  //       } finally {
  //         setLoading(false)
  //       }
  //     }
  //   }

  //   if (user) {
  //     fetchSchedule()
  //   }
  // }, [user])



  if (!user) {
    return null
  }

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-grow">
        {/* Hero Section and Workout Plans with background image */}
        <div className="relative">
        <Image
          src="/bg-website-c.png?height=1080&width=1920"
          alt="Fitness background"
          width={1920}
          height={1080}
          className="absolute inset-0 w-full h-full object-cover"
        />

          <div className="relative z-10 bg-black bg-opacity-50">
            {/* Hero Section */}
            <section className="text-white py-12">
              <div className="container mx-auto px-4">
                <div className="max-w-4xl mx-auto text-center">
                  <h2 className="text-4xl font-bold animate-fade-in-slide">Your Intelligent Fitness Journey</h2>
                  <h3 className="text-blue-600 animate-fade-in-slide text-xl mt-2">Select From Tailored Workout Plans</h3>
                </div>
              </div>
            </section>

            {/* Workout Plans Section */}
            <WorkoutPlans />
          </div>
        </div>


      </main>
      <footer className=" text-white  text-center p-4 bg-black bg-opacity-60">
        <div className="container mx-auto">© 2025 Workout Planner. All rights reserved.</div>
      </footer>
    </div>
  )
}
