// components/ProfileSummaryViewModal.tsx
"use client";
import { X } from "lucide-react";
import { motion } from "framer-motion";
import ReactMarkdown from "react-markdown";

interface ProfileSummaryViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  originalSummary: string;
  transformedSummary: string;
}

// --------------------------------------------------
//   Markdown Formatting Function (NOW OUTSIDE THE COMPONENT)
// --------------------------------------------------
/**
 * Splits the summary into sections and applies custom headers
 * or bullet lists where needed.  "Primary Focus" and
 * "Training Style" lines will appear in bold.
 */
function formatSummaryToMarkdown(summary: string): string {
  if (!summary) return "";

  // Split the summary into sections, separated by double newlines
  const sections = summary.split("\n\n");
  let formattedSummary = "";

  sections.forEach((section) => {
    const lowerSection = section.toLowerCase();

    if (lowerSection.includes("anthropometric assessment:")) {
      formattedSummary += "## Anthropometric Assessment\n\n";
      // Convert the body of that section into bullet points
      const measurements = section
        .replace(/anthropometric assessment:/i, "")
        .trim()
        .split("\n");
      measurements.forEach((m) => {
        if (m.trim()) {
          formattedSummary += `* ${m.trim()}\n`;
        }
      });
      formattedSummary += "\n";
    } else if (lowerSection.includes("body type classification:")) {
      formattedSummary += "## Body Type Classification\n\n";
      // Convert the body of that section into bullet points
      const classifications = section
        .replace(/body type classification:/i, "")
        .trim()
        .split("\n");
      classifications.forEach((c) => {
        if (c.trim()) {
          formattedSummary += `* ${c.trim()}\n`;
        }
      });
      formattedSummary += "\n";
    } else if (lowerSection.includes("long-term outlook:")) {
      formattedSummary += "## **Long-term Outlook**\n\n";
      formattedSummary += section.replace(/long-term outlook:/i, "").trim() + "\n\n";
    } else if (
      !lowerSection.includes("summary:") &&
      !lowerSection.includes("sample workout routine:") &&
      !lowerSection.match(/monday|tuesday|wednesday|thursday|friday|saturday|sunday/i) &&
      !lowerSection.includes("remember to adjust") &&
      section.trim()
    ) {
      // For any remaining text that doesn't match the above exclusions,
      // just add it as normal paragraphs
      formattedSummary += section.trim() + "\n\n";
    }
  });

  return formattedSummary.trim();
}

export function ProfileSummaryViewModal({
  isOpen,
  onClose,
  originalSummary,
  transformedSummary,
}: ProfileSummaryViewModalProps) {
  if (!isOpen) return null;

  const formattedOriginalSummary = formatSummaryToMarkdown(originalSummary);

  return (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center p-4 z-50 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="bg-black/20 backdrop-blur-sm rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-purple-500/20"
        role="dialog"
        aria-modal="true"
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold text-white">Your Fitness Profile Summaries</h2>
            <button
              onClick={onClose}
              className="text-gray-300 hover:text-gray-100 transition-colors rounded-full hover:bg-gray-700 p-2"
              aria-label="Close dialog"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Original Summary</h3>
              <div className="prose prose-sm md:prose-base max-w-none">
                <ReactMarkdown
                  className="text-gray-200"
                  components={{
                    // Apply text-amber-500 to all bold (strong) items
                    strong: (props) => <strong className="text-amber-500" {...props} />,
                  }}
                >
                  {formattedOriginalSummary || "No summary available"}
                </ReactMarkdown>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">Transformed Summary (for Audio)</h3>
              <p className="text-gray-200">{transformedSummary || "No transformed summary available"}</p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}