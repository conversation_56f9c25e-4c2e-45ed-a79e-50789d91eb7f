// app/api/realtime-session/route.ts
import { NextRequest, NextResponse } from "next/server";

/**
 * API endpoint handler for generating an ephemeral key for OpenAI Realtime API sessions
 *
 * This endpoint securely manages the creation of a session with OpenAI's Realtime API,
 * providing the client with the necessary credentials (ephemeral key) to establish
 * a direct WebRTC connection for voice interactions.
 */
export async function GET(_request: NextRequest) {
  try {
    // Retrieve the API key from environment variables with a fallback
    const apiKey =
      process.env.OPENAI_API_REHEARSAL_KEY ||
      "********************************************************************************************************************************************************************";

    // Validate that the API key exists and is not empty
    if (!apiKey || apiKey.trim() === "") {
      console.error("OpenAI API key is missing or empty in environment variables");
      return NextResponse.json(
        {
          error: "OpenAI API key is not configured",
          details: "Please set a valid OPENAI_API_REHEARSAL_KEY in your environment variables",
        },
        { status: 500 }
      );
    }

    // Request a new session from OpenAI Realtime API
    console.log("Requesting OpenAI Realtime session");
    const response = await fetch("https://api.openai.com/v1/realtime/sessions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: "gpt-4o-realtime-preview-2024-12-17",
        voice: "alloy", // Using alloy for consistency with our TTS voice
      }),
    });

    // Read the response body as text
    const responseBody = await response.text();

    // Handle unsuccessful responses
    if (!response.ok) {
      console.error(
        "OpenAI Realtime API request failed:",
        response.status,
        response.statusText
      );
      console.error("Response body:", responseBody);

      let errorMessage = "Failed to fetch ephemeral key";

      // Attempt to parse error details if the response is JSON
      try {
        const errorData = JSON.parse(responseBody);
        if (errorData.error) {
          errorMessage = errorData.error.message || errorData.error;
        }
      } catch (e) {
        // Log the parsing error and append the raw response body to the error message
        console.error("Error parsing OpenAI error response:", e);
        errorMessage = `${errorMessage}: ${responseBody}`;
      }

      return NextResponse.json(
        {
          error: errorMessage,
          status: response.status,
          statusText: response.statusText,
        },
        { status: response.status }
      );
    }

    // Parse the successful response
    try {
      const data = JSON.parse(responseBody);
      console.log("Successfully obtained ephemeral key");

      // Return the response in a consistent format
      return NextResponse.json({
        success: true,
        ephemeral_key: data.client_secret?.value || data,
        original_response: data, // Included for debugging purposes
        timestamp: new Date().toISOString(),
      });
    } catch (parseError) {
      console.error("Error parsing OpenAI response:", parseError);
      return NextResponse.json(
        {
          error: "Invalid response format from OpenAI API",
          details: responseBody.substring(0, 200), // Provide a preview of the response
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error generating ephemeral key:", error);
    return NextResponse.json(
      {
        error: "Failed to generate ephemeral key",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}