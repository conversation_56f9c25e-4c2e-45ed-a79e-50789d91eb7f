"use client"

import type React from "react"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Activity, Calendar, Clock, Dumbbell, Target, X, ChevronRight } from "lucide-react"
import type { LongTermSchedule } from "@/types/schedule"

// Define ExpectedLongTermSchedule as an extension of LongTermSchedule.
export type ExpectedLongTermSchedule = LongTermSchedule & {
  title: string
  lastUpdated: string
  // Optionally define types for notes and strategies if available
}

interface FitnessJourneyModalProps {
  isOpen: boolean
  onClose: () => void
  data: ExpectedLongTermSchedule | null
  currentPhase: number
  setCurrentPhase: React.Dispatch<React.SetStateAction<number>>
  phaseProgress: number
}

// Optionally, define a type for a day's data instead of "any"
type DayData = {
  name: string
  duration: number
  focus: string
  intensity: string
  exercises: {
    exerciseName: string
    specs: { duration: number }
    details: string
  }[]
}

export function FitnessJourneyModal({
  isOpen,
  onClose,
  data,
  phaseProgress,
}: FitnessJourneyModalProps) {
  const [activeTab, setActiveTab] = useState<"overview" | "schedule" | "strategies">("overview")

  // For phases and days, initialize with safe defaults
  const phaseKeys: (keyof LongTermSchedule["phases"])[] = data
    ? (Object.keys(data.phases) as (keyof LongTermSchedule["phases"])[])
    : []
  phaseKeys.sort((a, b) => {
    const aNum = Number.parseInt(a.split(" ")[1])
    const bNum = Number.parseInt(b.split(" ")[1])
    return aNum - bNum
  })

  const initialPhaseKey = phaseKeys.length > 0 ? phaseKeys[0] : ("Phase 1" as keyof LongTermSchedule["phases"])
  const [activePhaseKey, setActivePhaseKey] = useState<keyof LongTermSchedule["phases"]>(initialPhaseKey)

  // Compute sortedDays using a safe fallback when data is null
  const sortedDays: [string, DayData][] =
    data && activePhaseKey
      ? (Object.entries(data.phases[activePhaseKey]) as unknown as [string, DayData][]).sort((a, b) => {
          const aNum = Number.parseInt(a[0].split(" ")[1])
          const bNum = Number.parseInt(b[0].split(" ")[1])
          return aNum - bNum
        })
      : []

  const initialDayKey = sortedDays.length > 0 ? sortedDays[0][0] : ""
  const [activeDayKey, setActiveDayKey] = useState<string>(initialDayKey)

  // Handler to change active phase and reset active day.
  const handlePhaseChange = (phaseKey: keyof LongTermSchedule["phases"]) => {
    setActivePhaseKey(phaseKey)
    if (data) {
      const sorted = Object.entries(data.phases[phaseKey]).sort((a, b) => {
        const aNum = Number.parseInt(a[0].split(" ")[1])
        const bNum = Number.parseInt(b[0].split(" ")[1])
        return aNum - bNum
      })
      setActiveDayKey(sorted.length > 0 ? sorted[0][0] : "")
    }
  }

  // Get the active day's schedule.
  const activeDaySchedule =
    data && activeDayKey && data.phases[activePhaseKey]
      ? (data.phases[activePhaseKey][activeDayKey] as unknown as DayData)
      : null

  if (!isOpen || !data) {
    return null
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative w-full max-w-6xl h-[85vh] bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 rounded-2xl shadow-2xl overflow-hidden border border-purple-500/20"
      >
        <div className="absolute inset-0 bg-gradient-to-t from-purple-500/5 to-transparent pointer-events-none" />

        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-full transition-all duration-200 backdrop-blur-sm z-50"
        >
          <X className="w-6 h-6" />
        </button>

        <div className="flex h-full">
          <div className="w-64 bg-black/20 backdrop-blur-sm border-r border-white/10 p-4 flex flex-col">
            <h2 className="text-xl font-bold text-green-500 mb-6 px-2">Fitness Journey</h2>
            <div className="space-y-2 text-sm overflow-y-auto max-h-[calc(100vh-10rem)] pr-2 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
              {phaseKeys.map((phaseKey) => (
                <div key={phaseKey}>
                  <button
                    onClick={() => handlePhaseChange(phaseKey)}
                    className={`w-full px-4 py-3 rounded-lg flex items-center justify-between text-left transition-all duration-200 group ${
                      activePhaseKey === phaseKey
                        ? "bg-gradient-to-r from-purple-600 to-purple-500 text-white shadow-lg shadow-purple-500/20"
                        : "text-gray-400 hover:bg-white/5 hover:text-white hover:shadow-md hover:shadow-purple-500/10"
                    }`}
                  >
                    <span className="font-medium">{phaseKey}</span>
                    <ChevronRight
                      className={`w-5 h-5 transition-transform duration-200 ${activePhaseKey === phaseKey ? "rotate-90" : "group-hover:translate-x-1"}`}
                    />
                  </button>
                  {activePhaseKey === phaseKey && (
                    <div className="ml-4 mt-2 space-y-1">
                      {sortedDays.map(([dayKey, dayData]) => (
                        <button
                          key={dayKey}
                          onClick={() => setActiveDayKey(dayKey)}
                          className={`w-full px-4 py-2 rounded-md text-sm transition-colors ${
                            activeDayKey === dayKey
                              ? "bg-blue-100 text-blue-800 font-semibold"
                              : "text-gray-400 hover:bg-white/5 hover:text-white"
                          }`}
                        >
                          {dayKey}: {dayData.name}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="flex-1 overflow-hidden backdrop-blur-sm">
            <div className="h-full flex flex-col">
              <div className="border-b border-white/10 px-6 py-4 bg-black/20">
                <div className="flex space-x-6">
                  {["overview", "schedule", "strategies"].map((tab) => (
                    <button
                      key={tab}
                      onClick={() => setActiveTab(tab as "overview" | "schedule" | "strategies")}
                      className={`text-sm font-medium px-2 py-1 border-b-2 transition-all duration-200 ${
                        activeTab === tab
                          ? "border-purple-500 text-purple-400"
                          : "border-transparent text-gray-400 hover:text-white hover:border-white/20"
                      }`}
                    >
                      {tab.charAt(0).toUpperCase() + tab.slice(1)}
                    </button>
                  ))}
                </div>
              </div>

              <div className="flex-1 overflow-y-auto p-6 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={`${activePhaseKey}-${activeDayKey}-${activeTab}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-6"
                  >
                    {activeTab === "overview" && (
                      <div className="space-y-6">
                        <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10">
                          <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                            <Clock className="w-5 h-5 mr-2 text-blue-500" />
                            Last Updated
                          </h3>
                          <p className="text-sm text-gray-400">{new Date(data.lastUpdated).toLocaleString()}</p>
                        </div>
                        <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10">
                          <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                            <Dumbbell className="w-5 h-5 mr-2 text-amber-500" />
                            Phase Progress
                          </h3>
                          <div className="mt-2 h-2 bg-gray-700 rounded">
                            <div className="h-2 bg-blue-500 rounded" style={{ width: `${phaseProgress}%` }}></div>
                          </div>
                          <p className="text-sm text-gray-400 mt-1">{phaseProgress}% completed</p>
                        </div>
                      </div>
                    )}

                    {activeTab === "schedule" && activeDaySchedule && (
                      <div className="space-y-6">
                        <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10">
                          <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                            <Calendar className="w-5 h-5 mr-2 text-blue-500" />
                            {activeDayKey}: {activeDaySchedule.name}
                          </h3>
                          <div className="flex items-center text-sm text-gray-400">
                            <Clock className="mr-2 h-4 w-4" />
                            <span>Duration: {activeDaySchedule.duration} min</span>
                          </div>
                          <p className="text-sm text-gray-400 mt-2">
                            <strong>Focus:</strong> {activeDaySchedule.focus}
                          </p>
                          <p className="text-sm text-gray-400">
                            <strong>Intensity:</strong> {activeDaySchedule.intensity}
                          </p>
                        </div>
                        {activeDaySchedule.exercises.length > 0 ? (
                          activeDaySchedule.exercises.map((exercise, idx) => (
                            <div
                              key={`${activeDayKey}-ex-${idx}`}
                              className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <Dumbbell className="h-5 w-5 text-amber-500" />
                                  <h4 className="text-lg font-semibold text-white">{exercise.exerciseName}</h4>
                                </div>
                                <span className="text-sm text-gray-400">{exercise.specs.duration} mins</span>
                              </div>
                              <p className="text-sm text-gray-400 mt-2">
                                <strong>Details:</strong> {exercise.details}
                              </p>
                            </div>
                          ))
                        ) : (
                          <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10 text-center">
                            <h3 className="text-lg font-semibold text-white">Rest Day</h3>
                            <p className="text-sm text-gray-400">Focus on recovery and light stretching</p>
                          </div>
                        )}
                      </div>
                    )}

                    {activeTab === "strategies" && (
                      <div className="space-y-6">
                        <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10">
                          <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                            <Activity className="w-5 h-5 mr-2 text-blue-500" />
                            Program Notes
                          </h3>
                          <p className="text-sm text-gray-400">
                            {data.notes && data.notes.length > 0 ? data.notes[0] : "No notes available"}
                          </p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10">
                            <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                              <Target className="w-5 h-5 mr-2 text-blue-500" />
                              Motivation
                            </h3>
                            <p className="text-sm text-gray-400">{data.strategies?.maintainMotivation || "N/A"}</p>
                          </div>
                          <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10">
                            <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                              <Target className="w-5 h-5 mr-2 text-blue-500" />
                              Overcoming Plateaus
                            </h3>
                            <p className="text-sm text-gray-400">{data.strategies?.overcomePlateaus || "N/A"}</p>
                          </div>
                          <div className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10">
                            <h3 className="text-lg font-semibold text-white mb-2 flex items-center">
                              <Target className="w-5 h-5 mr-2 text-blue-500" />
                              Variety
                            </h3>
                            <p className="text-sm text-gray-400">{data.strategies?.variety || "N/A"}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

