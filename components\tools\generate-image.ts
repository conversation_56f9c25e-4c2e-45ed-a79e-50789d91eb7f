import { adminDb, adminStorage } from '../../components/firebase/admin'; // Adjusted path based on project structure
import { generateWithImagen } from "./imagen"; // Assuming this is correctly implemented
import { GenerateImageAgent } from './GenerateImageAgent'; // Assuming this is correctly implemented
import { GenerateImageTool as ComponentImageTool } from "../../components/tools/generateImageTool"; // Assuming this is correctly implemented
import OpenAI from 'openai';
import { v4 as uuidv4 } from 'uuid';

// Define types for image generation
export type ImageModel = 'dall-e-3' | 'dall-e-2' | 'gpt-image-1' | 'imagen-3.0-generate-002';
// Define types for image generation
// Note: OpenAI API only supports specific sizes, but we support additional ones for other providers
export type OpenAIImageSize = '1024x1024' | '1792x1024' | '1024x1792' | '512x512' | '256x256';
export type ImageSize = OpenAIImageSize | '1536x1024' | '1024x1536' | 'auto';
export type ImageStyle = 'vivid' | 'natural';
export type ImageQuality = 'low' | 'medium' | 'high' | 'auto' | 'standard' | 'hd';
export type ImageFormat = 'png' | 'jpeg' | 'webp';
export type ImageBackground = 'transparent' | 'opaque' | 'auto';
export type ImageCompression = number; // 0-100%

export interface ImageGenerationOptions {
  prompt: string;
  refinePrompt?: boolean;
  model?: ImageModel;
  size?: ImageSize;
  style?: ImageStyle;
  quality?: ImageQuality;
  format?: ImageFormat;
  background?: ImageBackground;
  compression?: ImageCompression;
  userId?: string;
}

export interface ImageGenerationResult {
  imageUrl: string;
  prompt: string;
  model: ImageModel;
  jobId: string;
  namespace: string;
  timestamp: string;
  savedToGallery?: boolean;
  galleryUrl?: string;
  message?: string;
}

interface OpenAIImageResponse {
  url: string;
  revised_prompt?: string;
  namespace?: string;
  [key: string]: string | undefined;
}

// Define a type for request body to OpenAI - updated to match OpenAI SDK expectations
interface OpenAIImageRequestBody {
  model: string;
  prompt: string;
  n: number;
  size: ImageSize;
  response_format: string;
  quality?: "standard" | "hd"; // Updated to match OpenAI SDK requirements
  style?: ImageStyle;
  output_format?: ImageFormat; // Made optional
  background?: ImageBackground;
  compression?: ImageCompression;
}

// Type for job data from Firestore
interface JobData {
  model: ImageModel;
  size: ImageSize;
  style: ImageStyle;
  quality: ImageQuality;
  format: ImageFormat;
  background: ImageBackground;
  compression?: ImageCompression;
  prompt: string;
  refinedPrompt?: string;
  originalPrompt?: string;
  status?: string;
  imageUrl?: string;
  updatedAt?: Date;
  processedAt?: Date;
}

// Type for result from Imagen
interface _ImagenResult {
  base64Image?: string;
  imageUrl?: string;
  namespace?: string;
  error?: string;
}

// Type for component image tool result
interface _ComponentImageToolResult {
  success: boolean;
  base64Image?: string;
  error?: string;
}

// Note: Removed unused error type definitions

export class GenerateImageTool {
  private apiKey: string | undefined;
  private _baseUrl: string; // Prefixed with underscore to indicate it's intentionally unused
  private defaultModel: ImageModel;
  private defaultSize: ImageSize;
  private imageAgent: GenerateImageAgent | null = null;
  private componentImageTool: ComponentImageTool | null = null;

  static description = {
    name: "generateImage",
    description: "Generate images from text descriptions using AI models like gpt-image-1, DALL-E, Imagen, and store them in Firebase.",
    parameters: {
      type: "object",
      properties: {
        prompt: { type: "string", description: "Text description of the image to generate." },
        refinePrompt: { type: "boolean", description: "Whether to refine the prompt with AI before generating the image.", default: true },
        model: { type: "string", description: "The image generation model to use.", enum: ["dall-e-3", "gpt-image-1", "dall-e-2", "imagen-3.0-generate-002"], default: "gpt-image-1" },
        size: { type: "string", description: "The size of the generated image.", enum: ["1024x1024", "1536x1024", "1024x1536", "1792x1024", "1024x1792", "auto", "512x512", "256x256"], default: "1024x1024" },
        style: { type: "string", description: "The style of the generated image (DALL-E 3 only).", enum: ["vivid", "natural"], default: "vivid" },
        quality: { type: "string", description: "The quality of the generated image.", enum: ["low", "medium", "high", "auto", "standard", "hd"], default: "auto" },
        format: { type: "string", description: "The file format of the generated image.", enum: ["png", "jpeg", "webp"], default: "jpeg" },
        background: { type: "string", description: "The background type for the generated image.", enum: ["transparent", "opaque", "auto"], default: "auto" },
        compression: { type: "number", description: "Compression level (0-100%) for JPEG and WebP formats.", minimum: 0, maximum: 100 },
        userId: { type: "string", description: "User ID for Firebase storage integration." }
      },
      required: ["prompt"]
    },
    returns: {
      type: "object",
      properties: {
        imageUrl: { type: "string", description: "URL of the generated image" },
        prompt: { type: "string", description: "The prompt used to generate the image (may be refined)" },
        model: { type: "string", description: "The model used to generate the image" },
        jobId: { type: "string", description: "Unique identifier for the image generation job" },
        namespace: { type: "string", description: "Namespace for the image in the storage system" }
      }
    },
    examples: [
      {
        input: { prompt: "A serene mountain landscape at sunset with a lake reflecting the sky", userId: "user123" },
        output: "Returns a URL to the generated image of a mountain landscape"
      },
      {
        input: {
          prompt: "A futuristic city with flying cars",
          model: "gpt-image-1",
          size: "1024x1024",
          userId: "user123"
        },
        output: "Returns a URL to a high-quality image of a futuristic city"
      }
    ]
  };

  constructor() {
    this.apiKey = process.env.OPENAI_API_KEY;
    this._baseUrl = 'https://api.openai.com/v1';
    this.defaultModel = 'gpt-image-1';
    this.defaultSize = '1024x1024';

    if (this.apiKey) {
      this.componentImageTool = new ComponentImageTool(this.apiKey);
      this.imageAgent = new GenerateImageAgent({ generateImageTool: this.componentImageTool });
    }
  }

  async generateImage(options: ImageGenerationOptions): Promise<ImageGenerationResult> {
    const {
      prompt,
      model = this.defaultModel,
      userId,
      refinePrompt: _refinePrompt = true, // Renamed to _refinePrompt to indicate it's not used directly
      size = this.defaultSize,
      style = 'vivid',
      quality = 'auto',
      format = 'jpeg',
      background = 'auto',
      compression
    } = options;

    if (!prompt) {
      throw new Error('Prompt is required for image generation');
    }

    if (userId && this.imageAgent) {
      return this.generateImageWithFirebase(
        prompt,
        userId,
        model,
        size,
        style,
        quality,
        format,
        background,
        compression
      );
    } else {
      return this.generateImageDirect(options);
    }
  }

  private async generateImageWithFirebase(
    prompt: string,
    userId: string,
    model: ImageModel,
    size?: ImageSize,
    style?: ImageStyle,
    quality?: ImageQuality,
    format?: ImageFormat,
    background?: ImageBackground,
    compression?: ImageCompression
  ): Promise<ImageGenerationResult> {
    try {
      if (!this.imageAgent) {
        throw new Error('Image agent not initialized');
      }

      const jobId = await this.imageAgent.initializeJob(
        prompt,
        userId,
        model || 'gpt-image-1',
        size || '1024x1024',
        style || 'vivid',
        quality || 'auto',
        format || 'jpeg',
        background || 'auto',
        compression
      );
      console.log(`Job initialized with ID: ${jobId}`);

      try {
        await this.imageAgent.refinePrompt(jobId, userId);
        console.log(`Prompt refined for job: ${jobId}`);
      } catch (refinementError: unknown) {
        const errorMessage = refinementError instanceof Error ? refinementError.message : String(refinementError);
        console.warn('Prompt refinement failed, using original prompt:', errorMessage);
      }

      const jobRef = await adminDb.collection('users').doc(userId).collection('images').doc(jobId).get();
      if (!jobRef.exists) {
        throw new Error(`Job ${jobId} not found for user ${userId}`);
      }
      const jobData = jobRef.data() as JobData | undefined;
      if (!jobData) {
        throw new Error(`No data found for job ${jobId}`);
      }
      const promptToUse = jobData.refinedPrompt || jobData.prompt;

      if (jobData.model && jobData.model.includes('imagen')) {
        console.log(`Using Google Imagen model for job: ${jobId}`);
        const imagenResult = await generateWithImagen({
          prompt: promptToUse,
          model: jobData.model,
          numberOfImages: 1,
          userId,
          jobId
        });
        console.log(`Image generated with Imagen for job: ${jobId}`);
        if (!imagenResult.base64Image) {
          throw new Error('No image data returned from Imagen');
        }
        const processedResult = await this.processAndStoreImage(
          imagenResult.base64Image,
          userId,
          jobId,
          promptToUse,
          jobData.model,
          imagenResult.namespace
        );
        console.log(`Imagen image processed and stored with namespace: ${processedResult.namespace}`);
        return {
          imageUrl: processedResult.imageUrl,
          prompt: promptToUse,
          model: jobData.model,
          jobId,
          namespace: processedResult.namespace,
          timestamp: new Date().toISOString(),
          savedToGallery: true,
          galleryUrl: '/imageGallery',
          message: 'Image has been saved to your gallery'
        };
      }

      const imageResponse = await this.imageAgent.generateImage({
        prompt: promptToUse,
        model: jobData.model,
        size: jobData.size,
        style: jobData.style,
        quality: jobData.quality,
        format: jobData.format,
        background: jobData.background,
        compression: jobData.compression
      });
      console.log(`Image generated for job: ${jobId} with model: ${jobData.model}`);
      if (!imageResponse.success || !imageResponse.base64Image) {
        throw new Error(imageResponse.error || 'Failed to generate image');
      }
      const base64Data = imageResponse.base64Image.replace(/^data:image\/\w+;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');
      if (imageBuffer.length === 0) {
        throw new Error('Generated image buffer is empty');
      }
      const bucket = adminStorage.bucket();
      const filePath = `users/${userId}/generated/${jobId}.png`;
      const file = bucket.file(filePath);
      await file.save(imageBuffer, {
        metadata: {
          contentType: 'image/png',
          metadata: {
            jobId,
            userId,
            generatedAt: new Date().toISOString()
          }
        }
      });
      console.log(`Image uploaded to Firebase Storage for job: ${jobId}`);
      const [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });
      console.log(`Signed URL generated for job: ${jobId}`);
      await adminDb.collection('users').doc(userId).collection('images').doc(jobId).update({
        status: 'completed',
        imageUrl: downloadUrl,
        updatedAt: new Date(),
        processedAt: new Date()
      });
      console.log(`Firestore job status updated for job: ${jobId}`);
      const namespace = uuidv4();
      await adminDb.collection('users').doc(userId).collection('files').doc(namespace).set({
        category: 'My Images',
        createdAt: new Date(),
        downloadUrl: downloadUrl,
        isImage: true,
        name: promptToUse,
        namespace: namespace,
        ref: `uploads/${userId}/generated/${jobId}.png`,
        size: imageBuffer.length,
        type: 'image/png',
        jobId: jobId,
        description: jobData.originalPrompt || jobData.prompt,
      });
      console.log(`Files collection record created for job: ${jobId} with namespace: ${namespace}`);
      return {
        imageUrl: downloadUrl,
        prompt,
        model,
        jobId,
        namespace,
        timestamp: new Date().toISOString(),
        savedToGallery: true,
        galleryUrl: '/imageGallery',
        message: 'Image has been saved to your gallery'
      };
    } catch (error: unknown) {
      console.error('Firebase image generation error:', error);
      throw new Error(`Failed to generate image with Firebase: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async generateImageDirect(options: ImageGenerationOptions): Promise<ImageGenerationResult> {
    const {
      prompt,
      refinePrompt = true,
      model = this.defaultModel,
      size = this.defaultSize,
      style = 'vivid',
      quality = 'auto',
      format = 'jpeg',
      background = 'auto',
      compression
    } = options;

    try {
      const jobId = this._generateJobId();
      let finalPrompt = prompt;

      // We're keeping refinePrompt and using it appropriately here
      if (refinePrompt && this.componentImageTool) {
        try {
          finalPrompt = await this.componentImageTool.refinePrompt(prompt);
          console.log('Prompt refined successfully');
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.warn('Prompt refinement failed, using original prompt:', errorMessage);
        }
      } else if (refinePrompt) {
        finalPrompt = await this._refinePrompt(prompt);
      }

      let imageData: OpenAIImageResponse;
      if (model.includes('imagen')) {
        console.log('Using Google Imagen model for image generation');
        const imagenResult = await generateWithImagen({
          prompt: finalPrompt,
          model: 'imagen-3.0-generate-002',
          numberOfImages: 1,
          userId: options.userId,
          jobId
        });
        imageData = {
          url: imagenResult.imageUrl || '',
          revised_prompt: finalPrompt,
          namespace: imagenResult.namespace
        };
      } else if (this.componentImageTool && (model === 'gpt-image-1' || model === 'dall-e-3')) {
        console.log(`Using ${model} model via component tool`);
        const result = await this.componentImageTool.generateImageBase64(
          finalPrompt,
          model,
          size,
          quality,
          format,
          background,
          compression
        );
        if (!result.success || !result.base64Image) {
          throw new Error(result.error || 'Failed to generate image');
        }
        const base64Data = result.base64Image.startsWith('data:image/') ? result.base64Image : `data:image/png;base64,${result.base64Image}`;
        imageData = {
          url: base64Data,
          revised_prompt: finalPrompt
        };
      } else {
        console.log('Using OpenAI DALL-E model for image generation');
        imageData = await this._generateImageWithOpenAI(
          finalPrompt,
          model,
          size,
          style,
          quality,
          format,
          background,
          compression
        );
      }
      const namespace = imageData.namespace || uuidv4();
      console.log(`Using namespace for image: ${namespace}`);
      return {
        imageUrl: imageData.url,
        prompt: finalPrompt,
        model,
        jobId,
        namespace,
        timestamp: new Date().toISOString(),
        savedToGallery: false,
        message: 'Image generated successfully. Click "Save to Gallery" to save it to your gallery.'
      };
    } catch (error: unknown) {
      console.error('Image generation error:', error);
      throw new Error(`Failed to generate image: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  // Updated method to handle OpenAI API compatibility
  private async _generateImageWithOpenAI(
    prompt: string,
    model: ImageModel,
    size: ImageSize,
    style: ImageStyle,
    quality: ImageQuality,
    format: ImageFormat = 'png',
    background: ImageBackground = 'auto',
    compression?: ImageCompression
  ): Promise<OpenAIImageResponse> {
    if (!this.apiKey) {
      throw new Error('OpenAI API key is required');
    }
    try {
      const openai = new OpenAI({ apiKey: this.apiKey });

      // Create base request body
      const requestBody: OpenAIImageRequestBody = {
        model,
        prompt,
        n: 1,
        size,
        response_format: 'b64_json'
      };

      // Handle model-specific parameters with proper type handling
      if (model === 'dall-e-3') {
        // For DALL-E 3, only 'standard' and 'hd' are valid quality values
        requestBody.quality = this._mapQualityForDallE3(quality);
        requestBody.style = style;
      } else if (model === 'gpt-image-1') {
        // For GPT Image, handle specific requirements
        if (['standard', 'hd'].includes(quality)) {
          requestBody.quality = quality as "standard" | "hd";
        }
        requestBody.background = background;
        if (compression !== undefined && (format === 'jpeg' || format === 'webp')) {
          requestBody.compression = compression;
        }
      }

      console.log(`Generating image with OpenAI ${model} model using parameters:`, {
        model, size, quality: requestBody.quality, style: requestBody.style
      });

      // Convert our size to an OpenAI compatible size if needed
      const openaiSize = this._ensureOpenAICompatibleSize(requestBody.size);

      // Define the specific response type for OpenAI's image generation
      type OpenAIImageGenResponse = {
        data: Array<{
          b64_json?: string;
          url?: string;
          revised_prompt?: string;
        }>;
      };

      const response = await openai.images.generate({
        model: requestBody.model,
        prompt: requestBody.prompt,
        n: requestBody.n,
        size: openaiSize,
        response_format: 'b64_json' as const,
        ...(requestBody.quality && { quality: requestBody.quality }),
        ...(requestBody.style && { style: requestBody.style }),
        ...(requestBody.background && { background: requestBody.background }),
        ...(requestBody.compression && { compression: requestBody.compression })
      }) as unknown as OpenAIImageGenResponse;

      if (response.data[0].b64_json) {
        return {
          url: `data:image/png;base64,${response.data[0].b64_json}`,
          revised_prompt: response.data[0].revised_prompt
        };
      } else if (response.data[0].url) {
        return {
          url: response.data[0].url,
          revised_prompt: response.data[0].revised_prompt
        };
      } else {
        throw new Error('No image data returned from OpenAI');
      }
    } catch (error: unknown) {
      console.error('OpenAI image generation error:', error);
      throw error;
    }
  }

  // New helper method to map our quality values to DALL-E 3 compatible values
  private _mapQualityForDallE3(quality: ImageQuality): "standard" | "hd" {
    // Map our quality values to DALL-E 3 accepted values
    switch (quality) {
      case 'high':
      case 'hd':
        return 'hd';
      case 'low':
      case 'medium':
      case 'auto':
      case 'standard':
      default:
        return 'standard';
    }
  }

  private async _refinePrompt(prompt: string): Promise<string> {
    if (!this.apiKey) {
      return prompt;
    }
    try {
      const openai = new OpenAI({ apiKey: this.apiKey });

      // Define the specific response type for OpenAI's chat completion
      type OpenAIChatResponse = {
        choices: Array<{
          message?: {
            content?: string;
          };
        }>;
      };

      const chatCompletion = await openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at creating detailed, vivid prompts for image generation. Enhance the user\'s prompt to include more details about style, lighting, composition, and mood. Keep the core idea intact but make it more descriptive for better image generation results.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 300
      }) as unknown as OpenAIChatResponse;

      return chatCompletion.choices[0]?.message?.content || prompt;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.warn('Prompt refinement failed, using original prompt:', errorMessage);
      return prompt;
    }
  }

  async processAndStoreImage(
    base64Image: string,
    userId: string,
    jobId: string,
    prompt: string,
    model: string = 'gpt-image-1',
    namespace?: string
  ): Promise<Record<string, string>> {
    try {
      const base64Data = base64Image.replace(/^data:image\/\w+;base64,/, '');
      const imageBuffer = Buffer.from(base64Data, 'base64');
      if (imageBuffer.length === 0) {
        throw new Error('Generated image buffer is empty');
      }
      const bucket = adminStorage.bucket();
      const filePath = `users/${userId}/generated/${jobId}.png`;
      const file = bucket.file(filePath);
      await file.save(imageBuffer, {
        metadata: {
          contentType: 'image/png',
          metadata: {
            jobId,
            userId,
            generatedAt: new Date().toISOString()
          }
        }
      });
      console.log(`Image uploaded to Firebase Storage for job: ${jobId}`);
      const [downloadUrl] = await file.getSignedUrl({
        action: 'read',
        expires: '03-01-2500'
      });
      console.log(`Signed URL generated for job: ${jobId}`);
      try {
        const jobRef = adminDb.collection('users').doc(userId).collection('images').doc(jobId);
        const jobDoc = await jobRef.get();
        if (jobDoc.exists) {
          await jobRef.update({
            status: 'completed',
            imageUrl: downloadUrl,
            updatedAt: new Date(),
            processedAt: new Date()
          });
          console.log(`Firestore job status updated for job: ${jobId}`);
        }
      } catch (updateError: unknown) {
        const errorMessage = updateError instanceof Error ? updateError.message : String(updateError);
        console.warn(`Could not update job status: ${errorMessage}`);
      }
      const fileNamespace = namespace || uuidv4();
      console.log(`Using namespace for image in Firebase: ${fileNamespace}`);
      try {
        await adminDb.collection('users').doc(userId).collection('files').doc(fileNamespace).set({
          category: 'My Images',
          createdAt: new Date(),
          downloadUrl: downloadUrl,
          isImage: true,
          name: prompt.substring(0, 100),
          namespace: fileNamespace,
          ref: `uploads/${userId}/generated/${jobId}.png`,
          size: imageBuffer.length,
          type: 'image/png',
          jobId: jobId,
          description: prompt.substring(0, 1000),
          model: model
        });
        console.log(`Files collection record created for job: ${jobId} with namespace: ${namespace}`);
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`Error saving to files collection: ${errorMessage}`);
      }
      return {
        imageUrl: downloadUrl,
        jobId,
        userId,
        prompt,
        namespace: fileNamespace,
        timestamp: new Date().toISOString(),
        savedToGallery: 'true',
        galleryUrl: '/imageGallery',
        message: 'Image has been saved to your gallery'
      };
    } catch (error: unknown) {
      console.error('Image processing error:', error);
      throw new Error(`Failed to process image: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private _generateJobId(): string {
    return `img_${Date.now()}_${Math.random().toString(36).substring(2, 10)}`;
  }

  /**
   * Ensures the size is compatible with OpenAI's API
   * @param size The size to check
   * @returns A size that is compatible with OpenAI's API
   */
  private _ensureOpenAICompatibleSize(size: ImageSize): OpenAIImageSize {
    // Check if the size is already compatible
    if (
      size === '1024x1024' ||
      size === '1792x1024' ||
      size === '1024x1792' ||
      size === '512x512' ||
      size === '256x256'
    ) {
      return size;
    }

    // Default to 1024x1024 for unsupported sizes
    console.warn(`Size ${size} is not supported by OpenAI API, defaulting to 1024x1024`);
    return '1024x1024';
  }

  getDescription(): typeof GenerateImageTool.description {
    return GenerateImageTool.description;
  }

  getAvailableMethods(): Record<string, string> {
    return {
      generateImage: "Generate an image from a text prompt and store it in Firebase"
    };
  }
}

export const generateImageTool = new GenerateImageTool();