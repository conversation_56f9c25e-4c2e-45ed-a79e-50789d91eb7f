// hooks/useGenerateWorkoutPlanAndSave.ts

"use client";

import { useState, useEffect } from "react";
import { useSaveAgentResponses } from "./useSaveAgentResponses";
import type { ProgressStep } from "@/types/progress";
import {
  isErrorStep,
  isCompletedStep,
} from "@/types/progress";

interface ToastMessage {
  title: string;
  message: string;
  type: "success" | "error" | null;
}

interface UseGenerateWorkoutPlanAndSaveResult {
  isGeneratingPlan: boolean;
  generateAndSaveWorkoutPlan: (
    profileText: string,
    userEmail: string
  ) => Promise<void>;
  toastMessage: ToastMessage | null;
  progress: ProgressStep;
}

export const useGenerateWorkoutPlanAndSave = (): UseGenerateWorkoutPlanAndSaveResult => {
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const { saveAgentResponses } = useSaveAgentResponses();
  const [toastMessage, setToastMessage] = useState<ToastMessage | null>(null);
  const [progress, setProgress] = useState<ProgressStep>("Initiating");

  const generateAndSaveWorkoutPlan = async (
    profileText: string,
    userEmail: string
  ): Promise<void> => {
    setIsGeneratingPlan(true);
    setToastMessage(null);
    setProgress("Initiating");

    try {
      if (!userEmail) {
        throw new Error("No email associated with user");
      }

      // Step 1: Generating Training Plan
      setProgress("Generating Training Plan");
      const trainingPlanResponse = await fetch("/api/generateworkoutplan", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userEmail,
          profileText: profileText,
        }),
      });

      if (!trainingPlanResponse.ok) {
        const errorData = await trainingPlanResponse.json();
        const errorMessage =
          errorData.error || "Failed to generate workout plan. Please try again.";
        setToastMessage({
          title: "Error",
          message: errorMessage,
          type: "error",
        });
        setProgress("Error");
        throw new Error(errorMessage);
      }

      const responseData = await trainingPlanResponse.json();
      console.log("Workout plan data fetched:", responseData);

      // Assuming the API returns information about each step's completion
      // Since the current API does not support incremental progress, we simulate progress steps
      // In a real-world scenario, the API should provide progress updates (e.g., via WebSockets or SSE)

      // Simulating progress steps
      setProgress("Generating Short Term Plan");
      // Simulate time taken for the step (optional)
      // await new Promise((resolve) => setTimeout(resolve, 500));

      setProgress("Generating Long Term Plan");
      // await new Promise((resolve) => setTimeout(resolve, 500));

      setProgress("Engaging form technique coach");
      // await new Promise((resolve) => setTimeout(resolve, 500));

      setProgress("Generating Nutritional suggestions");
      // await new Promise((resolve) => setTimeout(resolve, 500));

      // Step 2: Saving Data
      setProgress("Saving Data");
      await saveAgentResponses(responseData, userEmail);

      // Step 3: Completed
      setToastMessage({
        title: "Success",
        message: "Your new workout plan is ready!",
        type: "success",
      });
      setProgress("Completed");
    } catch (error: any) {
      console.error("Error generating and saving workout plan:", error);
      // If not already set to "Error", set it
      if (progress !== "Error") {
        setProgress("Error");
      }
      setToastMessage({
        title: "Error",
        message: `Failed to generate workout plan. ${error?.message}`,
        type: "error",
      });
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  // Optional: Reset progress after completion or error
  useEffect(() => {
    if (isCompletedStep(progress) || isErrorStep(progress)) {
      const timer = setTimeout(() => {
        setProgress("Initiating");
        setToastMessage(null);
      }, 5000); // Reset after 5 seconds

      return () => clearTimeout(timer);
    }
  }, [progress]);

  return {
    isGeneratingPlan,
    generateAndSaveWorkoutPlan,
    toastMessage,
    progress,
  };
};
