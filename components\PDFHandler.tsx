// components/pdf/PDFHandler.tsx

import { useState } from "react"
import { generateAndDownloadPDF } from "./generate-and-store-pdf"

interface PDFHandlerProps {
  userEmail: string
  profileText: string | null
  onError: (error: string) => void
}

export const PDFHandler = ({ userEmail, profileText, onError }: PDFHandlerProps) => {
  const [generatingPDF, setGeneratingPDF] = useState<boolean>(false)

  const handleGeneratePDF = (): void => {
    if (!profileText) {
      onError("No summary available to generate PDF.")
      return
    }
    
    setGeneratingPDF(true)
    try {
      generateAndDownloadPDF({
        userEmail,
        profileText,
      })
    } catch (pdfError) {
      console.error("Failed to generate PDF:", pdfError)
      onError("Failed to generate PDF. Please try again.")
    } finally {
      setGeneratingPDF(false)
    }
  }

  return {
    handleGeneratePDF,
    generatingPDF
  }
}