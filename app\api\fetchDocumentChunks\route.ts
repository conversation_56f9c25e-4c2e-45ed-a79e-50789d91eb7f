// app/api/fetchDocumentChunks/route.ts
import { NextRequest, NextResponse } from "next/server";
import { FirestoreStore } from "@/lib/FirestoreStore";
import { getAuth } from "firebase-admin/auth";

/**
 * Handles POST requests to fetch document chunks by their IDs
 * 
 * @param request The incoming request containing chunkIds and userId
 * @returns JSON response with fetched chunks or appropriate error
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse the request body
    const body = await request.json();
    const { chunkIds, userId } = body;

    // Validate required parameters
    if (!chunkIds || !Array.isArray(chunkIds) || !userId) {
      return NextResponse.json(
        { error: "Invalid request: chunkIds and userId are required" },
        { status: 400 }
      );
    }

    // Extract authorization token from headers
    const authHeader = request.headers.get("authorization");
    const token = authHeader ? authHeader.split(" ")[1] : "";

    // Verify the user authentication
    const auth = getAuth();
    try {
      const decodedToken = await auth.verifyIdToken(token || "");
      if (decodedToken.uid !== userId) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 403 }
        );
      }
    } catch (authError) {
      console.error("Authentication error:", authError);
      return NextResponse.json(
        { error: "Authentication failed" },
        { status: 401 }
      );
    }

    // Initialize Firestore store with user-specific collection path
    const firestoreStore = new FirestoreStore({
      collectionPath: `users/${userId}/byteStoreCollection`,
    });

    // Fetch documents by their IDs
    const documents = await firestoreStore.mgetByDocId(chunkIds);
    
    // Transform documents to the expected response format
    const chunks = documents.map((doc) => ({
      chunkId: doc.metadata?.chunk_id || "",
      pageContent: doc.pageContent,
      metadata: doc.metadata,
    }));

    // Return successful response
    return NextResponse.json({ chunks });
  } catch (error) {
    // Log and handle any unexpected errors
    console.error("Error fetching document chunks:", error);
    return NextResponse.json(
      { error: "Failed to fetch document chunks" },
      { status: 500 }
    );
  }
}

/**
 * Handle OPTIONS requests for CORS preflight
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}