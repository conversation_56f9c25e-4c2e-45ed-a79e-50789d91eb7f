import { db } from "@/components/firebase/config";
import { doc, setDoc } from "firebase/firestore";
import { type NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";
import { getGroqClient, callGroqAi } from "@/components/groqClient";

interface RequestBody {
  userEmail: string;
  profileText: string;
}

export async function POST(request: NextRequest) {
  let assessmentDocRef;
  let userEmail: string;

  console.log("=== Starting Profile Summary Generation ===");

  try {
    // 1. Parse and validate request body with error handling
    console.log("Step 1: Parsing request body");
    let body: RequestBody;
    try {
      const requestText = await request.text(); // Log the raw request body for debugging
      console.log("Raw request body:", requestText);

      // Attempt to parse JSON, handling potential malformed input
      body = JSON.parse(requestText) as RequestBody;
    } catch (parseError) {
      console.error("JSON Parsing Error:", parseError);
      return NextResponse.json(
        {
          error: "Invalid request",
          details: "Malformed JSON body: " + (parseError instanceof Error ? parseError.message : "Unknown parsing error"),
          code: "MALFORMED_JSON",
        },
        { status: 400 },
      );
    }

    userEmail = body.userEmail;
    const profileText = body.profileText;

    console.log("Received request for user:", userEmail);
    console.log("Profile text length:", profileText?.length || 0);

    if (!userEmail || typeof userEmail !== "string" || !userEmail.includes("@")) {
      console.error("Invalid email provided:", userEmail);
      return NextResponse.json(
        {
          error: "Invalid request",
          details: "Valid user email required",
          code: "INVALID_EMAIL",
        },
        { status: 400 },
      );
    }

    if (!profileText) {
      console.error("Missing profile text");
      return NextResponse.json(
        {
          error: "Invalid request",
          details: "Profile text is required",
          code: "INVALID_PROFILE_TEXT",
        },
        { status: 400 },
      );
    }

    // 2. Initialize assessment document reference
    console.log("Step 2: Initializing assessment document");
    assessmentDocRef = doc(db, `IF_users/${userEmail}/Profile/assessment`);
    await setDoc(
      assessmentDocRef,
      {
        status: "processing",
        lastUpdated: new Date().toISOString(),
        error: null,
      },
      { merge: true },
    );
    console.log("Assessment document initialized with processing status");

    // 3. Initialize Groq client and generate summary
    console.log("Step 3: Initializing Groq client");
    try {
      getGroqClient({ userEmail });
    } catch (error) {
      console.error("Failed to initialize Groq client:", error);
      throw new Error("AI_SERVICE_UNAVAILABLE");
    }

    const systemPrompt = `You are a personal fitness trainer and coach at Intelligentfitness.ai, acting as if 
      you are speaking directly to a client.  
      **You MUST address all comments directly to the client using "you" and "your,"and refer to yourself as "I." 
       Do NOT discuss the client in the third person (e.g., avoid phrases like "the user," "her goal," "she's")**,
       When providing advice, direct all comments only to the client and consider the following aspects:
        
                ANALYSIS FRAMEWORK:
                
                # Anthropometric Assessment
                Account for Gender
                Interpret the provided body measurements
                Identify key structural characteristics
                Note any biomechanical implications
                
                # Body Type Classification
                Determine the predominant somatotype (ectomorph, mesomorph, endomorph) as stated in the {profileText}
                
                # Movement Considerations
                Identify potential mobility limitations
                Assess leverage advantages/disadvantages
                Consider joint stability requirements

                # Training Preferences and Recommendations
                Account for workout preferences found in the {profileText}
                Account for training geared to a specific activity such as a sport                
                Suggest optimal training modalities based on the user profiles
                Explain why certain training styles are more suitable
                Address potential compensatory mechanisms
                
                # Risk Assessment
                Highlight areas requiring special attention
                Identify potential movement pattern issues
                Suggest preventive approaches
                
                FORMAT YOUR RESPONSE WITH:
                Summary: A brief overview of the key anthropometric characteristics and their implications
                Primary Focus: The main training direction based on the analysis
                Training Style: Always suggest training approaches that would be most effective for the client in particular given their attached profile
                Special Considerations: Any specific adaptations. preferences or modifications needed
                Long-term Outlook: Progressive development possibilities`.trim();

    console.log("Generating summary with Groq");
    const response = await callGroqAi({
      messages: [
        {
          role: "system",
          content: systemPrompt,
          name: "system",
        },
        {
          role: "user",
          content: profileText,
          name: "user",
        },
      ],
      temperature: 0.7,
      maxTokens: 2500,
    });

    if (!response?.choices?.[0]?.message?.content) {
      throw new Error("Invalid or empty response from AI service");
    }

    console.log("Summary generated successfully");

    // 4. Process AI response
    console.log("Step 4: Processing AI response");
    const summary = response.choices[0].message.content;
    console.log("Summary length:", summary.length);

    // 5. Update assessment with final summary
    console.log("Step 5: Updating assessment with final summary");
    await setDoc(
      assessmentDocRef,
      {
        status: "completed",
        summary: summary,
        summaryGeneratedAt: new Date().toISOString(),
        lastUpdated: new Date().toISOString(),
        HasPdf: false,
        error: null,
      },
      { merge: true },
    );
    console.log("Assessment document completed");

    // 5b) Setup files document in Firestore, mark HasPdf=false
    const newDocumentId = uuidv4();
    const filesRef = doc(db, `users/${userEmail}/files/${newDocumentId}`);
    console.log("Step 5b: Setup files document in Firestore, mark HasPdf=false");
    try {
      await setDoc(
        filesRef,
        {
          category: "fitnessintelligentai",
          summary,
          isImage: false,
          createdAt: new Date().toISOString(),
          name: "Fitness Summary",
          namespace: newDocumentId,
        },
        { merge: true },
      );
      console.log("Successfully wrote to filesRef!");
    } catch (err) {
      console.error("Error writing filesRef:", err);
    }

    // 6. Return success response
    console.log("Step 6: Returning success response", summary);
    return NextResponse.json({
      summary: summary,
      status: "success",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error in profile summary generation:", error);
    console.error("Full error details:", JSON.stringify(error, null, 2));

    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    const errorResponse = {
      error: "Failed to generate profile summary",
      code: errorMessage.includes(":") ? errorMessage.split(":")[0] : "UNKNOWN_ERROR",
      details: errorMessage,
      timestamp: new Date().toISOString(),
    };

    if (assessmentDocRef) {
      try {
        console.log("Updating assessment with error status");
        await setDoc(
          assessmentDocRef,
          {
            status: "error",
            lastError: errorMessage,
            lastUpdated: new Date().toISOString(),
          },
          { merge: true },
        );
      } catch (updateError) {
        console.error("Failed to update error status:", updateError);
      }
    }

    const statusCode =
      errorMessage.includes("INVALID_EMAIL") ||
      errorMessage.includes("INVALID_PROFILE_TEXT") ||
      errorMessage.includes("MALFORMED_JSON")
        ? 400
        : errorMessage.includes("AI_SERVICE_UNAVAILABLE")
          ? 503
          : 500;

    return NextResponse.json(errorResponse, { status: statusCode });
  }
}