import {
  doc,
  getDoc,
  setDoc,
  Firestore,
} from "firebase/firestore";
import { v4 as uuidv4 } from "uuid";
import { processWorkoutPlanForVectorSearch } from "./workout-vector-processor";

/**
 * ============================
 * Interfaces & Type Definitions
 * ============================
 */

interface WarmUpExercise {
  exercise: string;
  libraryId: string;
  duration?: number;
  details?: string;
}

interface CoolDownExercise {
  exercise: string;
  libraryId: string;
  duration?: number;
  details?: string;
}

interface ResistanceTrainingExercise {
  exercise: string;
  sets: number;
  reps: number;
  notes: string;
  libraryId: string;
}

interface CardioTrainingExercise {
  exercise: string;
  duration: number;
  notes: string;
  libraryId: string;
}

interface ProgressiveOverload {
  increase_reps: string;
  increase_sets: string;
  notes: string;
}

interface RestAndRecovery {
  stretching: string;
  foam_rolling: string;
  sleep: string;
}

interface Modifications {
  lower_back: string;
  broken_toe: string;
}

export interface DayData {
  day: number | string;
  name: string;
  duration: number;
  warm_up?: (string | ExerciseDetail)[];
  resistance_training?: {
    exercise: string;
    sets: number;
    reps: number;
    notes: string;
    libraryId?: string; // libraryId is now expected in the input
  }[];
  cardio_training?: {
    exercise: string;
    duration: number | string;
    notes: string;
    libraryId?: string; // libraryId is now expected in the input
  }[];
  cool_down?: (string | ExerciseDetail)[];
  progressive_overload?: ProgressiveOverload;
  rest_and_recovery?: RestAndRecovery;
  modifications?: Modifications;
}

export interface ExerciseProgram {
  title: string;
  days: DayData[];
  lastUpdated?: string;
}

interface OptimizedSchedule {
  title: string;
  days: {
    day: number | string;
    name: string;
    duration: number;
    warm_up: WarmUpExercise[];
    resistance_training: ResistanceTrainingExercise[];
    cardio_training: CardioTrainingExercise[];
    cool_down: CoolDownExercise[];
    progressive_overload: ProgressiveOverload;
    rest_and_recovery: RestAndRecovery;
    modifications: Modifications;
  }[];
  lastUpdated: string;
}

interface ExerciseDetail {
  exercise: string;
  duration?: number;
  details?: string;
  libraryId?: string; // libraryId is now expected in the input
}

/**
 * ============================
 * Utility Functions
 * ============================
 */

function sanitizeString(value: string | number): string {
  return String(value).replace(/[\r\n\t]/g, " ").trim();
}

function sanitizeNumber(value: string | number): number {
  const num = typeof value === "number" ? value : parseFloat(String(value));
  return isNaN(num) ? 0 : num;
}

function safeParseJSON(jsonString: string, debug = false): ExerciseProgram | null {
  if (!jsonString || typeof jsonString !== 'string') {
    console.error("Invalid input: jsonString is empty or not a string");
    return null;
  }

  try {
    if (debug) {
      console.log(`Parsing JSON string (first 100 chars): ${jsonString.substring(0, 100)}...`);
    }

    let cleanedString = jsonString
      .replace(/^```json\s*\n/, "")
      .replace(/\n```$/, "")
      .trim();

    if (cleanedString.startsWith('"') && cleanedString.endsWith('"')) {
      try {
        const unwrappedString = JSON.parse(cleanedString);
        if (typeof unwrappedString === 'string') {
          if (debug) {
            console.log("Detected potential double-stringified JSON, attempting to unwrap");
          }
          cleanedString = unwrappedString;
        }
      } catch {
        if (debug) {
          console.log("Not a double-stringified JSON, continuing with original");
        }
      }
    }

    let parsedData;
    try {
      parsedData = JSON.parse(cleanedString);
    } catch (parseError) {
      console.error("JSON.parse error:", parseError);
      try {
        if (debug) {
          console.log("Attempting alternative parsing approach");
        }
        cleanedString = cleanedString.replace(/\\"/g, '"');
        parsedData = JSON.parse(cleanedString);
      } catch {
        console.error("Alternative JSON parsing failed");
        throw parseError;
      }
    }

    if (!parsedData) {
      console.error("Parse resulted in null or undefined data");
      return null;
    }

    if (typeof parsedData !== 'object') {
      console.error(`Parse resulted in non-object type: ${typeof parsedData}`);
      return null;
    }

    if (!parsedData.title) {
      console.error("Invalid exercise program: missing 'title' property", parsedData);
      if (Array.isArray(parsedData.days) && parsedData.days.length > 0) {
        if (debug) {
          console.log("Attempting recovery: Adding default title to program");
        }
        parsedData.title = "Workout Program";
      } else {
        return null;
      }
    }

    if (!Array.isArray(parsedData.days)) {
      console.error("Invalid exercise program: 'days' property is not an array", parsedData);
      if (parsedData.workoutPlan && Array.isArray(parsedData.workoutPlan.days)) {
        if (debug) {
          console.log("Found days array nested under workoutPlan property, restructuring");
        }
        parsedData.days = parsedData.workoutPlan.days;
      } else if (parsedData.exerciseProgram && typeof parsedData.exerciseProgram === 'object') {
        const nestedProgram = parsedData.exerciseProgram;
        if (nestedProgram.title && Array.isArray(nestedProgram.days)) {
          if (debug) {
            console.log("Found valid exercise program structure nested under exerciseProgram property");
          }
          return nestedProgram as ExerciseProgram;
        }
      } else if (parsedData.exerciseProgram && typeof parsedData.exerciseProgram === 'string') {
        if (debug) {
          console.log("Found potentially stringified exercise program, attempting to parse nested JSON");
        }
        try {
          const nestedProgram = JSON.parse(parsedData.exerciseProgram);
          if (nestedProgram.title && Array.isArray(nestedProgram.days)) {
            if (debug) {
              console.log("Successfully parsed nested exercise program");
            }
            return nestedProgram as ExerciseProgram;
          }
        } catch {
          console.error("Failed to parse nested exercise program");
        }
      } else {
        return null;
      }
    }

    if (parsedData.days.length === 0) {
      console.error("Invalid exercise program: 'days' array is empty");
      return null;
    }

    for (let i = 0; i < parsedData.days.length; i++) {
      const day = parsedData.days[i];
      if (!day) {
        console.error(`Invalid day at index ${i}: day is null or undefined`);
        parsedData.days[i] = {
          day: i + 1,
          name: `Day ${i + 1}`,
          duration: 0
        };
      } else if (!day.name) {
        if (debug) {
          console.log(`Day at index ${i} missing name, adding default name`);
        }
        day.name = `Day ${i + 1}`;
      }

      if (day.day === undefined || day.day === null) {
        if (debug) {
          console.log(`Day at index ${i} missing day number, adding default day number`);
        }
        day.day = i + 1;
      }

      if (day.duration === undefined || day.duration === null) {
        if (debug) {
          console.log(`Day at index ${i} missing duration, adding default duration`);
        }
        day.duration = 0;
      }
    }

    if (debug) {
      console.log(`Successfully parsed exercise program with title "${parsedData.title}" and ${parsedData.days.length} days`);
    }
    return parsedData as ExerciseProgram;
  } catch (error) {
    console.error("Error parsing exercise program JSON:", error);
    console.error("Original JSON string (truncated):", jsonString.substring(0, 200) + "...");
    return null;
  }
}

/**
 * ============================
 * Main Schedule Generation Function
 * ============================
 */

export async function generateAndStoreKickstarterSchedule(
  db: Firestore,
  userEmail: string,
  documentId: string
): Promise<void> {
  if (!db || !userEmail) {
    throw new Error("Database instance and user email are required");
  }
  try {
    console.log(`Starting schedule generation for user: ${userEmail}`);

    const workoutPlanRef = doc(
      db,
      "IF_users",
      userEmail,
      "Profile",
      "workoutplan"
    );
    const workoutPlanSnap = await getDoc(workoutPlanRef);

    if (!workoutPlanSnap.exists()) {
      throw new Error(`No workout plan found for user: ${userEmail}`);
    }

    const workoutPlanData = workoutPlanSnap.data();
    console.log("Workout plan data keys:", Object.keys(workoutPlanData || {}));

    if (!workoutPlanData) {
      throw new Error("Workout plan document exists but contains no data");
    }

    let workoutReference = workoutPlanData.WorkoutReference;

    if (!workoutReference) {
      console.log("Primary WorkoutReference is missing, looking for alternatives");

      const alternativeReference = workoutPlanData.DefaultWorkoutReference ||
                                  workoutPlanData.LastWorkoutReference ||
                                  workoutPlanData.SelectedWorkout;

      if (alternativeReference) {
        console.log(`Found alternative workout reference: ${alternativeReference}`);
        const altFileRef = doc(db, "users", userEmail, "files", alternativeReference);
        const altFileSnap = await getDoc(altFileRef);

        if (altFileSnap.exists()) {
          workoutReference = alternativeReference;
          console.log(`Using alternative workout reference: ${workoutReference}`);
        } else {
          throw new Error(
            `Alternative workout reference (${alternativeReference}) does not point to a valid file`
          );
        }
      } else if (workoutPlanData.exerciseProgram) {
        console.log("No workout reference found but direct exerciseProgram exists, creating new file");
        
        workoutReference = uuidv4();
        console.log(`Generated new workout reference: ${workoutReference}`);

        const newFileRef = doc(db, "users", userEmail, "files", workoutReference);
        
        const exerciseProgramString = typeof workoutPlanData.exerciseProgram === 'string'
          ? workoutPlanData.exerciseProgram
          : JSON.stringify(workoutPlanData.exerciseProgram);

        await setDoc(newFileRef, {
          exerciseProgram: exerciseProgramString,
          fileType: "workout_program",
          createdAt: new Date().toISOString(),
          name: "Workout planning (auto-created)",
          category: "fitnessintelligentai"
        });

        await setDoc(workoutPlanRef, { WorkoutReference: workoutReference }, { merge: true });
        console.log(`Created new file for exercise program and updated workout plan reference`);
      } else {
        throw new Error(
          "Invalid workout reference in plan: WorkoutReference field is missing or empty and no alternatives were found"
        );
      }
    }

    console.log(`Using workout reference: ${workoutReference}`);

    const fileRef = doc(db, "users", userEmail, "files", workoutReference);
    const fileSnap = await getDoc(fileRef);

    if (!fileSnap.exists()) {
      throw new Error(`Exercise program file not found with reference: ${workoutReference}`);
    }

    const data = fileSnap.data();
    console.log(`File data keys: ${Object.keys(data || {})}`);

    let exerciseProgramString: string | null = null;

    if (data?.exerciseProgram) {
      if (typeof data.exerciseProgram === 'string') {
        exerciseProgramString = data.exerciseProgram;
      } else if (typeof data.exerciseProgram === 'object') {
        exerciseProgramString = JSON.stringify(data.exerciseProgram);
      }
    }

    if (!exerciseProgramString) {
      if (data?.workout && typeof data.workout === 'string') {
        exerciseProgramString = data.workout;
      } else if (data?.program && typeof data.program === 'string') {
        exerciseProgramString = data.program;
      } else {
        throw new Error(`Invalid exercise program data format for reference: ${workoutReference}`);
      }
    }

    console.log(`Exercise program string (first 100 chars): ${exerciseProgramString.substring(0, 100)}...`);

    const exerciseProgram = safeParseJSON(exerciseProgramString);
    if (!exerciseProgram) {
      throw new Error(`Failed to parse exercise program for reference: ${workoutReference}`);
    }

    console.log(`Successfully parsed exercise program: "${exerciseProgram.title}" with ${exerciseProgram.days.length} days`);

    const workoutReferenceId = workoutReference || uuidv4();

    const processedDays = await Promise.all(
      exerciseProgram.days.map(async (dayData, index) => {
        try {
          console.log(`Processing day ${index + 1}: ${dayData.name || 'Unnamed Day'}`);

          const warmUpExercises: WarmUpExercise[] = dayData.warm_up
            ? dayData.warm_up.map((item) => {
                try {
                  if (typeof item === "string") {
                    return {
                      exercise: sanitizeString(item),
                      libraryId: "", // Default to empty if not provided
                    };
                  } else if (typeof item === "object" && item !== null) {
                    return {
                      exercise: sanitizeString(item.exercise || ""),
                      duration: item.duration ? sanitizeNumber(item.duration) : undefined,
                      details: item.details ? sanitizeString(item.details) : undefined,
                      libraryId: item.libraryId ? sanitizeString(item.libraryId) : "",
                    };
                  } else {
                    console.warn(`Invalid warm_up item in day ${dayData.day || index}: ${JSON.stringify(item)}`);
                    return {
                      exercise: "Light Stretching",
                      libraryId: "",
                    };
                  }
                } catch (exerciseError) {
                  console.error(`Error processing warm-up exercise:`, exerciseError);
                  return {
                    exercise: "Light Stretching",
                    libraryId: "",
                  };
                }
              })
            : [];

          const resistanceExercises: ResistanceTrainingExercise[] =
            dayData.resistance_training
              ? dayData.resistance_training.map((rt) => {
                  try {
                    return {
                      exercise: sanitizeString(rt.exercise),
                      sets: rt.sets || 0,
                      reps: rt.reps || 0,
                      notes: rt.notes ? sanitizeString(rt.notes) : "",
                      libraryId: rt.libraryId ? sanitizeString(rt.libraryId) : "",
                    };
                  } catch (exerciseError) {
                    console.error(`Error processing resistance exercise:`, exerciseError);
                    return {
                      exercise: "Recovery Exercise",
                      sets: 1,
                      reps: 10,
                      notes: "Error in original exercise data",
                      libraryId: "",
                    };
                  }
                })
              : [];

          const cardioExercises: CardioTrainingExercise[] = dayData.cardio_training
            ? dayData.cardio_training.map((ct) => {
                try {
                  return {
                    exercise: sanitizeString(ct.exercise),
                    duration: typeof ct.duration === "number" ? ct.duration : sanitizeNumber(ct.duration),
                    notes: ct.notes ? sanitizeString(ct.notes) : "",
                    libraryId: ct.libraryId ? sanitizeString(ct.libraryId) : "",
                  };
                } catch (exerciseError) {
                  console.error(`Error processing cardio exercise:`, exerciseError);
                  return {
                    exercise: "Light Walking",
                    duration: 10,
                    notes: "Error in original exercise data",
                    libraryId: "",
                  };
                }
              })
            : [];

          const coolDownExercises: CoolDownExercise[] = dayData.cool_down
            ? dayData.cool_down.map((item) => {
                try {
                  if (typeof item === "string") {
                    return {
                      exercise: sanitizeString(item),
                      libraryId: "",
                    };
                  } else if (typeof item === "object" && item !== null) {
                    return {
                      exercise: sanitizeString(item.exercise || ""),
                      duration: item.duration ? sanitizeNumber(item.duration) : undefined,
                      details: item.details ? sanitizeString(item.details) : undefined,
                      libraryId: item.libraryId ? sanitizeString(item.libraryId) : "",
                    };
                  } else {
                    console.warn(`Invalid cool_down item in day ${dayData.day || index}: ${JSON.stringify(item)}`);
                    return {
                      exercise: "Light Stretching",
                      libraryId: "",
                    };
                  }
                } catch (exerciseError) {
                  console.error(`Error processing cool-down exercise:`, exerciseError);
                  return {
                    exercise: "Light Stretching",
                    libraryId: "",
                  };
                }
              })
            : [];

          const dayNumber = typeof dayData.day === "number"
            ? dayData.day
            : (typeof dayData.day === "string" && dayData.day.trim() !== ""
              ? parseInt(sanitizeString(dayData.day))
              : index + 1);

          return {
            day: dayNumber,
            name: sanitizeString(dayData.name || `Day ${dayNumber}`),
            duration: typeof dayData.duration === "number" ? dayData.duration : 0,
            warm_up: warmUpExercises,
            resistance_training: resistanceExercises,
            cardio_training: cardioExercises,
            cool_down: coolDownExercises,
            progressive_overload: dayData.progressive_overload || {
              increase_reps: "",
              increase_sets: "",
              notes: "",
            },
            rest_and_recovery: dayData.rest_and_recovery || {
              stretching: "Light stretching as needed",
              foam_rolling: "",
              sleep: "Prioritize good sleep",
            },
            modifications: dayData.modifications || {
              lower_back: "Avoid exercises that strain lower back",
              broken_toe: "Avoid high-impact exercises"
            },
          };
        } catch (dayError) {
          console.error(`Error processing day ${index + 1}:`, dayError);
          return {
            day: index + 1,
            name: `Day ${index + 1} (Error Recovery)`,
            duration: 30,
            warm_up: [{
              exercise: "Light Stretching",
              libraryId: "",
            }],
            resistance_training: [{
              exercise: "Recovery Exercise",
              sets: 1,
              reps: 10,
              notes: "Error recovery mode - please regenerate workout",
              libraryId: "",
            }],
            cardio_training: [{
              exercise: "Light Walking",
              duration: 10,
              notes: "Error recovery mode - please regenerate workout",
              libraryId: "",
            }],
            cool_down: [{
              exercise: "Light Stretching",
              libraryId: "",
            }],
            progressive_overload: {
              increase_reps: "",
              increase_sets: "",
              notes: "Error occurred during schedule generation",
            },
            rest_and_recovery: {
              stretching: "Light stretching as needed",
              foam_rolling: "",
              sleep: "Prioritize good sleep",
            },
            modifications: {
              lower_back: "Avoid exercises that strain lower back",
              broken_toe: "Avoid high-impact exercises"
            },
          };
        }
      })
    );

    const optimizedSchedule: OptimizedSchedule = {
      title: sanitizeString(exerciseProgram.title || "Workout Program"),
      days: processedDays,
      lastUpdated: new Date().toISOString(),
    };

    const scheduleRef = doc(
      db,
      "IF_users",
      userEmail,
      "Profile",
      "kick_starter"
    );

    await setDoc(scheduleRef, {
      scheduleId: uuidv4(),
      schedule: optimizedSchedule,
      workoutReferenceId: workoutReferenceId,
      workoutReference,
      title: optimizedSchedule.title,
      lastUpdated: optimizedSchedule.lastUpdated,
    });

    console.log(
      `Optimized workout schedule successfully stored in Firestore for user: ${userEmail}`
    );

    console.log(`Starting vector processing for workout plan...`);
    try {
      const vectorResult = await processWorkoutPlanForVectorSearch(
        db,
        userEmail,
        "kick_starter"
      );

      console.log(`Vector processing completed with result:`, JSON.stringify(vectorResult, null, 2));

      await setDoc(
        scheduleRef,
        {
          vectorProcessing: {
            status: vectorResult.success ? "completed" : "failed",
            processedAt: new Date().toISOString(),
            chunkCount: vectorResult.chunkCount || 0,
            error: vectorResult.error || null
          }
        },
        { merge: true }
      );
    } catch (vectorError) {
      console.error(`Error processing vectors for kick_starter plan:`, vectorError);

      await setDoc(
        scheduleRef,
        {
          vectorProcessing: {
            status: "failed",
            processedAt: new Date().toISOString(),
            error: vectorError instanceof Error ? vectorError.message : "Unknown vector processing error"
          }
        },
        { merge: true }
      );
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error("Error in schedule generation:", error);

      try {
        const errorRef = doc(db, "IF_users", userEmail, "Profile", "errors");
        await setDoc(errorRef, {
          source: "kick_starter_generation",
          timestamp: new Date().toISOString(),
          message: error.message,
          stack: error.stack,
          documentId
        }, { merge: true });
      } catch (logError) {
        console.error("Failed to log error to Firestore:", logError);
      }

      throw new Error(`Failed to generate schedule: ${error.message}`);
    } else {
      console.error("Unknown error in schedule generation:", error);
      throw new Error("Failed to generate schedule due to an unknown error.");
    }
  }
}

/**
 * ============================
 * Convenience function that combines schedule generation with vector processing
 * ============================
 */

export async function generateKickStarterWithVectors(
  db: Firestore,
  userEmail: string,
  documentId: string
): Promise<Record<string, unknown>> {
  try {
    await generateAndStoreKickstarterSchedule(db, userEmail, documentId);

    return {
      success: true,
      generation: {
        success: true,
        message: "Kick starter plan generated and processed for vectors successfully"
      },
      vectorProcessing: {
        status: "completed",
        processedAt: new Date().toISOString()
      }
    };
  } catch (error: unknown) {
    console.error(`Error in generateKickStarterWithVectors for user ${userEmail}:`, error);

    if (error instanceof Error) {
      return {
        success: false,
        error: error.message,
        stack: error.stack
      };
    } else {
      return {
        success: false,
        error: String(error),
        stack: "No stack trace available"
      };
    }
  }
}

/**
 * ============================
 * Usage Note
 * ============================
 *
 * This module exports the functions:
 * - 'generateAndStoreKickstarterSchedule': For generating a kick starter schedule with integrated vector processing
 * - 'generateKickStarterWithVectors': A convenience wrapper that aligns with the other vector generation functions
 *
 * Import them as follows:
 *
 *   import { generateAndStoreKickstarterSchedule, generateKickStarterWithVectors } from "./generate_KickerStarter_Schedule";
 *
 * Main generation with vectors:
 *   await generateAndStoreKickstarterSchedule(db, userEmail, documentId);
 *
 * Or use the convenience function that returns a result object:
 *   const result = await generateKickStarterWithVectors(db, userEmail, documentId);
 *   console.log(result.success ? "Success!" : `Error: ${result.error}`);
 */