import Link from 'next/link'
import { Target, Users, Zap } from 'lucide-react'
import { SiteHeader } from '@/components/site-header'

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <SiteHeader />

      <main className="flex-grow">
        <section className="bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 py-20">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Your AI-Powered <span className="text-blue-600">Fitness Journey</span>
            </h1>
            <p className="text-xl mb-8 text-gray-600 max-w-2xl mx-auto">
              Personalized workout plans, expert guidance, and a supportive community to help you achieve your fitness goals.
            </p>
            <Link href="/auth/signin" className="px-8 py-3 bg-blue-600 text-white rounded-md text-lg font-semibold hover:bg-blue-700 transition duration-300">
              Start Your Journey
            </Link>
          </div>
        </section>

        <section className="py-20">
          <div className="container mx-auto px-4">
            <div className="grid md:grid-cols-3 gap-8 ">
              {[
                { icon: Zap, title: "AI Workout Plans", description: "Personalized routines tailored to your goals and fitness level" },
                { icon: Target, title: "Goal Tracking", description: "Track your progress and stay motivated with detailed analytics" },
                { icon: Users, title: "Community", description: "Connect with like-minded fitness enthusiasts and share your journey" }
              ].map((feature, index) => (
                <div key={index} className="text-center">
                  <feature.icon className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
      </main>

      <footer className="bg-gray-100  py-8">
        <div className="container mx-auto px-4 text-center text-gray-600">
          <p>&copy; 2025 IntelligentFitness. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}

