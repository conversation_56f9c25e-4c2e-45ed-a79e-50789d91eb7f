"use client"

// Type alias for AudioContext constructor
type AudioContextConstructor = typeof AudioContext

export class AudioService {
  /**
   * Sets up audio processing for WebRTC
   * @returns An object containing the media stream and audio context
   */
  static async setupAudioProcessing(peerConnection: RTCPeerConnection): Promise<{
    stream: MediaStream;
    audioContext: AudioContext;
  }> {
    console.log("[AudioService] Setting up audio processing")
    try {
      const AudioContextClass: AudioContextConstructor | undefined =
        window.AudioContext ||
        ("webkitAudioContext" in window
          ? (window as unknown as { webkitAudioContext: AudioContextConstructor }).webkitAudioContext
          : undefined)

      if (!AudioContextClass) {
        throw new Error("AudioContext not supported in this browser")
      }

      // Create audio context that will be returned and used for cleanup
      const audioContext = new AudioContextClass()
      console.log("[AudioService] AudioContext created")

      console.log("[AudioService] Requesting microphone access")
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          channelCount: 1,
          sampleRate: 24000,
        },
      })

      console.log("[AudioService] Microphone access granted")
      const tracks = stream.getAudioTracks()

      if (tracks.length === 0) {
        throw new Error("No audio tracks found in microphone stream")
      }

      console.log(`[AudioService] Adding ${tracks.length} audio tracks to peer connection`)
      tracks.forEach((track) => {
        track.enabled = false
        if (peerConnection) {
          try {
            peerConnection.addTrack(track, stream)
          } catch (error) {
            console.error("[AudioService] Error adding audio track:", error)
            throw new Error("Failed to configure audio")
          }
        } else {
          throw new Error("Peer connection not initialized")
        }
      })
      console.log("[AudioService] Audio tracks added successfully")

      // Return both the stream and audio context
      return { stream, audioContext }
    } catch (err) {
      console.error("[AudioService] Audio setup error:", err)
      let errorMessage = "Microphone access error"

      if (err instanceof DOMException) {
        if (err.name === "NotAllowedError" || err.name === "PermissionDeniedError") {
          errorMessage = "Microphone access denied. Please allow microphone access in your browser settings."
        } else if (err.name === "NotFoundError" || err.name === "DevicesNotFoundError") {
          errorMessage = "No microphone detected. Please connect a microphone and try again."
        } else if (err.name === "NotReadableError" || err.name === "TrackStartError") {
          errorMessage = "Microphone is in use by another application. Please close other applications and try again."
        } else {
          errorMessage = `Microphone error: ${err.message}`
        }
      } else if (err instanceof Error) {
        errorMessage = err.message
      }

      console.error("[AudioService] Audio setup failed:", errorMessage)
      throw new Error(errorMessage)
    }
  }

  /**
   * Toggles the microphone state
   */
  static toggleMicrophone(mediaStream: MediaStream | null, newState: boolean): boolean {
    console.log("[AudioService] Toggle microphone requested, new state:", newState)
    if (!mediaStream) {
      console.warn("[AudioService] Cannot toggle microphone - stream not available")
      return false
    }

    try {
      console.log(`[AudioService] Setting microphone state to: ${newState ? "active" : "muted"}`)

      mediaStream.getAudioTracks().forEach((track) => {
        track.enabled = newState
        console.log(`[AudioService] Track ${track.id} enabled: ${track.enabled}`)
      })

      return newState
    } catch (micError) {
      console.error("[AudioService] Error toggling microphone:", micError)
      return false
    }
  }

  /**
   * Cleans up audio resources
   */
  static cleanupAudio(
    mediaStream: MediaStream | null,
    audioContext: AudioContext | null,
    audioElement: HTMLAudioElement | null
  ): void {
    if (mediaStream) {
      try {
        console.log("[AudioService] Stopping media tracks")
        mediaStream.getTracks().forEach((track) => {
          track.stop()
          console.log(`[AudioService] Track ${track.id} stopped`)
        })
      } catch (trackError) {
        console.error("[AudioService] Error stopping media tracks:", trackError)
      }
    }

    if (audioContext) {
      if (audioContext.state !== "closed") {
        console.log("[AudioService] Closing audio context")
        audioContext.close().catch((contextError) => {
          console.error("[AudioService] Error closing audio context:", contextError)
        })
      }
    }

    if (audioElement) {
      try {
        audioElement.pause()
        audioElement.srcObject = null
      } catch (audioError) {
        console.error("[AudioService] Error cleaning up audio element:", audioError)
      }
    }
  }
}
