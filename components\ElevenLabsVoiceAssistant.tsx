// components/ElevenLabsVoiceAssistant.tsx
"use client";

import React, { useState, useEffect, useRef } from "react";
import { Mic, MicOff, Volume2, VolumeX, X, RefreshCw } from "lucide-react";
import { auth } from "@/components/firebase/config";
import { useConversation } from "@11labs/react";

interface ElevenLabsVoiceAssistantProps {
  isOpen: boolean;
  onClose: () => void;
}

const ElevenLabsVoiceAssistant: React.FC<ElevenLabsVoiceAssistantProps> = ({ isOpen, onClose }) => {
  // Core state management
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [isListening, setIsListening] = useState<boolean>(false);
  const [isConversationActive, setIsConversationActive] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [isClosing, setIsClosing] = useState<boolean>(false);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [lastUserMessage, setLastUserMessage] = useState<string>("");
  
  // Reference to track active conversation state
  const conversationActive = useRef<boolean>(false);
  
  // Initialize ElevenLabs conversation hook
  const conversation = useConversation({
    onConnect: () => {
      console.log("Connected to ElevenLabs voice service");
      conversationActive.current = true;
      setIsConversationActive(true);
    },
    onDisconnect: () => {
      console.log("Disconnected from ElevenLabs voice service");
      conversationActive.current = false;
      setIsConversationActive(false);
      setIsListening(false);
    },
    onMessage: async (message: string | { message: string }) => {
      // Handle voice input from user (speech-to-text)
      if (typeof message === "string" && message.trim().length > 0) {
        console.log("User voice input:", message);
        setLastUserMessage(message);
      }
      // Handle response events from the voice assistant
      else if (message && typeof message === "object" && 'message' in message) {
        console.log("AI response event:", message.message);
      }
    },
  });

  // Extract status from the conversation hook
  const { status, isSpeaking } = conversation;

  // Check authentication state
  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      setIsAuthenticated(!!user);
    });
    return () => unsubscribe();
  }, []);

  // Check microphone permissions
  useEffect(() => {
    const checkMicrophonePermission = async () => {
      try {
        // Check if the browser supports mediaDevices
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error("Media devices not supported in this browser");
        }
        await navigator.mediaDevices.getUserMedia({ audio: true });
        setHasPermission(true);
        setErrorMessage("");
      } catch (error) {
        console.error("Error accessing microphone:", error);
        setHasPermission(false);
        setErrorMessage(
          "Microphone access is required. Please grant permission in your browser settings."
        );
      }
    };

    checkMicrophonePermission();

    // Add voice wave animation styles
    const styleElement = document.createElement("style");
    styleElement.innerHTML = `
      @keyframes voiceWave {
        0% {
          transform: scale(0.8);
          opacity: 1;
        }
        100% {
          transform: scale(2);
          opacity: 0;
        }
      }
    `;
    document.head.appendChild(styleElement);

    // Cleanup
    return () => {
      if (isConversationActive) {
        handleEndConversation();
      }
      
      // Remove dynamically added styles
      document.head.removeChild(styleElement);
    };
  }, [isConversationActive]);

  // Start the voice conversation
  const handleStartConversation = async () => {
    try {
      setIsListening(true);
      setErrorMessage("");

      console.log("Starting ElevenLabs voice session");
      
      const agentId = process.env.NEXT_PUBLIC_ELEVENLABS_AGENT_ID || "6swpg2ysNXE14FVa8VmD";
      
      // Start the ElevenLabs session
      await conversation.startSession({ agentId });
      
      console.log("Voice session started successfully");
    } catch (error) {
      console.error("Error starting conversation:", error);
      setErrorMessage("Failed to start voice assistant. Please try again.");
      setIsListening(false);
      setIsConversationActive(false);
    }
  };

  // End the voice conversation
  const handleEndConversation = async () => {
    try {
      console.log("Ending ElevenLabs voice session");
      await conversation.endSession();
      setIsConversationActive(false);
      setIsListening(false);
      setErrorMessage("");
      setLastUserMessage("");
    } catch (error) {
      console.error("Error ending conversation:", error);
      setErrorMessage("Failed to end voice session. Please try again.");
      
      // Force update UI state even if API call fails
      setIsConversationActive(false);
      setIsListening(false);
    }
  };

  // Toggle mute state
  const toggleMute = async () => {
    try {
      await conversation.setVolume({ volume: isMuted ? 1 : 0 });
      setIsMuted(!isMuted);
      setErrorMessage("");
    } catch (error) {
      console.error("Error changing volume:", error);
      setErrorMessage("Failed to change volume. Please try again.");
    }
  };

  // Retry microphone permission
  const handleRetryPermission = async () => {
    setErrorMessage("");
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      setHasPermission(true);
      setErrorMessage("");
    } catch (error) {
      console.error("Error retrying microphone permission:", error);
      setHasPermission(false);
      setErrorMessage(
        "Microphone access is required. Please grant permission in your browser settings."
      );
    }
  };

  // Handle closing the voice assistant
  const handleClose = () => {
    if (isConversationActive) {
      const confirmClose = window.confirm(
        "You have an active conversation. Are you sure you want to close the voice assistant?"
      );
      if (!confirmClose) return;
    }

    setIsClosing(true);
    setTimeout(() => {
      if (isListening) {
        handleEndConversation();
      }
      onClose();
      setIsClosing(false);
    }, 300);
  };

  if (!isAuthenticated || !isOpen) {
    return null;
  }

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 transition-all duration-300 ${
        isOpen && !isClosing ? "opacity-100 translate-y-0" : "opacity-0 translate-y-5"
      }`}
    >
      <div className="bg-slate-800 rounded-xl shadow-lg p-6 flex flex-col items-center w-full max-w-md border border-purple-600/20">
        <div className="w-full flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-white">Voice Assistant</h3>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-white"
            aria-label="Close voice assistant"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Voice status indicator */}
        <div className="relative mb-8">
          <div
            className={`w-24 h-24 rounded-full flex items-center justify-center transition-colors ${
              isListening ? "bg-purple-600" : "bg-slate-700"
            }`}
          >
            <Mic
              className={`h-12 w-12 ${isListening ? "text-white" : "text-gray-400"} ${
                isSpeaking ? "animate-pulse" : ""
              }`}
            />
          </div>

          {/* Animated ring when listening */}
          {isListening && (
            <div className="absolute inset-0">
              <div
                className="absolute w-full h-full rounded-full bg-purple-500/30 animate-[voiceWave_2s_infinite]"
              ></div>
              <div
                className="absolute w-full h-full rounded-full bg-purple-500/30 animate-[voiceWave_2s_infinite_0.5s]"
              ></div>
            </div>
          )}
        </div>

        {/* Status text */}
        <div
          className="text-base mb-6 h-6 text-center"
          aria-live="polite"
          role="status"
        >
          {!hasPermission ? (
            <span className="text-red-500">Microphone access required</span>
          ) : status === "connecting" ? (
            <span className="text-yellow-500">Connecting...</span>
          ) : isSpeaking ? (
            <span className="text-green-500">Speaking...</span>
          ) : isListening ? (
            <span className="text-blue-500">Listening...</span>
          ) : (
            <span className="text-gray-400">Ready to listen</span>
          )}
        </div>
        
        {/* Last message preview (for user feedback) */}
        {lastUserMessage && (
          <div className="w-full mb-4 px-2">
            <div className="text-xs text-gray-400 mb-1">You said:</div>
            <div className="text-sm bg-slate-700 p-2 rounded-lg text-gray-200">
              {lastUserMessage.length > 60 
                ? `${lastUserMessage.substring(0, 60)}...` 
                : lastUserMessage}
            </div>
          </div>
        )}

        {/* Error message and retry button */}
        {errorMessage && (
          <div className="text-sm text-red-500 mb-6 text-center flex flex-col items-center gap-2">
            <span>{errorMessage}</span>
            {hasPermission === false && (
              <button
                onClick={handleRetryPermission}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-colors"
                aria-label="Retry microphone permission"
              >
                <RefreshCw className="h-4 w-4" />
                Retry
              </button>
            )}
          </div>
        )}

        {/* Controls */}
        <div className="flex items-center space-x-6">
          {/* Mute button */}
          <button
            onClick={toggleMute}
            disabled={status !== "connected"}
            className={`p-3 rounded-full ${
              status === "connected"
                ? "bg-slate-700 hover:bg-slate-600"
                : "bg-slate-800 text-gray-600 cursor-not-allowed"
            }`}
            aria-label={isMuted ? "Unmute voice assistant" : "Mute voice assistant"}
          >
            {isMuted ? (
              <VolumeX className="h-6 w-6 text-gray-400" />
            ) : (
              <Volume2 className="h-6 w-6 text-gray-400" />
            )}
          </button>

          {/* Start/Stop button */}
          {isListening ? (
            <button
              onClick={handleEndConversation}
              className="flex items-center justify-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-full transition-colors"
              aria-label="Stop voice assistant"
            >
              <MicOff className="h-6 w-6" />
              <span>Stop</span>
            </button>
          ) : (
            <button
              onClick={handleStartConversation}
              className="flex items-center justify-center gap-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-full transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!hasPermission}
              aria-label="Start voice assistant"
            >
              <Mic className="h-6 w-6" />
              <span>Start</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ElevenLabsVoiceAssistant;