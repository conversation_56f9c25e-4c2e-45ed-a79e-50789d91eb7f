// lib/generateDocs.ts

import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { Document } from "langchain/document";
import { FirestoreStore } from "@/lib/FirestoreStore";
import { adminDb } from "@/components/firebase-admin";

/**
 * Generates document chunks from a PDF, stores them in Firestore, and returns the chunks.
 * @param userId - The user's unique identifier (e.g., email).
 * @param docId - The unique document ID.
 * @returns An array of Document instances with metadata.
 */
export async function generateDocs(userId: string, docId: string): Promise<Document[]> {
  if (!userId) {
    throw new Error("User not signed in");
  }

  // Fetch the document metadata from Firestore
  const firebaseRef = await adminDb
    .collection("foodKhare_users")
    .doc(userId)
    .collection("files")
    .doc(docId)
    .get();

  const documentData = firebaseRef.data();
  const downloadUrl = documentData?.downloadUrl;
  const documentName = documentData?.name;
  const category = documentData?.category;

  if (!downloadUrl) {
    throw new Error(`Cannot locate the file from the URL: ${downloadUrl}`);
  }

  console.log(`Download URL fetched successfully: ${downloadUrl}`);

  // Fetch the PDF file
  const response = await fetch(downloadUrl);
  if (!response.ok) {
    throw new Error(`Failed to fetch PDF from URL: ${downloadUrl}`);
  }
  const data = await response.blob();

  const pdfLoader = new PDFLoader(data);
  const pdfDocument = await pdfLoader.load();

  // Split the document into smaller chunks
  const splitter = new RecursiveCharacterTextSplitter({
    chunkSize: 1000, // Adjust as needed
    chunkOverlap: 100, // Adjust as needed
  });

  const splitPdfDocs = await splitter.splitDocuments(pdfDocument);
  console.log(`Splitting document into ${splitPdfDocs.length} parts...`);

  // Assign metadata to each chunk
  const docsWithMetadata = splitPdfDocs.map((doc, index) => {
    const chunkId = `${docId}_${index + 1}`; // e.g., "docId_1", "docId_2", etc.
    const metadata = {
      doc_id: docId, // Base docId (without chunk index)
      chunk_id: chunkId, // Full chunk ID
      page_number: doc.metadata?.pageNumber || index + 1,
      category: category || "Uncategorized",
      document_title: documentName || "Untitled",
    };

    console.log(`Document chunk ${index + 1}:`);
    console.log(`Page Content:`, doc.pageContent);
    console.log(`Metadata:`, metadata);



    return new Document({
      pageContent: doc.pageContent,
      metadata: metadata,
    });
  });

// Define the Firestore collection path using the userId and 'byteStoreCollection'
const byteCollection = `foodKhare_users/${userId}/byteStoreCollection`;

// Initialize FirestoreStore with the collection path
const byteStore = new FirestoreStore({ collectionPath: byteCollection });

// Prepare the entries array to store document chunks with their chunk IDs as the key
const entries: [string, Document][] = docsWithMetadata.map((doc) => [
  doc.metadata.chunk_id,  // Using 'chunk_id' as the key
  doc,                    // The document itself as the value
]);

  await byteStore.mset(entries);
  console.log("All document chunks have been stored in Firestore byteStore.");

  return docsWithMetadata;
}
