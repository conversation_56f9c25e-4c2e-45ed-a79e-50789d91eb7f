"use client"

import { getFirestore, collection, doc, getDoc, getDocs, query, orderBy, limit } from "firebase/firestore"

// Define interfaces for the data structures
interface Exercise {
  name?: string
  sets?: number
  reps?: string | number
  [key: string]: unknown
}

interface DayWorkout {
  workoutType?: string
  exercises?: Exercise[]
  [key: string]: unknown
}

type DayData = Exercise[] | DayWorkout | string | unknown

interface FirebaseTimestamp {
  toDate(): Date
  seconds: number
  nanoseconds: number
}

interface MessageData {
  role: string
  text: string
  timestamp: FirebaseTimestamp | string | number | Date
}

export class DataService {
  /**
   * Checks if a value is a Firebase timestamp
   */
  static isFirebaseTimestamp(value: unknown): value is FirebaseTimestamp {
    return (
      typeof value === "object" &&
      value !== null &&
      "toDate" in value &&
      typeof (value as FirebaseTimestamp).toDate === "function"
    )
  }

  /**
   * Fetches the user's assessment summary from Firebase
   */
  static async fetchAssessmentSummary(userEmail: string): Promise<string> {
    console.log("[DataService] Fetching assessment summary for:", userEmail)
    if (!userEmail) {
      console.warn("[DataService] User email is required to fetch assessment summary")
      return "No assessment data available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const assessmentDocRef = doc(db, "IF_users", userEmail, "Profile", "assessment")
      const assessmentDoc = await getDoc(assessmentDocRef)

      if (assessmentDoc.exists()) {
        const data = assessmentDoc.data()
        if (data.summary && typeof data.summary === "string") {
          console.log("[DataService] Assessment summary found, length:", data.summary.length)
          return data.summary
        } else {
          console.warn("[DataService] Assessment summary field missing or not a string")
          return "Assessment data format error: summary field not found"
        }
      } else {
        console.warn("[DataService] Assessment document not found")
        return "No assessment data found for this user"
      }
    } catch (error) {
      console.error("[DataService] Error fetching assessment summary:", error)
      return "Error retrieving assessment data"
    }
  }

  /**
   * Fetches the user's tracking data from Firebase
   */
  static async fetchTrackingData(userEmail: string): Promise<string> {
    console.log("[DataService] Fetching tracking data for:", userEmail)
    if (!userEmail) {
      console.warn("[DataService] User email is required to fetch tracking data")
      return "No tracking data available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const recordsRef = collection(db, "IF_users", userEmail, "tracking_records")
      const q = query(recordsRef, orderBy("completedDate", "desc"))
      const querySnapshot = await getDocs(q)

      console.log(`[DataService] Retrieved ${querySnapshot.size} tracking records`)

      if (querySnapshot.empty) {
        console.warn("[DataService] No tracking records found")
        return "User has not tracked any exercises yet"
      }

      let formattedTracking = "Exercise Tracking Summary:\n"
      formattedTracking += `\nTotal tracking records: ${querySnapshot.size}\n`

      if (querySnapshot.size > 100) {
        formattedTracking += "\nNote: Showing a summary of tracking data due to the large number of records.\n"
      }

      interface TrackingRecord {
        id: string
        exerciseId: string
        exerciseName: string
        libraryId: string
        workoutReference: string
        completedDate: { toDate: () => Date }
        difficultyLevel: number
        energyLevel: number
        timeToComplete: number
        sets?: number
        reps?: number
        weight?: number
        notes?: string
        day: number
        category: string
        [key: string]: unknown
      }

      const recordsByDay = new Map<number, TrackingRecord[]>()
      querySnapshot.forEach((doc) => {
        const record = doc.data() as TrackingRecord
        const day = record.day
        if (!recordsByDay.has(day)) {
          recordsByDay.set(day, [])
        }
        const dayRecords = recordsByDay.get(day)
        if (dayRecords) {
          const recordWithId = { ...record }
          recordWithId.id = doc.id
          dayRecords.push(recordWithId)
        }
      })

      recordsByDay.forEach((records, day) => {
        formattedTracking += `\nDay ${day}:\n`
        formattedTracking += `  Total exercises tracked: ${records.length}\n`

        const MAX_RECORDS_PER_DAY = 8
        let recordsToProcess = records
        if (records.length > MAX_RECORDS_PER_DAY) {
          formattedTracking += `  Note: Showing the ${MAX_RECORDS_PER_DAY} most recent records for this day.\n`
          recordsToProcess = [...records]
            .sort((a, b) => {
              const dateA = a.completedDate?.toDate?.() || new Date(0)
              const dateB = b.completedDate?.toDate?.() || new Date(0)
              return dateB.getTime() - dateA.getTime()
            })
            .slice(0, MAX_RECORDS_PER_DAY)
        }

        const recordsByCategory = new Map<string, TrackingRecord[]>()
        recordsToProcess.forEach((record: TrackingRecord) => {
          if (!recordsByCategory.has(record.category)) {
            recordsByCategory.set(record.category, [])
          }
          const categoryRecords = recordsByCategory.get(record.category)
          if (categoryRecords) {
            categoryRecords.push(record)
          }
        })

        const categoryNames: Record<string, string> = {
          "warm_up": "Warm-up",
          "resistance_training": "Resistance Training",
          "cardio_training": "Cardio Training",
          "cool_down": "Cool Down"
        }

        recordsByCategory.forEach((categoryRecords, category) => {
          const categoryName = categoryNames[category as keyof typeof categoryNames] || category
          formattedTracking += `  ${categoryName}:\n`

          categoryRecords.forEach((record: TrackingRecord) => {
            formattedTracking += `    - ${record.exerciseName}\n`
            formattedTracking += `      Difficulty: ${record.difficultyLevel}/5, Energy: ${record.energyLevel}/5\n`
            formattedTracking += `      Time: ${record.timeToComplete} minutes\n`

            if (category === "resistance_training") {
              if (record.sets) formattedTracking += `      Sets: ${record.sets}\n`
              if (record.reps) formattedTracking += `      Reps: ${record.reps}\n`
              if (record.weight) formattedTracking += `      Weight: ${record.weight} kg\n`
            }

            if (record.notes) formattedTracking += `      Notes: ${record.notes}\n`
          })
        })
      })

      formattedTracking += "\n\nSummary Statistics:\n"

      let totalDifficulty = 0
      let totalEnergy = 0
      let totalRecords = 0
      let resistanceTrainingCount = 0
      let cardioTrainingCount = 0
      let warmUpCount = 0
      let coolDownCount = 0
      let mostRecentDate: Date | null = null

      querySnapshot.forEach((doc) => {
        const record = doc.data() as TrackingRecord
        totalDifficulty += record.difficultyLevel || 0
        totalEnergy += record.energyLevel || 0
        totalRecords++

        if (record.category === "resistance_training") resistanceTrainingCount++
        else if (record.category === "cardio_training") cardioTrainingCount++
        else if (record.category === "warm_up") warmUpCount++
        else if (record.category === "cool_down") coolDownCount++

        if (record.completedDate) {
          let recordDate: Date
          if (typeof record.completedDate.toDate === "function") {
            recordDate = record.completedDate.toDate()
          } else if (record.completedDate instanceof Date) {
            recordDate = record.completedDate
          } else {
            try {
              recordDate = new Date(record.completedDate as unknown as string | number)
            } catch {
              recordDate = new Date()
            }
          }
          if (!mostRecentDate || recordDate > mostRecentDate) {
            mostRecentDate = recordDate
          }
        }
      })

      if (totalRecords > 0) {
        formattedTracking += `Average difficulty level: ${(totalDifficulty / totalRecords).toFixed(1)}/5\n`
        formattedTracking += `Average energy level: ${(totalEnergy / totalRecords).toFixed(1)}/5\n`
        formattedTracking += `\nExercise breakdown:\n`
        formattedTracking += `- Resistance training exercises: ${resistanceTrainingCount}\n`
        formattedTracking += `- Cardio training exercises: ${cardioTrainingCount}\n`
        formattedTracking += `- Warm-up exercises: ${warmUpCount}\n`
        formattedTracking += `- Cool-down exercises: ${coolDownCount}\n`

        if (mostRecentDate) {
          try {
            formattedTracking += `\nMost recent tracking: ${(mostRecentDate as Date).toLocaleDateString()}\n`
          } catch {
            formattedTracking += `\nMost recent tracking: ${(mostRecentDate as Date).toString()}\n`
          }
        }
      }

      console.log("[DataService] Tracking data formatted, length:", formattedTracking.length)
      return formattedTracking
    } catch (error) {
      console.error("[DataService] Error fetching tracking data:", error)
      return "Error retrieving tracking data"
    }
  }

  /**
   * Fetches the user's nutrition data from Firebase
   */
  static async fetchNutritionData(userEmail: string): Promise<string> {
    console.log("[DataService] Fetching nutrition data for:", userEmail)
    if (!userEmail) {
      console.warn("[DataService] User email is required to fetch nutrition data")
      return "No nutrition data available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const docRef = doc(db, "IF_users", userEmail, "Profile", "workoutplan")
      const docSnap = await getDoc(docRef)

      if (!docSnap.exists()) {
        console.warn("[DataService] Workout plan document does not exist")
        return "No nutrition advice available"
      }

      const data = docSnap.data()
      const nutritionAdvice = data?.nutritionAdvice

      if (!nutritionAdvice) {
        console.warn("[DataService] Nutrition advice field is missing")
        return "No nutrition advice available"
      }

      const sections: string[] = nutritionAdvice.split(";").map((section: string) => section.trim())
      let formattedAdvice = "Nutrition Advice:\n\n"

      sections.forEach((section: string) => {
        if (!section) return
        const colonIndex = section.indexOf(": ")
        if (colonIndex !== -1) {
          let key = section.substring(0, colonIndex).trim()
          let value = section.substring(colonIndex + 2).trim()
          if (key.startsWith('"')) key = key.substring(1)
          if (key.endsWith('"')) key = key.substring(0, key.length - 1)
          if (value.startsWith('"')) value = value.substring(1)
          if (value.endsWith('"')) value = value.substring(0, value.length - 1)
          const capitalizedKey = key.charAt(0).toUpperCase() + key.slice(1)
          formattedAdvice += `${capitalizedKey}: ${value}\n\n`
        } else {
          formattedAdvice += `${section}\n\n`
        }
      })

      console.log("[DataService] Nutrition data formatted, length:", formattedAdvice.length)
      return formattedAdvice
    } catch (error) {
      console.error("[DataService] Error fetching nutrition data:", error)
      return "Error retrieving nutrition data"
    }
  }

  /**
   * Fetches the user's workout plan from Firebase
   */
  static async fetchUserWorkoutPlan(userEmail: string): Promise<string> {
    console.log("[DataService] Fetching workout plan for:", userEmail)
    if (!userEmail) {
      console.warn("[DataService] User email is required to fetch workout plan")
      return "No workout plan available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const collections = ["schedule", "shortTerm", "longTerm", "kick_starter"]

      for (const collectionName of collections) {
        console.log("[DataService] Checking collection:", collectionName)
        const docRef = doc(db, "IF_users", userEmail, "Profile", collectionName)
        const docSnap = await getDoc(docRef)

        if (docSnap.exists() && Object.keys(docSnap.data()).length > 0) {
          const data = docSnap.data()
          console.log(`[DataService] Found data in ${collectionName} collection`)

          let formattedPlan = `User's ${collectionName} workout plan:\n`

          if (collectionName === "schedule") {
            const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]
            days.forEach((day) => {
              if (data[day.toLowerCase()]) {
                formattedPlan += `\n${day}:\n`
                const dayData = data[day.toLowerCase()] as DayData
                if (Array.isArray(dayData)) {
                  dayData.forEach((exercise: Exercise, index: number) => {
                    formattedPlan += `  ${index + 1}. ${exercise.name || "Exercise"}`
                    if (exercise.sets) formattedPlan += ` - ${exercise.sets} sets`
                    if (exercise.reps) formattedPlan += ` x ${exercise.reps} reps`
                    formattedPlan += "\n"
                  })
                } else if (typeof dayData === "object" && dayData !== null) {
                  const workoutData = dayData as DayWorkout
                  formattedPlan += `  Workout: ${workoutData.workoutType || "Not specified"}\n`
                  if (workoutData.exercises && Array.isArray(workoutData.exercises)) {
                    workoutData.exercises.forEach((exercise: Exercise, index: number) => {
                      formattedPlan += `  ${index + 1}. ${exercise.name || "Exercise"}`
                      if (exercise.sets) formattedPlan += ` - ${exercise.sets} sets`
                      if (exercise.reps) formattedPlan += ` x ${exercise.reps} reps`
                      formattedPlan += "\n"
                    })
                  }
                } else {
                  formattedPlan += `  ${dayData}\n`
                }
              } else {
                formattedPlan += `\n${day}: Rest day or no workout assigned\n`
              }
            })
          } else {
            Object.entries(data).forEach(([key, value]) => {
              if (key.startsWith("_") || key === "createdAt" || key === "updatedAt") return
              if (typeof value === "object" && value !== null) {
                formattedPlan += `\n${key}:\n`
                Object.entries(value as Record<string, unknown>).forEach(([subKey, subValue]) => {
                  formattedPlan += `  ${subKey}: ${JSON.stringify(subValue)}\n`
                })
              } else {
                formattedPlan += `${key}: ${value}\n`
              }
            })
          }

          console.log("[DataService] Workout plan formatted, length:", formattedPlan.length)
          return formattedPlan
        }
      }

      console.warn("[DataService] No workout plan found in any collection")
      return "No workout plan found in any collection"
    } catch (error) {
      console.error("[DataService] Error fetching workout plan:", error)
      return "Error retrieving workout plan data"
    }
  }

  /**
   * Fetches the user's latest tracking summary from Firebase
   */
  static async fetchTrackingSummary(userEmail: string): Promise<string> {
    console.log("[DataService] ===== FETCHING TRACKING SUMMARY =====")
    console.log("[DataService] Fetching tracking summary for:", userEmail)
    if (!userEmail) {
      console.warn("[DataService] User email is required to fetch tracking summary")
      return "No tracking summary available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const summariesRef = collection(db, "IF_users", userEmail, "tracking_summaries")
      const q = query(summariesRef, orderBy("date-time", "desc"), limit(1))
      const querySnapshot = await getDocs(q)

      if (querySnapshot.empty) {
        console.warn("[DataService] No tracking summaries found")
        return "No tracking summary available yet. Please track some exercises first."
      }

      const summaryDoc = querySnapshot.docs[0]
      const summaryData = summaryDoc.data()

      if (!summaryData.summary || typeof summaryData.summary !== "string") {
        console.warn("[DataService] Summary field missing or not a string")
        return "Tracking summary format error: summary field not found"
      }

      const dateTime = summaryData["date-time"]
      let formattedDate = "Unknown date"

      if (dateTime) {
        try {
          if (typeof dateTime.toDate === "function") {
            formattedDate = dateTime.toDate().toLocaleDateString()
          } else if (dateTime instanceof Date) {
            formattedDate = dateTime.toLocaleDateString()
          } else {
            formattedDate = new Date(dateTime).toLocaleDateString()
          }
        } catch (e) {
          console.warn("[DataService] Error formatting date:", e)
        }
      }

      const workType = summaryData.work_type || "Unknown"
      const workoutReference = summaryData.workoutReference || "Unknown"

      let formattedSummary = `Tracking Summary (${formattedDate}):\n\n`
      formattedSummary += `Workout Type: ${workType}\n`
      formattedSummary += `Workout Reference: ${workoutReference}\n\n`
      formattedSummary += summaryData.summary

      console.log("[DataService] Tracking summary found, length:", formattedSummary.length)
      console.log("[DataService] ===== TRACKING SUMMARY FETCH COMPLETE =====")
      return formattedSummary
    } catch (error) {
      console.error("[DataService] Error fetching tracking summary:", error)
      console.log("[DataService] ===== TRACKING SUMMARY FETCH ERROR =====")
      return "Error retrieving tracking summary"
    }
  }

  /**
   * Fetches the user's chat history from Firebase
   */
  static async fetchChatHistory(userEmail: string, messageLimit = 15): Promise<string> {
    console.log("[DataService] Fetching chat history for:", userEmail)
    if (!userEmail) {
      console.warn("[DataService] User email is required to fetch chat history")
      return "No chat history available (missing user ID)"
    }

    try {
      const db = getFirestore()
      const chatsRef = collection(db, `users/${userEmail}/chats`)
      const chatsQuery = query(chatsRef, orderBy("createdAt", "desc"), limit(5))
      const chatsSnapshot = await getDocs(chatsQuery)

      if (chatsSnapshot.empty) {
        console.log("[DataService] No chats found")
        return "No previous chat history found"
      }

      let selectedChatId = chatsSnapshot.docs[0].id
      let maxMessages = 0

      for (const chatDoc of chatsSnapshot.docs) {
        const chatId = chatDoc.id
        const messagesRef = collection(db, `users/${userEmail}/chats/${chatId}/messages`)
        const messagesSnapshot = await getDocs(messagesRef)

        if (messagesSnapshot.size > maxMessages) {
          maxMessages = messagesSnapshot.size
          selectedChatId = chatId
        }
      }

      console.log(`[DataService] Selected chat ${selectedChatId} with ${maxMessages} messages`)

      const messagesRef = collection(db, `users/${userEmail}/chats/${selectedChatId}/messages`)
      const messagesQuery = query(messagesRef, orderBy("createdAt", "desc"), limit(messageLimit))
      const messagesSnapshot = await getDocs(messagesQuery)

      if (messagesSnapshot.empty) {
        console.log("[DataService] No messages found in selected chat")
        return "No messages found in chat history"
      }

      const messages = messagesSnapshot.docs
        .map((doc) => {
          const data = doc.data()
          return {
            role: data.role,
            text: data.text || "",
            timestamp: data.createdAt,
          } as MessageData
        })
        .sort((a, b) => {
          const timeA = DataService.isFirebaseTimestamp(a.timestamp)
            ? a.timestamp.toDate().getTime()
            : new Date(a.timestamp).getTime()
          const timeB = DataService.isFirebaseTimestamp(b.timestamp)
            ? b.timestamp.toDate().getTime()
            : new Date(b.timestamp).getTime()
          return timeA - timeB
        })

      console.log(`[DataService] Retrieved ${messages.length} messages`)

      let formattedHistory = "Previous conversation highlights:\n"
      messages.forEach((msg) => {
        const role = msg.role === "user" ? "User" : "Assistant"
        formattedHistory += `${role}: ${msg.text}\n`
      })

      console.log("[DataService] Chat history formatted, length:", formattedHistory.length)
      return formattedHistory
    } catch (error) {
      console.error("[DataService] Error fetching chat history:", error)
      return "Error retrieving chat history"
    }
  }
}
