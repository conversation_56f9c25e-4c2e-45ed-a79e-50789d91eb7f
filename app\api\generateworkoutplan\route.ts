import { type NextRequest, NextResponse } from "next/server";
import { doc, setDoc } from "firebase/firestore";
import { db } from "@/components/firebase/config";
import { v4 as uuidv4 } from "uuid";

import { supervisorAgent } from "@/lib/agents";
import type { AgentResponses } from "@/lib/agents";
import { AgentResponsesSchema } from "@/lib/agents"; // Import Zod schema

console.log("API: Dependencies loaded successfully");

/**
 * Formats and validates exercise program data for storage
 * @param exerciseProgram - The exercise program data to format
 * @returns A properly formatted JSON string
 */
function formatExerciseProgramForStorage(exerciseProgram: unknown): string {
  if (!exerciseProgram) {
    console.error("API: Exercise program is missing or null");
    // Return a minimal valid structure to prevent parsing errors
    return JSON.stringify({
      title: "Default Workout Program",
      days: [
        {
          day: 1,
          name: "Recovery Day",
          duration: 30
        }
      ]
    });
  }

  try {
    // If it's already a string, validate it can be parsed as JSON with required fields
    if (typeof exerciseProgram === 'string') {
      try {
        const parsed = JSON.parse(exerciseProgram);
        // Validate the structure has required fields
        if (!parsed.title || !Array.isArray(parsed.days)) {
          console.warn("API: Exercise program string does not contain required fields, applying fixes");
          
          // Add missing fields
          const fixed = {
            title: parsed.title || "Workout Program",
            days: Array.isArray(parsed.days) ? parsed.days : []
          };
          
          // If days is empty, add a default day
          if (fixed.days.length === 0) {
            fixed.days.push({
              day: 1,
              name: "Day 1",
              duration: 30
            });
          }
          
          return JSON.stringify(fixed);
        }
        // Already valid JSON string with required fields
        return exerciseProgram;
      } catch (e) {
        console.error("API: Exercise program string is not valid JSON:", e);
        // It's a string but not JSON, wrap it in a valid structure
        return JSON.stringify({
          title: "Workout Program",
          days: [
            {
              day: 1,
              name: "Day 1",
              duration: 30,
              notes: "Error parsing original program - please regenerate"
            }
          ]
        });
      }
    } 
    
    // It's an object, ensure it has the required fields
    if (typeof exerciseProgram === 'object' && exerciseProgram !== null) {
      // TypeScript safety: convert unknown to Record<string, unknown>
      const programObj = exerciseProgram as Record<string, unknown>;
      
      // Direct object with title and days
      if (
        programObj.title && 
        Array.isArray(programObj.days)
      ) {
        return JSON.stringify(programObj);
      }
      
      // Nested structure - look for days array
      if (
        programObj.workoutPlan && 
        typeof programObj.workoutPlan === 'object' && 
        programObj.workoutPlan !== null
      ) {
        const workoutPlan = programObj.workoutPlan as Record<string, unknown>;
        
        if (workoutPlan.days && Array.isArray(workoutPlan.days)) {
          return JSON.stringify({
            title: workoutPlan.title || "Workout Program",
            days: workoutPlan.days
          });
        }
      }
      
      // No valid structure found, create a default
      console.warn("API: Exercise program object does not contain required fields, creating default structure");
      return JSON.stringify({
        title: "Default Workout Program",
        days: [
          {
            day: 1,
            name: "Default Workout Day",
            duration: 30,
            notes: "Default program created - original data format was invalid"
          }
        ]
      });
    }
    
    // Unexpected type, return default structure
    console.error(`API: Exercise program has unexpected type: ${typeof exerciseProgram}`);
    return JSON.stringify({
      title: "Default Workout Program",
      days: [
        {
          day: 1,
          name: "Default Workout Day",
          duration: 30,
          notes: "Default program created - original data type was invalid"
        }
      ]
    });
  } catch (error) {
    console.error("API: Error formatting exercise program:", error);
    // Return a minimal valid structure in case of any errors
    return JSON.stringify({
      title: "Error Recovery Workout",
      days: [
        {
          day: 1,
          name: "Recovery Day",
          duration: 30,
          notes: "Error occurred during program generation"
        }
      ]
    });
  }
}

/**
 * POST handler for /api/generateworkoutplan
 * Generates a workout plan using the supervisorAgent and stores the result in Firestore
 */
export async function POST(request: NextRequest) {
  console.log("API: Starting /api/generateworkoutplan POST request");
  try {
    // Parse the incoming JSON request
    const body = await request.json();
    const { userEmail, profileText } = body;
    console.log("API: Request received:", {
      userEmail,
      profileTextLength: profileText?.length,
    });

    // Validate that both userEmail and profileText exist.
    if (!userEmail || !profileText) {
      console.log("API: Missing userEmail or profileText. Returning 400.");
      return NextResponse.json(
        { error: "Missing 'userEmail' or 'profileText'" },
        { status: 400 }
      );
    }

    // Call the supervisor agent to obtain the complete workout plan data.
    console.log("API: Calling supervisorAgent to generate full workout plan");
    const agentResponses: AgentResponses = await supervisorAgent(userEmail, profileText,db);
    console.log("API: Supervisor agent returned");

    // Validate the agent responses using Zod
    try {
        AgentResponsesSchema.parse(agentResponses);
        console.log("API: Agent responses validated successfully.");
    } catch (validationError) {
        console.error("API: Agent response validation error:", validationError);
        return NextResponse.json(
          { error: "Invalid agent response format", details: validationError },
          { status: 500 }
        );
    }

    // Generate a unique document ID for the workout file
    const workoutFileId = uuidv4();
    console.log(`API: Generated workout file ID: ${workoutFileId}`);

    // Format the exercise program properly for storage
    const formattedExerciseProgram = formatExerciseProgramForStorage(
      agentResponses.workoutPlan.exerciseProgram
    );
    console.log(`API: Formatted exercise program for storage (first 100 chars): ${formattedExerciseProgram.substring(0, 100)}...`);

    // First, store the exercise program as a separate file in the files collection
    try {
      const fileRef = doc(db, `users/${userEmail}/files`, workoutFileId);
      await setDoc(fileRef, {
        fileType: "workout_program",
        category: "fitnessintelligentai",
        userProfileSummary: agentResponses.userProfileSummary || "",
        formTechniqueGuidance: agentResponses.workoutPlan.formTechniqueGuidance || "",
        longTermPlanning: agentResponses.workoutPlan.longTermPlan || "",
        exerciseProgram: agentResponses.workoutPlan.exerciseProgram || "",
        shortTermPlanning: agentResponses.workoutPlan.shortTermPlan || "",
        nutritionAdvice: agentResponses.nutritionAdvice || "",
        coachInstructions: agentResponses.coachInstructions || "",
        createdAt: new Date().toISOString(),
        name: "Workout planning",
        namespace: workoutFileId
      });
      console.log(`API: Exercise program saved as file with ID: ${workoutFileId}`);
    } catch (fileError) {
      console.error("API: Error saving exercise program file:", fileError);
      throw new Error(`Failed to save exercise program file: ${fileError instanceof Error ? fileError.message : "Unknown error"}`);
    }

    // Store all parts of the workout plan in Firestore with the WorkoutReference field
    const workoutPlanRef = doc(db, `IF_users/${userEmail}/Profile`, "workoutplan");
    const workoutPlanSummary = {
      // Store the reference to the exercise program file
      WorkoutReference: workoutFileId, // This is the critical field
      
      // Include other fields as before
      shortTermPlan: agentResponses.workoutPlan.shortTermPlan || "",
      longTermPlan: agentResponses.workoutPlan.longTermPlan || "",
      formTechniqueGuidance: agentResponses.workoutPlan.formTechniqueGuidance || "",
      exerciseProgram: agentResponses.workoutPlan.exerciseProgram || "",
      coachInstructions: agentResponses.coachInstructions || "",
      nutritionAdvice: agentResponses.nutritionAdvice || "",
      userProfileSummary: agentResponses.userProfileSummary || "",
      
      // Add metadata
      lastUpdated: new Date().toISOString()
    };

    console.log("API: Saving workout plan summary to Firestore with WorkoutReference");
    await setDoc(workoutPlanRef, workoutPlanSummary, { merge: true });
    console.log("API: Workout plan summary saved successfully");

    // Add the workoutFileId to the response so the frontend can use it if needed
    const enhancedResponse = {
      ...agentResponses,
      workoutFileId // Include the reference ID in the response
    };

    // Return the full consolidated response back to the client.
    console.log("API: Returning successful response");
    return NextResponse.json(enhancedResponse);

  } catch (error: unknown) {
    console.error("API: Error in generateworkoutplan handler:", error);
    const message = error instanceof Error ? error.message : "Unexpected error";
    return NextResponse.json(
      { error: message || "Failed to generate workout plan" },
      { status: 500 }
    );
  }
}