// utils/unit-conversions.ts

// --- Existing conversion functions (keep these) ---
export const convertHeight = (value: number, fromUnit: string, toUnit: string): number => {
  // Convert to cm first
  let cm: number;
  switch (fromUnit) {
    case 'ft':
      cm = value * 30.48;
      break;
    case 'm':
      cm = value * 100;
      break;
    default:
      cm = value;
  }

  // Convert from cm to target unit
  switch (toUnit) {
    case 'ft':
      return Number((cm / 30.48).toFixed(2));
    case 'm':
      return Number((cm / 100).toFixed(2));
    default:
      return Number(cm.toFixed(2));
  }
}

export const convertWeight = (value: number, fromUnit: string, toUnit: string): number => {
  // Convert to kg first
  let kg: number;
  switch (fromUnit) {
    case 'lbs':
      kg = value * 0.453592;
      break;
    case 'st':
      kg = value * 6.35029;
      break;
    default:
      kg = value;
  }

  // Convert from kg to target unit
  switch (toUnit) {
    case 'lbs':
      return Number((kg / 0.453592).toFixed(2));
    case 'st':
      return Number((kg / 6.35029).toFixed(2));
    default:
      return Number(kg.toFixed(2));
  }
}

// --- Add these new functions for string parsing and formatting ---

export const parseHeight = (heightString: string): number => {
const feetMatch = heightString.match(/(\d+)'/)
const inchesMatch = heightString.match(/(\d+)"?/) // Allow optional "

let feet = 0
let inches = 0

if (feetMatch) {
  feet = parseInt(feetMatch[1], 10)
}
if (inchesMatch) {
  inches = parseInt(inchesMatch[1], 10)
}

return feet * 12 + inches
}

export const formatHeight = (totalInches: number): string => {
const feet = Math.floor(totalInches / 12)
const inches = totalInches % 12
return `${feet}'${inches}"`
}