// Updated page.tsx for Exercise Library
"use client";

import { useState, useEffect } from "react";
import { collection, getDocs, query, orderBy } from "firebase/firestore";
import { getAuth, onAuthStateChanged, User } from "firebase/auth"; // Added Firebase Auth
import { db } from "@/components/firebase/config";
import { Search, Filter, X, ChevronDown, Loader2, Video, Upload, PlayCircle } from "lucide-react";
// Removed WorkoutPlannerLayout import as we're using DashboardLayout via layout.tsx
import { ExerciseModal } from "@/components/exercise-modal";
import { ExerciseVideoListModal } from "@/components/ExerciseVideoListModal";
import { uploadExerciseLibrary } from "@/utils/upload-exercises";
import { processSingleExerciseForVectorSearch } from "@/utils/libraryVectorUpload";
import { updateExerciseVideos } from "@/utils/update-exercise-videos";
import { resetAllExerciseVideos } from "@/utils/reset-exercise-videos";
import ExerciseCard from "@/components/exercise-card";

interface Exercise {
  name: string;
  focusArea: string;
  description: string;
  level: string;
  muscleGroups: string[];
  equipment: string;
  variations: string[];
  image?: string;
  video?: string;
  urlVideo?: string;
  videoId?: string;
  videoThumbnail?: string;
  id: string;
}

interface FilterOptions {
  focusArea: string;
  level: string;
  equipment: string;
  muscleGroup: string;
  hasVideo: boolean; // New filter option for video availability
}

interface UploadProgress {
  uploaded: number;
  total: number;
  skipped: number;
  failed: number;
}

interface PineconeUploadProgress {
  processed: number;
  total: number;
  failed: number;
}

interface VideoUpdateProgress {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
  skipped: number;
}

interface VideoResetProgress {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
}

export default function ExerciseLibrary() {
  const [exercises, setExercises] = useState<Exercise[]>([]);
  const [filteredExercises, setFilteredExercises] = useState<Exercise[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<FilterOptions>({
    focusArea: "",
    level: "",
    equipment: "",
    muscleGroup: "",
    hasVideo: false,
  });
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [isUploadingToPinecone, setIsUploadingToPinecone] = useState(false);
  const [pineconeUploadProgress, setPineconeUploadProgress] = useState<PineconeUploadProgress | null>(null);
  const [isUpdatingVideos, setIsUpdatingVideos] = useState(false);
  const [videoUpdateProgress, setVideoUpdateProgress] = useState<VideoUpdateProgress | null>(null);
  const [isResettingVideos, setIsResettingVideos] = useState(false);
  const [videoResetProgress, setVideoResetProgress] = useState<VideoResetProgress | null>(null);
  const [totalExercises, setTotalExercises] = useState(0);
  const [exercisesWithVideos, setExercisesWithVideos] = useState(0);
  const [focusAreas, setFocusAreas] = useState<string[]>([]);

  // Authentication state
  // We only need the email, so we prefix the unused variable with underscore
  const [_currentUser, _setCurrentUser] = useState<User | null>(null);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  // Admin email from environment variable with fallback
  const adminEmail = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>';

  // Derived state for admin access check
  const isAdmin = userEmail === adminEmail;

  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(null);
  const [showVideoListModal, setShowVideoListModal] = useState(false);
  const [displayCount, setDisplayCount] = useState(10); // Number of exercises to display initially
  const [isLoading, setIsLoading] = useState(true);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  // Firebase Authentication listener
  useEffect(() => {
    const auth = getAuth();

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      _setCurrentUser(user); // Update the user object (not directly used)
      setUserEmail(user?.email || null); // We only need the email for admin check

      if (user) {
        console.log("User authenticated:", user.email);
      } else {
        console.log("No user authenticated");
      }
    });

    // Clean up the listener on component unmount
    return () => unsubscribe();
  }, []);

  // Force a refresh when the component mounts to ensure data is loaded
  const [key, setKey] = useState(0);

  // Function to fetch exercises
  const fetchExercises = async () => {
    console.log('Fetching exercises...');
    setIsLoading(true);
    setLoadingError(null);

    try {
      const exerciseCollectionRef = collection(db, "Fitness Library");
      const q = query(exerciseCollectionRef, orderBy("focusArea"), orderBy("name"));
      const querySnapshot = await getDocs(q);

      const exerciseData: Exercise[] = [];
      const uniqueFocusAreas = new Set<string>();
      let videoCount = 0;

      querySnapshot.forEach((doc) => {
        const exercise = { ...doc.data(), id: doc.id } as Exercise;
        exerciseData.push(exercise);
        uniqueFocusAreas.add(exercise.focusArea);

        // Count exercises with videos
        if (exercise.urlVideo || exercise.videoId) {
          videoCount++;
        }
      });

      console.log(`Loaded ${exerciseData.length} exercises`);
      setExercises(exerciseData);
      setFilteredExercises(exerciseData);
      setTotalExercises(exerciseData.length);
      setExercisesWithVideos(videoCount);
      setFocusAreas(Array.from(uniqueFocusAreas));
    } catch (error) {
      console.error("Error fetching exercises:", error);
      setLoadingError("Failed to load exercises. Please try refreshing the page.");
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch on mount
  useEffect(() => {
    fetchExercises();
  }, [key]);

  // Add a manual refresh function
  const handleManualRefresh = () => {
    setKey(prevKey => prevKey + 1);
  };

  // If page is blank, try to refresh automatically after a short delay
  useEffect(() => {
    if (exercises.length === 0 && !isLoading && !loadingError) {
      const timer = setTimeout(() => {
        console.log('Auto-refreshing due to empty data...');
        handleManualRefresh();
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [exercises.length, isLoading, loadingError]);

  // Handle filtering as a separate effect to avoid race conditions
  useEffect(() => {
    if (exercises.length === 0) return; // Don't run if no exercises yet

    let filtered = [...exercises];

    // Text search
    if (searchTerm) {
      filtered = filtered.filter(
        (exercise) =>
          exercise.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          exercise.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply filters
    Object.entries(selectedFilters).forEach(([key, value]) => {
      if (value) {
        if (key === "hasVideo") {
          filtered = filtered.filter((exercise) =>
            Boolean(exercise.urlVideo || exercise.videoId)
          );
        } else if (key === "muscleGroup") {
          filtered = filtered.filter((exercise) =>
            exercise.muscleGroups.includes(value as string)
          );
        } else if (key !== "hasVideo" && typeof value === "string") {
          filtered = filtered.filter((exercise) =>
            exercise[key as keyof Exercise] === value
          );
        }
      }
    });

    setFilteredExercises(filtered);
  }, [searchTerm, selectedFilters, exercises]);

  const handleUploadLibrary = async () => {
    setIsUploading(true);
    setUploadProgress({ uploaded: 0, total: 0, skipped: 0, failed: 0 });

    try {
      const result = await uploadExerciseLibrary((progress) => {
        setUploadProgress(progress);
      });

      if (result.success) {
        const exerciseCollectionRef = collection(db, "Fitness Library");
        const q = query(exerciseCollectionRef, orderBy("focusArea"), orderBy("name"));
        const querySnapshot = await getDocs(q);

        const exerciseData: Exercise[] = [];
        const uniqueFocusAreas = new Set<string>();
        let videoCount = 0;

        querySnapshot.forEach((doc) => {
          const exercise = { ...doc.data(), id: doc.id } as Exercise;
          exerciseData.push(exercise);
          uniqueFocusAreas.add(exercise.focusArea);

          // Count exercises with videos
          if (exercise.urlVideo || exercise.videoId) {
            videoCount++;
          }
        });

        setExercises(exerciseData);
        setFilteredExercises(exerciseData);
        setTotalExercises(exerciseData.length);
        setExercisesWithVideos(videoCount);
        setFocusAreas(Array.from(uniqueFocusAreas));
      } else {
        alert("Failed to upload exercises: " + result.message);
      }
    } catch (error) {
      console.error("Error uploading exercises:", error);
      alert("Failed to upload exercises");
    } finally {
      setIsUploading(false);
      setUploadProgress(null);
    }
  };

  const handleUploadToPinecone = async () => {
    setIsUploadingToPinecone(true);
    setPineconeUploadProgress({ processed: 0, total: exercises.length, failed: 0 });

    try {
      let processed = 0;
      let failed = 0;

      for (const exercise of exercises) {
        const result = await processSingleExerciseForVectorSearch(db, exercise.id);
        if (result.success) {
          processed++;
        } else {
          failed++;
          console.error(`Failed to process exercise ${exercise.id}: ${result.message}`);
        }
        setPineconeUploadProgress({ processed, total: exercises.length, failed });
      }

      if (failed === 0) {
        alert(`Successfully uploaded ${processed} exercises to Pinecone`);
      } else {
        alert(`Uploaded ${processed} exercises to Pinecone with ${failed} failures`);
      }
    } catch (error) {
      console.error("Error uploading to Pinecone:", error);
      alert("Failed to upload exercises to Pinecone");
    } finally {
      setIsUploadingToPinecone(false);
      setPineconeUploadProgress(null);
    }
  };

  const handleResetVideos = async () => {
    setIsResettingVideos(true);
    setVideoResetProgress({ total: 0, processed: 0, succeeded: 0, failed: 0 });

    try {
      // Get confirmation
      if (!confirm("WARNING: This will REMOVE ALL YouTube videos from exercises. This action cannot be undone. Continue?")) {
        setIsResettingVideos(false);
        return;
      }

      const result = await resetAllExerciseVideos(setVideoResetProgress, 20);

      if (result.success) {
        alert(result.message);

        // Refresh exercise list to show updated video status
        const exerciseCollectionRef = collection(db, "Fitness Library");
        const q = query(exerciseCollectionRef, orderBy("focusArea"), orderBy("name"));
        const querySnapshot = await getDocs(q);

        const exerciseData: Exercise[] = [];
        const uniqueFocusAreas = new Set<string>();
        let videoCount = 0;

        querySnapshot.forEach((doc) => {
          const exercise = { ...doc.data(), id: doc.id } as Exercise;
          exerciseData.push(exercise);
          uniqueFocusAreas.add(exercise.focusArea);

          // Count exercises with videos
          if (exercise.urlVideo || exercise.videoId) {
            videoCount++;
          }
        });

        setExercises(exerciseData);
        setFocusAreas(Array.from(uniqueFocusAreas));
        setTotalExercises(exerciseData.length);
        setExercisesWithVideos(videoCount);
      } else {
        alert(`Error: ${result.message}`);
      }
    } catch (error) {
      console.error("Error resetting videos:", error);
      alert(`Error resetting videos: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsResettingVideos(false);
      setVideoResetProgress(null);
    }
  };

  const handleUpdateVideos = async () => {
    setIsUpdatingVideos(true);
    setVideoUpdateProgress({ total: 0, processed: 0, succeeded: 0, failed: 0, skipped: 0 });

    try {
      // Get confirmation
      if (!confirm("This will search for and add YouTube videos to exercises. Continue?")) {
        setIsUpdatingVideos(false);
        return;
      }

      const result = await updateExerciseVideos(setVideoUpdateProgress, 5, false);

      if (result.success) {
        alert(result.message);

        // Refresh exercise list to show new videos
        const exerciseCollectionRef = collection(db, "Fitness Library");
        const q = query(exerciseCollectionRef, orderBy("focusArea"), orderBy("name"));
        const querySnapshot = await getDocs(q);

        const exerciseData: Exercise[] = [];
        const uniqueFocusAreas = new Set<string>();
        let videoCount = 0;

        querySnapshot.forEach((doc) => {
          const exercise = { ...doc.data(), id: doc.id } as Exercise;
          exerciseData.push(exercise);
          uniqueFocusAreas.add(exercise.focusArea);

          // Count exercises with videos
          if (exercise.urlVideo || exercise.videoId) {
            videoCount++;
          }
        });

        setExercises(exerciseData);
        setFilteredExercises(exerciseData);
        setTotalExercises(exerciseData.length);
        setExercisesWithVideos(videoCount);
      } else {
        alert("Failed to update videos: " + result.message);
      }
    } catch (error) {
      console.error("Error updating videos:", error);
      alert("Failed to update videos");
    } finally {
      setIsUpdatingVideos(false);
      setVideoUpdateProgress(null);
    }
  };

  // Loading spinner component
  const LoadingSpinner = () => (
    <div className="absolute inset-0 flex items-center justify-center bg-slate-900/90 z-50">
      <div className="text-center bg-slate-800 p-8 rounded-xl shadow-xl">
        <Loader2 className="w-16 h-16 animate-spin text-blue-400 mx-auto mb-6" />
        <p className="text-white font-medium text-xl mb-2">Loading Exercise Library</p>
        <p className="text-gray-400 mb-4">Please wait while we fetch the exercises...</p>
        <button
          onClick={handleManualRefresh}
          className="px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors mt-2"
        >
          Refresh Data
        </button>
      </div>
    </div>
  );

  // Error component
  const ErrorDisplay = () => (
    <div className="absolute inset-0 flex items-center justify-center bg-slate-900/90 z-50">
      <div className="bg-slate-800 p-8 rounded-xl shadow-xl max-w-md">
        <div className="text-red-400 mb-4 text-2xl font-bold">Error Loading Exercises</div>
        <p className="text-white mb-6">{loadingError}</p>
        <div className="flex gap-4">
          <button
            onClick={handleManualRefresh}
            className="px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors flex-1"
          >
            Retry
          </button>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-slate-600 text-white rounded-xl hover:bg-slate-700 transition-colors flex-1"
          >
            Hard Refresh
          </button>
        </div>
      </div>
    </div>
  );

  // Empty state component
  const EmptyState = () => (
    <div className="absolute inset-0 flex items-center justify-center bg-slate-900/90 z-50">
      <div className="text-center bg-slate-800 p-8 rounded-xl shadow-xl">
        <div className="w-16 h-16 bg-blue-900/50 rounded-full flex items-center justify-center mx-auto mb-6">
          <Search className="w-8 h-8 text-blue-400" />
        </div>
        <p className="text-white font-medium text-xl mb-2">No Exercises Found</p>
        <p className="text-gray-400 mb-4">We&apos;re having trouble loading the exercise library.</p>
        <button
          onClick={handleManualRefresh}
          className="px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors mt-2"
        >
          Refresh Data
        </button>
      </div>
    </div>
  );

  // Filter panel component
  const FilterPanel = () => (
    <div className={`mb-8 bg-slate-800/80 p-6 rounded-2xl transition-all duration-300 ${showFilters ? 'opacity-100' : 'opacity-0 hidden'}`}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-gray-300 mb-2">Level</label>
          <select
            value={selectedFilters.level}
            onChange={(e) => setSelectedFilters({ ...selectedFilters, level: e.target.value })}
            className="w-full bg-slate-700 text-white p-2 rounded-xl"
          >
            <option value="">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
        <div>
          <label className="block text-gray-300 mb-2">Equipment</label>
          <select
            value={selectedFilters.equipment}
            onChange={(e) => setSelectedFilters({ ...selectedFilters, equipment: e.target.value })}
            className="w-full bg-slate-700 text-white p-2 rounded-xl"
          >
            <option value="">All Equipment</option>
            <option value="barbell">Barbell</option>
            <option value="dumbbell">Dumbbell</option>
            <option value="kettlebell">Kettlebell</option>
            <option value="bodyweight">Bodyweight</option>
            <option value="machine">Machine</option>
            <option value="cable">Cable</option>
            <option value="resistance band">Resistance Band</option>
          </select>
        </div>
        <div>
          <label className="block text-gray-300 mb-2">Muscle Group</label>
          <select
            value={selectedFilters.muscleGroup}
            onChange={(e) => setSelectedFilters({ ...selectedFilters, muscleGroup: e.target.value })}
            className="w-full bg-slate-700 text-white p-2 rounded-xl"
          >
            <option value="">All Muscle Groups</option>
            <option value="chest">Chest</option>
            <option value="back">Back</option>
            <option value="shoulders">Shoulders</option>
            <option value="biceps">Biceps</option>
            <option value="triceps">Triceps</option>
            <option value="legs">Legs</option>
            <option value="abs">Abs</option>
            <option value="glutes">Glutes</option>
            <option value="calves">Calves</option>
          </select>
        </div>
      </div>
      <div className="mt-4 flex items-center">
        <label className="inline-flex items-center text-gray-300 cursor-pointer">
          <input
            type="checkbox"
            checked={selectedFilters.hasVideo}
            onChange={(e) => setSelectedFilters({ ...selectedFilters, hasVideo: e.target.checked })}
            className="mr-2 h-4 w-4"
          />
          <Video className="w-4 h-4 mr-2" />
          Only show exercises with videos
        </label>
        <button
          onClick={() => setSelectedFilters({
            focusArea: "",
            level: "",
            equipment: "",
            muscleGroup: "",
            hasVideo: false,
          })}
          className="ml-auto text-blue-400 hover:text-blue-300"
        >
          Clear Filters
        </button>
      </div>
    </div>
  );

  return (
    <>
        {/* Show loading spinner while loading */}
        {isLoading && <LoadingSpinner />}

        {/* Show error message if loading failed */}
        {loadingError && <ErrorDisplay />}

        {/* Show empty state if no exercises and not loading or error */}
        {!isLoading && !loadingError && exercises.length === 0 && <EmptyState />}

        <div className="container mx-auto px-4 py-8">
          {/* Header Section */}
          <div className="mb-8">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
              <div>
                <h1 className="text-4xl font-bold text-blue-400 mb-2">Exercise Library</h1>
                <p className="text-gray-300">Discover and learn new exercises for your workout routine</p>
                <div className="mt-2 flex flex-wrap gap-3 text-sm">
                  <p className="text-blue-400">
                    <span className="font-semibold">{filteredExercises.length}</span> / {totalExercises} exercises
                  </p>
                  <button
                    onClick={() => setShowVideoListModal(true)}
                    className="text-green-400 flex items-center hover:text-green-300 transition-colors"
                  >
                    <Video className="w-4 h-4 mr-1.5" />
                    <span>
                      <span className="font-semibold">{exercisesWithVideos}</span>{" "}
                      <span className="text-green-400/90">with videos</span>
                    </span>
                    <PlayCircle className="w-4 h-4 ml-1.5" />
                  </button>
                </div>
              </div>
              {/* Admin buttons - only visible for admin user */}
              {isAdmin && (
                <div className="flex flex-wrap gap-3">
                  <button
                    onClick={handleUploadLibrary}
                    disabled={isUploading || isUploadingToPinecone || isUpdatingVideos || isResettingVideos || isLoading}
                    className="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-xl transition-all duration-300 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Upload className="w-4 h-4" />
                    {isUploading ? "Uploading..." : "Upload Exercises"}
                  </button>
                  <button
                    onClick={handleUploadToPinecone}
                    disabled={isUploading || isUploadingToPinecone || isUpdatingVideos || isResettingVideos || isLoading}
                    className="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-xl transition-all duration-300 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUploadingToPinecone ? "Processing..." : "Index in Pinecone"}
                  </button>
                  <button
                    onClick={handleResetVideos}
                    disabled={isUploading || isUploadingToPinecone || isUpdatingVideos || isResettingVideos || isLoading}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-300 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <X className="w-4 h-4" />
                    {isResettingVideos ? "Resetting..." : "Reset Video URLs"}
                  </button>
                  <button
                    onClick={handleUpdateVideos}
                    disabled={isUploading || isUploadingToPinecone || isUpdatingVideos || isResettingVideos || isLoading}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl transition-all duration-300 flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Video className="w-4 h-4" />
                    {isUpdatingVideos ? "Searching..." : "Add YouTube Videos"}
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Search Bar and Focus Area Filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Search exercises..."
                className="w-full pl-12 pr-12 py-4 bg-slate-800/50 backdrop-blur-md border border-slate-700 rounded-2xl text-white placeholder-gray-400 focus:outline-none"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={isLoading}
              />
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm("")}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
            </div>
            <div className="relative">
              <select
                value={selectedFilters.focusArea}
                onChange={(e) => setSelectedFilters({ ...selectedFilters, focusArea: e.target.value })}
                className="appearance-none w-full md:w-48 pl-4 pr-12 py-4 bg-slate-800/50 backdrop-blur-md border border-slate-700 rounded-2xl text-white focus:outline-none"
                disabled={isLoading}
              >
                <option value="">All Focus Areas</option>
                {focusAreas.map((area) => (
                  <option key={area} value={area} className="text-gray-900">
                    {area}
                  </option>
                ))}
              </select>
              <ChevronDown className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 pointer-events-none" />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-xl transition-all duration-300 flex items-center gap-2"
              disabled={isLoading}
            >
              <Filter className="w-5 h-5" />
              {showFilters ? "Hide Filters" : "More Filters"}
            </button>
          </div>

          {/* Advanced filters panel */}
          {showFilters && <FilterPanel />}

          {/* Exercise Cards Grid - Only render when not loading */}
          {!isLoading && !loadingError && (
            <>
              {filteredExercises.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredExercises.slice(0, displayCount).map((exercise) => (
                    <ExerciseCard
                      key={exercise.id}
                      exercise={exercise}
                      onClick={() => setSelectedExercise(exercise)}
                    />
                  ))}
                </div>
              ) : (
                <div className="bg-slate-800/70 p-8 rounded-2xl text-center">
                  <p className="text-gray-300 text-lg">No exercises found matching your filters.</p>
                  <button
                    onClick={() => {
                      setSearchTerm("");
                      setSelectedFilters({
                        focusArea: "",
                        level: "",
                        equipment: "",
                        muscleGroup: "",
                        hasVideo: false,
                      });
                    }}
                    className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
                  >
                    Clear All Filters
                  </button>
                </div>
              )}

              {/* Load More Button */}
              <div className="mt-8 text-center">
                {displayCount < filteredExercises.length && (
                  <button
                    onClick={() => setDisplayCount((prev) => prev + 10)}
                    className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors"
                  >
                    Load More
                  </button>
                )}
              </div>
            </>
          )}
        </div>

      {/* Upload Progress Modal */}
      {isUploading && uploadProgress && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-lg p-6 w-96">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Uploading Exercises</h3>
              <button
                onClick={() => setIsUploading(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="mb-4">
              <div className="h-2 w-full bg-slate-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600 rounded-full transition-all duration-300"
                  style={{
                    width: `${((uploadProgress.uploaded + uploadProgress.skipped) / uploadProgress.total) * 100}%`
                  }}
                ></div>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-300">
                Uploaded: {uploadProgress.uploaded} exercises
              </p>
              <p className="text-sm text-gray-300">
                Skipped: {uploadProgress.skipped} existing exercises
              </p>
              {uploadProgress.failed > 0 && (
                <p className="text-sm text-red-400">
                  Failed: {uploadProgress.failed} exercises
                </p>
              )}
              <p className="text-sm font-medium text-gray-200">
                Progress: {uploadProgress.uploaded + uploadProgress.skipped} / {uploadProgress.total}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Pinecone Upload Progress Modal */}
      {isUploadingToPinecone && pineconeUploadProgress && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-lg p-6 w-96">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Uploading to Pinecone</h3>
              <button
                onClick={() => setIsUploadingToPinecone(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="mb-4">
              <div className="h-2 w-full bg-slate-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600 rounded-full transition-all duration-300"
                  style={{
                    width: `${(pineconeUploadProgress.processed / pineconeUploadProgress.total) * 100}%`
                  }}
                ></div>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-300">
                Processed: {pineconeUploadProgress.processed} exercises
              </p>
              {pineconeUploadProgress.failed > 0 && (
                <p className="text-sm text-red-400">
                  Failed: {pineconeUploadProgress.failed} exercises
                </p>
              )}
              <p className="text-sm font-medium text-gray-200">
                Progress: {pineconeUploadProgress.processed} / {pineconeUploadProgress.total}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Video Update Progress Modal */}
      {isUpdatingVideos && videoUpdateProgress && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-lg p-6 w-96">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Finding & Adding Videos</h3>
              <button
                onClick={() => setIsUpdatingVideos(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="mb-4">
              <div className="h-2 w-full bg-slate-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600 rounded-full transition-all duration-300"
                  style={{
                    width: `${(videoUpdateProgress.processed / videoUpdateProgress.total) * 100}%`
                  }}
                ></div>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-300">
                Found Videos: {videoUpdateProgress.succeeded} exercises
              </p>
              <p className="text-sm text-gray-300">
                Skipped: {videoUpdateProgress.skipped} (already had videos)
              </p>
              {videoUpdateProgress.failed > 0 && (
                <p className="text-sm text-red-400">
                  Failed: {videoUpdateProgress.failed} exercises
                </p>
              )}
              <p className="text-sm font-medium text-gray-200">
                Progress: {videoUpdateProgress.processed} / {videoUpdateProgress.total}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Video Reset Progress Modal */}
      {isResettingVideos && videoResetProgress && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-lg p-6 w-96">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-white">Resetting Exercise Videos</h3>
              <button
                onClick={() => setIsResettingVideos(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            <div className="mb-4">
              <div className="h-2 w-full bg-slate-700 rounded-full overflow-hidden">
                <div
                  className="h-full bg-blue-600 rounded-full transition-all duration-300"
                  style={{
                    width: `${(videoResetProgress.processed / videoResetProgress.total) * 100}%`
                  }}
                ></div>
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-300">
                Processed: {videoResetProgress.processed} exercises
              </p>
              <p className="text-sm text-gray-300">
                Succeeded: {videoResetProgress.succeeded} resets
              </p>
              {videoResetProgress.failed > 0 && (
                <p className="text-sm text-red-400">
                  Failed: {videoResetProgress.failed} exercises
                </p>
              )}
              <p className="text-sm font-medium text-gray-200">
                Progress: {videoResetProgress.processed} / {videoResetProgress.total}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Exercise Detail Modal */}
      <ExerciseModal
        exercise={selectedExercise}
        onClose={() => setSelectedExercise(null)}
      />

      {/* Exercise Video List Modal */}
      {showVideoListModal && (
        <ExerciseVideoListModal
          onClose={() => setShowVideoListModal(false)}
          onSelectExercise={(exercise) => {
            setSelectedExercise(exercise);
            setShowVideoListModal(false);
          }}
        />
      )}
    </>
  );
}