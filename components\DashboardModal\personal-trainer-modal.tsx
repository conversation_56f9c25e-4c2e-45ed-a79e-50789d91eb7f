"use client"

// 1. Core imports
import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>R<PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Clock, 
  CheckCircle, 
  Target, 
  Activity 
} from "lucide-react"

// 2. Type Definitions
interface CoachInstructions {
  instructions: string
  mistakes: string
  modifications: string
}

interface Exercise {
  coachInstructions: CoachInstructions
  completed: boolean
  exerciseName: string
  libraryId: string
  location: string
  specs: string
  weight: string
}

interface ExerciseMap {
  [key: string]: Exercise
}

interface DaySchedule {
  [key: string]: ExerciseMap
}

export interface PersonalTrainerSchedule {
  exercises: DaySchedule
  lastUpdated: string
  scheduleId: string
  workoutReference: string
  trainerNotes?: {
    general: string
    form: string
    progression: string
  }
  assessments?: {
    initial: {
      strength: string
      endurance: string
      flexibility: string
    }
    goals: string[]
  }
}

// 3. Component Props
interface PersonalTrainerModalProps {
  isOpen: boolean
  onClose: () => void
  data: PersonalTrainerSchedule | null
}

// 4. Main Component Implementation
export function PersonalTrainerModal({ isOpen, onClose, data }: PersonalTrainerModalProps) {
  // State Management
  const [activeTab, setActiveTab] = useState<"overview" | "schedule" | "assessment">("overview")
  const [selectedDay, setSelectedDay] = useState<string>("")

  // Initialize selected day
  useEffect(() => {
    if (data?.exercises && Object.keys(data.exercises).length > 0 && !selectedDay) {
      setSelectedDay(Object.keys(data.exercises)[0])
    }
  }, [data, selectedDay])

  // Early return if modal shouldn't be shown
  if (!isOpen || !data) return null

  // Sort days in proper order
  const days = Object.keys(data.exercises).sort((a, b) => {
    // Extract the first word (the actual day name) from the key (e.g., "Thursday" from "Thursday - Gym")
    const [dayA] = a.split(" - ")
    const [dayB] = b.split(" - ")

    const daysOrder = [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday"
    ]

    return daysOrder.indexOf(dayA) - daysOrder.indexOf(dayB)
  })

  // Tab Rendering Functions
  const renderOverviewTab = () => {
    const trainerNotes = data.trainerNotes || {
      general: "No general notes available.",
      form: "No form guidelines available.",
      progression: "No progression plan available.",
    }

    return (
      <div className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Target className="w-5 h-5 mr-2 text-purple-500" />
            Trainer Notes
          </h3>
          <p className="text-gray-400">{trainerNotes.general}</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Activity className="w-5 h-5 mr-2 text-purple-500" />
            Form Guidelines
          </h3>
          <p className="text-gray-400">{trainerNotes.form}</p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Dumbbell className="w-5 h-5 mr-2 text-purple-500" />
            Progression Plan
          </h3>
          <p className="text-gray-400">{trainerNotes.progression}</p>
        </motion.div>
      </div>
    )
  }

  const renderScheduleTab = () => {
    if (!selectedDay || !data.exercises[selectedDay]) {
      return <p className="text-gray-400">No schedule available for selected day.</p>
    }

    const dayExercises = data.exercises[selectedDay]

    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-purple-400 capitalize">{selectedDay}</h2>
        <div className="grid gap-4">
          {Object.entries(dayExercises).map(([exerciseKey, exercise]) => (
            <motion.div
              key={`${exercise.libraryId}-${exerciseKey}`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="group bg-black/20 backdrop-blur-sm rounded-lg p-4 hover:bg-white/5 transition-all duration-200 border border-white/5 hover:border-purple-500/20 hover:shadow-lg hover:shadow-purple-500/10"
            >
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-white flex items-center group-hover:text-purple-400 transition-colors duration-200">
                    <Dumbbell className="w-5 h-5 mr-2 text-green-500" />
                    {exercise.exerciseName}
                  </h3>
                  <div className="mt-2 grid grid-cols-2 gap-4 text-sm text-gray-400">
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-2 text-purple-500/70" />
                      {exercise.specs}
                    </div>
                    {exercise.weight && (
                      <div className="flex items-center">
                        <ChevronRight className="w-4 h-4 mr-2 text-purple-500/70" />
                        Weight: {exercise.weight}
                      </div>
                    )}
                  </div>
                  <div className="mt-4 space-y-2">
                    <p className="text-sm text-gray-400">
                      <span className="font-medium text-purple-400">Instructions:</span>{" "}
                      {exercise.coachInstructions.instructions}
                    </p>
                    <p className="text-sm text-gray-400">
                      <span className="font-medium text-red-600">Common Mistakes:</span>{" "}
                      {exercise.coachInstructions.mistakes}
                    </p>
                    {exercise.coachInstructions.modifications && (
                      <p className="text-sm text-gray-400">
                        <span className="font-medium text-purple-400">Modifications:</span>{" "}
                        {exercise.coachInstructions.modifications}
                      </p>
                    )}
                  </div>
                </div>
                <div
                  className={`p-2 rounded-full transition-all duration-200 ${
                    exercise.completed
                      ? "bg-green-500/10 text-green-400 group-hover:bg-green-500/20"
                      : "bg-white/5 text-gray-400 group-hover:bg-white/10"
                  }`}
                >
                  {exercise.completed ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <Clock className="w-5 h-5" />
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    )
  }

  const renderAssessmentTab = () => {
    const assessment = data.assessments?.initial || {
      strength: "No strength assessment available.",
      endurance: "No endurance assessment available.",
      flexibility: "No flexibility assessment available.",
    }
    const goals = data.assessments?.goals || []

    return (
      <div className="space-y-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Activity className="w-5 h-5 mr-2 text-purple-500" />
            Initial Assessment
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="font-semibold text-purple-400">Strength</h4>
              <p className="text-gray-400">{assessment.strength}</p>
            </div>
            <div>
              <h4 className="font-semibold text-purple-400">Endurance</h4>
              <p className="text-gray-400">{assessment.endurance}</p>
            </div>
            <div>
              <h4 className="font-semibold text-purple-400">Flexibility</h4>
              <p className="text-gray-400">{assessment.flexibility}</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Target className="w-5 h-5 mr-2 text-purple-500" />
            Training Goals
          </h3>
          {goals.length > 0 ? (
            <ul className="list-disc list-inside text-gray-400 space-y-2">
              {goals.map((goal, index) => (
                <motion.li
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="hover:text-white transition-colors duration-200"
                >
                  {goal}
                </motion.li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-400">No training goals set.</p>
          )}
        </motion.div>
      </div>
    )
  }

  // Main Render
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="relative w-full max-w-6xl h-[85vh] bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 rounded-2xl shadow-2xl overflow-hidden border border-purple-500/20"
      >
        <div className="absolute inset-0 bg-gradient-to-t from-purple-500/5 to-transparent pointer-events-none" />

        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-full transition-all duration-200 backdrop-blur-sm z-50"
        >
          <X className="w-6 h-6" />
        </button>

        <div className="flex h-full">
          <div className="w-64 bg-black/20 backdrop-blur-sm border-r border-white/10 p-4 flex flex-col">
            <h2 className="text-md font-bold text-amber-500 mb-6 px-2">
              Personal Training Program
            </h2>
            <div className="space-y-2 overflow-y-auto max-h-[calc(100vh-10rem)] text-sm pr-2 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
              {["overview", "schedule", "assessment"].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as "overview" | "schedule" | "assessment")}
                  className={`w-full px-4 py-3 rounded-lg flex items-center justify-between text-left transition-all duration-200 group ${
                    activeTab === tab
                      ? "bg-gradient-to-r from-purple-600 to-purple-500 text-white shadow-lg shadow-purple-500/20"
                      : "text-gray-400 hover:bg-white/5 hover:text-white hover:shadow-md hover:shadow-purple-500/10"
                  }`}
                >
                  <span className="font-medium capitalize">{tab}</span>
                  <ChevronRight
                    className={`w-5 h-5 transition-transform duration-200 ${
                      activeTab === tab ? "rotate-90" : "group-hover:translate-x-1"
                    }`}
                  />
                </button>
              ))}
              {activeTab === "schedule" && (
                <div className="mt-4 space-y-2">
                  {days.map((day) => (
                    <button
                      key={day}
                      onClick={() => setSelectedDay(day)}
                      className={`w-full px-4 py-3 rounded-lg flex items-center justify-between text-left transition-all duration-200 group ${
                        selectedDay === day
                          ? "bg-gradient-to-r from-purple-600 to-purple-500 text-white shadow-lg shadow-purple-500/20"
                          : "text-gray-400 hover:bg-white/5 hover:text-white hover:shadow-md hover:shadow-purple-500/10"
                      }`}
                    >
                      <span className="font-medium">{day}</span>
                      <ChevronRight
                        className={`w-5 h-5 transition-transform duration-200 ${
                          selectedDay === day ? "rotate-90" : "group-hover:translate-x-1"
                        }`}
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex-1 overflow-hidden backdrop-blur-sm">
            <div className="h-full flex flex-col">
              <div className="flex-1 overflow-y-auto p-6 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
                <AnimatePresence mode="wait">
                  <motion.div
                    key={activeTab}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                    className="space-y-6"
                  >
                    {activeTab === "overview" && renderOverviewTab()}
                    {activeTab === "schedule" && renderScheduleTab()}
                    {activeTab === "assessment" && renderAssessmentTab()}
                  </motion.div>
                </AnimatePresence>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
