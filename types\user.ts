export interface AccountData {
    email: string;
    firstName: string;
    surname: string;
    city: string;
    country: string;
    createdAt: string;
    IF: boolean;
  }
  
  export interface ProfileData {
    country: string;
    city: string;
    basic_info: {
      height: { value: string; unit: string };
      weight: { value: string; unit: string };
      age: string;
      gender: string;
    };
    fitness_goals: {
      primary: string;
      secondary: string;
    };
    workout_preferences: {
      frequency: string;
      duration: string;
      preferred_time: string;
    };
    health_information: {
      medical_conditions: string;
      injuries: string;
    };
    preferences: { // Add this section
      training_location: string;
      focus_areas: string[];
      sport_specific: string;
    };
    profileComplete: boolean;
    lastUpdated: string;
  }
  
  