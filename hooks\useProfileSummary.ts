import { useState } from "react"
import { storage, auth, db } from "@/components/firebase/config"
import { ref, uploadBytes, getDownloadURL } from "firebase/storage"
import { doc, getDoc, setDoc, updateDoc } from "firebase/firestore"
import { jsPDF } from "jspdf"

interface UseProfileSummaryResult {
  generateSummary: () => Promise<void>
  isLoading: boolean
  error: string | null
}

export function useProfileSummary(): UseProfileSummaryResult {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const ensureAssessmentDoc = async (userEmail: string) => {
    const assessmentDocRef = doc(db, `IF_users/${userEmail}/Profile/assessment`);
    const assessmentDoc = await getDoc(assessmentDocRef);

    if (!assessmentDoc.exists()) {
      await setDoc(assessmentDocRef, {
        summary: "",
        summaryGeneratedAt: null,
        summaryPdfUrl: null,
        status: "pending",
      });
    }
    return assessmentDocRef;
  };

  const fetchProfileData = async (userEmail: string) => {
    const fitnessDocRef = doc(db, `IF_users/${userEmail}/Profile/fitness`);
    const fitnessDoc = await getDoc(fitnessDocRef);
    if (!fitnessDoc.exists()) {
      throw new Error("Profile data not found");
    }
    return fitnessDoc.data();
  };

  const generateSummary = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error("No authenticated user found");
      }

      const userEmail = currentUser.email!;
      const profileData = await fetchProfileData(userEmail);
      const profileText = JSON.stringify(profileData); // Convert profile data to string

      // Ensure assessment document exists
      await ensureAssessmentDoc(userEmail);

      const idToken = await currentUser.getIdToken(true);
      const requestBody = { userEmail, profileText };

      console.log("Sending to /api/summarizeProfile:", JSON.stringify(requestBody));
      const response = await fetch("/api/summarizeProfile", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${idToken}`,
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || errorData.error || "Failed to generate summary");
      }

      const { summary } = await response.json();

      // Generate PDF and upload to storage
      const pdf = new jsPDF();
      const splitText = pdf.splitTextToSize(summary, 180);

      pdf.setFontSize(20);
      pdf.text("IntelligentFitness Profile Summary", 20, 20);
      pdf.setFontSize(12);
      pdf.text(splitText, 20, 40);
      pdf.setFontSize(10);
      pdf.text(`Generated on ${new Date().toLocaleDateString()}`, 20, pdf.internal.pageSize.height - 20);

      const pdfBlob = pdf.output("blob");

      const storageRef = ref(storage, `IF_users/${userEmail}/profile_summary.pdf`);
      await uploadBytes(storageRef, pdfBlob);
      const pdfUrl = await getDownloadURL(storageRef);

      // Update assessment document with summary and PDF URL
      const assessmentDocRef = doc(db, `IF_users/${userEmail}/Profile/assessment`);
      await updateDoc(assessmentDocRef, {
        summary,
        summaryPdfUrl: pdfUrl,
        summaryGeneratedAt: new Date().toISOString(),
        status: "completed",
        lastUpdated: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Profile summary generation error:", error);

      if (auth.currentUser?.email) {
        const assessmentDocRef = doc(db, `IF_users/${auth.currentUser.email}/Profile/assessment`);
        await updateDoc(assessmentDocRef, {
          status: "error",
          lastError: error instanceof Error ? error.message : "Unknown error",
          lastUpdated: new Date().toISOString(),
        }).catch(console.error);
      }

      setError(error instanceof Error ? error.message : "Failed to generate summary");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    generateSummary,
    isLoading,
    error,
  };
}

