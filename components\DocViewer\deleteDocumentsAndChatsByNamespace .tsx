import { Pinecone } from "@pinecone-database/pinecone";
import { adminDb, adminStorage } from '@/components/firebase-admin';

export const deleteDocumentAndChatsByNamespace = async (userId: string, namespace: string) => {
  try {
    // 1. Delete the document metadata from Firestore (user's files collection)
    const filesRef = adminDb.collection('users').doc(userId).collection('files');
    const filesQuery = filesRef.where('namespace', '==', namespace);
    const querySnapshot = await filesQuery.get();

    if (querySnapshot.empty) {
      throw new Error(`No document found with namespace: ${namespace}`);
    }

    let fileDocumentId = '';

    // Loop through each file document and delete it
    for (const fileDoc of querySnapshot.docs) {
      fileDocumentId = fileDoc.id;
      await fileDoc.ref.delete();
      await deleteAssociatedChats(userId, fileDocumentId);
    }

    // 2. Delete the embeddings from Pinecone with proper error handling
    try {
      await deleteVectorsFromPinecone(namespace);
    } catch (pineconeError) {
      console.error('Error deleting vectors from Pinecone:', pineconeError);
      // Continue with other deletions even if Pinecone deletion fails
    }

    // 3. Delete the document chunks from Firestore
    await deleteDocumentChunks(namespace, userId);

    // 4. Delete the file from Firebase Storage
    await deleteFileFromStorage(namespace, userId);

    // 5. Add a notification
    await addNotification(userId, `Document with namespace: ${namespace} has been successfully removed.`);

    return `Document and associated chats with namespace: ${namespace} have been successfully deleted.`;
  } catch (error) {
    console.error('Error deleting document and chats by namespace:', error);
    throw new Error('Error deleting document and chats.');
  }
};

const deleteVectorsFromPinecone = async (namespace: string) => {
  try {
    const index = process.env.PINECONE_INDEX;
    if (!index) {
      throw new Error('PINECONE_INDEX environment variable is not set.');
    }

    const pinecone = new Pinecone();
    //const pineconeIndex = pinecone.Index(index);
    
    const pineconeIndex = pinecone.Index(index);
    await pineconeIndex.namespace(namespace).deleteAll();
    console.log(`Vectors in namespace '${namespace}' have been deleted from Pinecone.`);
  } catch (error) {
    console.error('Error deleting vectors from Pinecone:', error);
    throw error; // Propagate the error to be handled by the caller
  }
};

const deleteAssociatedChats = async (userId: string, fileDocumentId: string) => {
  try {
    const chatsRef = adminDb.collection('users').doc(userId).collection('chats');
    const chatsQuery = chatsRef.where('fileDocumentId', '==', fileDocumentId);
    const chatSnapshot = await chatsQuery.get();

    if (!chatSnapshot.empty) {
      const deletionPromises = chatSnapshot.docs.map(async (chatDoc) => {
        await deleteAssociatedMessages(userId, chatDoc.id);
        await chatDoc.ref.delete();
      });
      
      await Promise.all(deletionPromises);
    }
  } catch (error) {
    console.error("Error deleting associated chats:", error);
    throw new Error("Error deleting associated chats.");
  }
};

const deleteAssociatedMessages = async (userId: string, chatId: string) => {
  try {
    const messagesRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('chats')
      .doc(chatId)
      .collection('messages');
    
    const messagesSnapshot = await messagesRef.get();
    
    if (!messagesSnapshot.empty) {
      const batch = adminDb.batch();
      messagesSnapshot.docs.forEach((messageDoc) => {
        batch.delete(messageDoc.ref);
      });
      await batch.commit();
    }
  } catch (error) {
    console.error("Error deleting associated messages:", error);
    throw new Error("Error deleting associated messages.");
  }
};

const deleteDocumentChunks = async (namespace: string, userId: string) => {
  try {
    const byteCollection = `users/${userId}/byteStoreCollection`;
    const byteStoreCollectionRef = adminDb.collection(byteCollection);
    const chunksQuerySnapshot = await byteStoreCollectionRef
      .where('metadata.doc_id', '==', namespace)
      .get();

    if (!chunksQuerySnapshot.empty) {
      // Use batched writes for better performance
      const batches = [];
      const batchSize = 500; // Firestore batch limit
      let batch = adminDb.batch();
      let operationCount = 0;

      chunksQuerySnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
        operationCount++;

        if (operationCount === batchSize) {
          batches.push(batch.commit());
          batch = adminDb.batch();
          operationCount = 0;
        }
      });

      if (operationCount > 0) {
        batches.push(batch.commit());
      }

      await Promise.all(batches);
      console.log(`Document chunks for namespace ${namespace} deleted from Firestore.`);
    } else {
      console.warn(`No document chunks found for namespace ${namespace} in Firestore.`);
    }
  } catch (error) {
    console.error('Error deleting document chunks from Firestore:', error);
    throw new Error('Error deleting document chunks from Firestore.');
  }
};

const deleteFileFromStorage = async (namespace: string, userId: string) => {
  try {
    const bucketName = process.env.FIREBASE_STORAGE_BUCKET;
    if (!bucketName) {
      throw new Error('FIREBASE_STORAGE_BUCKET environment variable is not set.');
    }

    const bucket = adminStorage.bucket(bucketName);
    const filePath = `uploads/${userId}/${namespace}`;

    const [fileExists] = await bucket.file(filePath).exists();
    if (fileExists) {
      await bucket.file(filePath).delete();
      console.log(`File ${namespace} deleted from Firebase Storage.`);
    } else {
      console.warn(`File ${namespace} not found in Firebase Storage.`);
    }
  } catch (error) {
    console.error('Error deleting file from Firebase Storage:', error);
    throw new Error('Error deleting file from Firebase Storage.');
  }
};

const addNotification = async (userId: string, message: string) => {
  try {
    const notificationsRef = adminDb
      .collection('users')
      .doc(userId)
      .collection('notifications');
      
    await notificationsRef.add({
      message,
      timestamp: new Date(),
      read: false,
      type: 'document_deletion'
    });
    
    console.log(`Notification added for user: ${userId} - ${message}`);
  } catch (error) {
    console.error('Error adding notification:', error);
    throw new Error('Error adding notification.');
  }
};