/**
 * Macro Nutrient Analyser
 *
 * This tool extracts individual dishes from a meal plan and processes them
 * to get nutritional information, then saves them to Firestore.
 */

import { v4 as uuidv4 } from 'uuid';
import { processWithGroq } from "@/components/tools/groq-ai";
import { setDoc, doc } from "firebase/firestore";
import { db } from "@/components/firebase/config";

// Define interfaces for dish extraction
interface ExtractedDish {
  name: string;
  recipe: string;
  mealType: 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'pre-workout' | 'post-workout';
  day: number;
}

interface NutritionalInfo {
  calories?: number;
  protein?: {
    grams?: number;
    percentage?: number;
  };
  carbohydrates?: {
    grams?: number;
    percentage?: number;
  };
  fat?: {
    grams?: number;
    percentage?: number;
    saturated?: number;
  };
  fiber?: number;
  sugar?: number;
  sodium?: number;
  vitamins?: Record<string, number | string>;
  minerals?: Record<string, number | string>;
  servingSize?: string;
  error?: string;
  [key: string]: unknown; // For any additional properties returned by the AI
}

interface ProcessedDish {
  id: string;
  name: string;
  recipe: string;
  nutritionalInfo: NutritionalInfo;
}

/**
 * Extracts individual dishes from a meal plan
 * @param mealPlan - The complete meal plan text
 * @param dayCount - The number of days in the meal plan
 * @returns Array of extracted dishes
 */
export async function extractDishesFromMealPlan(
  mealPlan: string,
  dayCount: number = 1
): Promise<ExtractedDish[]> {
  try {
    // Use Groq to extract dishes from the meal plan
    const prompt = `
Extract all individual dishes from the following meal plan. For each dish, provide:
1. The dish name
2. The complete recipe including ingredients and instructions
3. The meal type (breakfast, lunch, dinner, snack, pre-workout, or post-workout)
4. The day number (1-${dayCount})

Please respond with a JSON array of dish objects with the following structure:
[
  {
    "name": "Dish Name",
    "recipe": "Complete recipe with ingredients and instructions",
    "mealType": "breakfast|lunch|dinner|snack|pre-workout|post-workout",
    "day": 1
  },
  ...
]

Your response should be valid JSON that can be parsed with JSON.parse().

Here is the meal plan:
${mealPlan}
`;

    const result = await processWithGroq({
      prompt,
      model: "llama-3.3-70b-versatile",
      modelOptions: {
        temperature: 0.2,
        maxTokens: 4000, // This will be converted to max_tokens in processWithGroq
        response_format: { type: "json_object" }
      }
    });

    // Parse the JSON response
    try {
      // First try to parse the entire response as JSON
      const dishes = JSON.parse(result);
      if (Array.isArray(dishes)) {
        return dishes;
      }

      // If the response is an object with a dishes property
      if (dishes.dishes && Array.isArray(dishes.dishes)) {
        return dishes.dishes;
      }

      // If we have a different structure, try to find an array property
      for (const key in dishes) {
        if (Array.isArray(dishes[key])) {
          return dishes[key];
        }
      }

      throw new Error("Could not find dishes array in response");
    } catch (parseError) {
      console.error("Error parsing JSON response:", parseError);

      // Try to extract JSON from markdown code blocks
      const jsonMatch = result.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        try {
          const extractedJson = JSON.parse(jsonMatch[1].trim());
          if (Array.isArray(extractedJson)) {
            return extractedJson;
          }

          // If the extracted JSON is an object with a dishes property
          if (extractedJson.dishes && Array.isArray(extractedJson.dishes)) {
            return extractedJson.dishes;
          }

          // If we have a different structure, try to find an array property
          for (const key in extractedJson) {
            if (Array.isArray(extractedJson[key])) {
              return extractedJson[key];
            }
          }
        } catch (extractError) {
          console.error("Failed to extract JSON from markdown:", extractError);
        }
      }

      throw new Error("Failed to parse dishes from response");
    }
  } catch (error) {
    console.error("Error extracting dishes from meal plan:", error);
    return [];
  }
}

/**
 * Processes a dish to get nutritional information
 * @param dish - The dish to process
 * @returns The processed dish with nutritional information
 */
export async function processDish(dish: ExtractedDish): Promise<ProcessedDish> {
  try {
    const prompt = `You are an expert nutritionist tasked with breaking down the recipe into key components. Given the following recipe, calculate the estimated caloric, nutrient, and macronutrient breakdown per serving. Assume the recipe serves 4 people. Include the breakdown of calories, protein, fat, saturated fat, carbohydrates, fiber, sugar, sodium, and any key vitamins and minerals. Present the macronutrient breakdown as both grams and percentage of total calories, and list other important nutrients such as Vitamin A, Vitamin C, Calcium, Iron, and Potassium. If any of the properties are not available from the provided recipe, retrieve the answer from your knowledge base.

Please format your response as JSON with the following structure:
{
  "calories": number,
  "protein": { "grams": number, "percentage": number },
  "carbohydrates": { "grams": number, "percentage": number },
  "fat": { "grams": number, "percentage": number, "saturated": number },
  "fiber": number,
  "sugar": number,
  "sodium": number,
  "vitamins": { "A": string, "C": string, ... },
  "minerals": { "calcium": string, "iron": string, ... },
  "servingSize": string
}

Recipe Name: ${dish.name}
Recipe: ${dish.recipe}`;

    const result = await processWithGroq({
      prompt,
      model: process.env.GROQ_MODEL || "llama-3.3-70b-versatile",
      modelOptions: {
        temperature: 0.2,
        maxTokens: 4000, // This will be converted to max_tokens in processWithGroq
        response_format: { type: "json_object" }
      }
    });

    // Parse the JSON response
    let nutritionalInfo;
    try {
      nutritionalInfo = JSON.parse(result);
    } catch (parseError) {
      console.error("Error parsing nutritional info JSON:", parseError);

      // Try to extract JSON from markdown code blocks
      const jsonMatch = result.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        try {
          nutritionalInfo = JSON.parse(jsonMatch[1].trim());
        } catch (extractError) {
          console.error("Failed to extract JSON from markdown:", extractError);
          nutritionalInfo = { error: "Failed to parse nutritional information" };
        }
      } else {
        nutritionalInfo = { error: "Failed to parse nutritional information" };
      }
    }

    return {
      id: uuidv4(),
      name: dish.name,
      recipe: dish.recipe,
      nutritionalInfo
    };
  } catch (error) {
    console.error("Error processing dish:", error);
    return {
      id: uuidv4(),
      name: dish.name,
      recipe: dish.recipe,
      nutritionalInfo: { error: "Failed to process nutritional information" }
    };
  }
}

/**
 * Saves processed dishes to Firestore via the processReceipes API
 * @param userEmail - The user's email address
 * @param dishes - The processed dishes to save
 * @returns Result of the save operation
 */
export async function saveDishesToFirestore(
  userEmail: string,
  dishes: ProcessedDish[]
): Promise<{
  success: boolean;
  savedCount: number;
  errors: string[];
}> {
  const errors: string[] = [];
  let savedCount = 0;

  try {
    // Process each dish in sequence to avoid overwhelming the API
    for (const dish of dishes) {
      try {
        // Prepare the data for saving to Firestore
        const docId = dish.id;
        const category = "Recipes";

        // Log the dish being saved
        console.log(`Saving dish to Recipes collection: "${dish.name}" (ID: ${docId})`);

        try {
          // Format date for createdAt field
          const now = new Date();
          const formattedDate = now.toLocaleDateString('en-GB'); // Formats date as "dd/mm/yyyy"

          // Create the document data
          const docData = {
            "Recipe Name": dish.name,
            ...dish.nutritionalInfo,
            createdAt: formattedDate
          };

          // Save to Firestore directly using the imported modules
          console.log(`Saving to foodKhare_users/${userEmail}/${category}/${docId}`);
          await setDoc(doc(db, "foodKhare_users", userEmail, category, docId), docData);

          // Log success and increment counter
          console.log(`Successfully saved dish "${dish.name}" with ID: ${docId}`);
          savedCount++;
        } catch (processError) {
          // Log error and add to errors array
          console.error(`Error saving dish "${dish.name}":`, processError);
          errors.push(`Failed to save dish "${dish.name}": ${processError instanceof Error ? processError.message : "Unknown error"}`);
        }
      } catch (error) {
        console.error(`Error saving dish "${dish.name}":`, error);

        // Provide more detailed error information
        let errorMessage = "Unknown error";
        if (error instanceof Error) {
          errorMessage = error.message;
          if (error.cause) {
            errorMessage += ` (Cause: ${JSON.stringify(error.cause)})`;
          }
        }

        errors.push(`Error saving dish "${dish.name}": ${errorMessage}`);
        console.log(`Failed to save dish "${dish.name}" with ID: ${dish.id}`);
      }

      // Add a small delay between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return {
      success: errors.length === 0,
      savedCount,
      errors
    };
  } catch (error) {
    console.error("Error saving dishes to Firestore:", error);
    return {
      success: false,
      savedCount,
      errors: [...errors, error instanceof Error ? error.message : "Unknown error occurred"]
    };
  }
}

/**
 * Saves the meal plan to the MealPlans collection in Firestore
 * @param userEmail - The user's email address
 * @param mealPlan - The complete meal plan text
 * @param recipeIds - Array of recipe IDs associated with the meal plan
 * @returns Result of the save operation
 */
async function saveMealPlanToFirestore(
  userEmail: string,
  mealPlan: string,
  recipeIds: string[]
): Promise<{
  success: boolean;
  mealPlanId: string;
  error?: string;
}> {
  try {
    // Generate a unique ID for the meal plan
    const mealPlanId = uuidv4();

    // Create the document data
    const mealPlanData = {
      content: mealPlan,
      recipeIds: recipeIds,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Save to Firestore
    console.log(`Saving meal plan to foodKhare_users/${userEmail}/MealPlans/${mealPlanId}`);
    await setDoc(doc(db, "foodKhare_users", userEmail, "MealPlans", mealPlanId), mealPlanData);

    console.log(`Successfully saved meal plan with ID: ${mealPlanId}`);
    return {
      success: true,
      mealPlanId
    };
  } catch (error) {
    console.error("Error saving meal plan to Firestore:", error);
    return {
      success: false,
      mealPlanId: "",
      error: error instanceof Error ? error.message : "Unknown error occurred"
    };
  }
}

/**
 * Main function to analyze a meal plan and save dishes to Firestore
 * @param userEmail - The user's email address
 * @param mealPlan - The complete meal plan text
 * @param dayCount - The number of days in the meal plan
 * @returns Result of the analysis and save operation
 */
export async function analyzeMealPlan(
  userEmail: string,
  mealPlan: string,
  dayCount: number = 1
): Promise<{
  success: boolean;
  extractedDishCount: number;
  processedDishCount: number;
  savedDishCount: number;
  mealPlanId?: string;
  errors: string[];
}> {
  const errors: string[] = [];

  try {
    // Step 1: Extract dishes from the meal plan
    console.log("Extracting dishes from meal plan...");
    const extractedDishes = await extractDishesFromMealPlan(mealPlan, dayCount);
    console.log(`Extracted ${extractedDishes.length} dishes from meal plan`);

    // Log the first few dishes for debugging
    if (extractedDishes.length > 0) {
      console.log("Sample extracted dishes:");
      extractedDishes.slice(0, 2).forEach((dish, index) => {
        console.log(`Dish ${index + 1}: ${dish.name} (${dish.mealType}, Day ${dish.day})`);
      });
    }

    if (extractedDishes.length === 0) {
      errors.push("No dishes extracted from meal plan");
      return {
        success: false,
        extractedDishCount: 0,
        processedDishCount: 0,
        savedDishCount: 0,
        errors
      };
    }

    // Step 2: Process each dish to get nutritional information
    console.log("Processing dishes to get nutritional information...");
    const processedDishes: ProcessedDish[] = [];
    for (const dish of extractedDishes) {
      try {
        const processedDish = await processDish(dish);
        processedDishes.push(processedDish);
        console.log(`Processed dish: ${dish.name}`);
      } catch (error) {
        console.error(`Error processing dish "${dish.name}":`, error);
        errors.push(`Error processing dish "${dish.name}": ${error instanceof Error ? error.message : "Unknown error"}`);
      }

      // Add a small delay between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`Processed ${processedDishes.length} dishes`);

    // Step 3: Save processed dishes to Firestore
    console.log("Saving dishes to Firestore...");
    const saveResult = await saveDishesToFirestore(userEmail, processedDishes);
    console.log(`Saved ${saveResult.savedCount} dishes to Firestore`);

    // Combine errors from save operation
    errors.push(...saveResult.errors);

    // Step 4: Save the meal plan to the MealPlans collection
    console.log("Saving meal plan to MealPlans collection...");
    const recipeIds = processedDishes.map(dish => dish.id);
    const saveMealPlanResult = await saveMealPlanToFirestore(userEmail, mealPlan, recipeIds);

    if (!saveMealPlanResult.success) {
      errors.push(`Failed to save meal plan: ${saveMealPlanResult.error || "Unknown error"}`);
    }

    return {
      success: errors.length === 0,
      extractedDishCount: extractedDishes.length,
      processedDishCount: processedDishes.length,
      savedDishCount: saveResult.savedCount,
      mealPlanId: saveMealPlanResult.success ? saveMealPlanResult.mealPlanId : undefined,
      errors
    };
  } catch (error) {
    console.error("Error analyzing meal plan:", error);
    errors.push(error instanceof Error ? error.message : "Unknown error occurred");
    return {
      success: false,
      extractedDishCount: 0,
      processedDishCount: 0,
      savedDishCount: 0,
      errors
    };
  }
}
