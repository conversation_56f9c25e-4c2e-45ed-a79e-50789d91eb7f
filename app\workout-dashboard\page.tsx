"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation"; // Import the router
import { doc, getDoc } from "firebase/firestore";
import { db, auth } from "@/components/firebase/config";

// Import modal components
import { ExpectedLongTermSchedule, FitnessJourneyModal } from "@/components/DashboardModal/fitness-journey-modal";
import { KickStarterModal } from "@/components/DashboardModal/kick-starter-modal";
import {
  PersonalTrainerModal,
  PersonalTrainerSchedule,
} from "@/components/DashboardModal/personal-trainer-modal";
import { SevenDayModal, SevenDaySchedule } from "@/components/DashboardModal/Seven-day-modal";

// ----------------------------------------------------------------
//  Types
// ----------------------------------------------------------------

interface CoachInstructions {
  instructions: string;
  mistakes: string;
  modifications: string;
}

interface ExerciseSpecs {
  sets: string;
  reps: string;
  weight: string;
  duration: string;
  intensity: string;
}

interface Exercise {
  name: string;
  completed: boolean;
  exerciseName: string;
  libraryId: string;
  coachInstructions: CoachInstructions;
  specs: ExerciseSpecs;
}

interface DaySchedule {
  exercises: Exercise[];
  duration: string;
  intensity: string;
  focus: string;
  name: string;
  completed: boolean;
  location: string;
  timeFrame: string;
  workoutFrequency: string;
  coreWork: string;
  strengthTraining: string;
  cardio: string;
  flexibility: string;
}

interface Phase {
  [dayKey: string]: DaySchedule;
}

interface Phases {
  [phaseKey: string]: Phase;
}

export interface LongTermSchedule {
  title: string;
  lastUpdated: string;
  workoutReference?: string;
  scheduleId?: string;
  notes?: string[];
  strategies?: {
    maintainMotivation: string;
    overcomePlateaus: string;
    variety: string;
  };
  phases?: Phases;
}

type OptimizedSchedule = {
  title: string;
  days: Array<{
    name: string;
    exercises: Array<{
      name: string;
      completed: boolean;
    }>;
  }>;
  lastUpdated: string;
  workoutReference?: string;
  scheduleId?: string;
};

type ModalData =
  | LongTermSchedule
  | PersonalTrainerSchedule
  | SevenDaySchedule
  | OptimizedSchedule
  | null;

// Firestore document shapes
interface FirestoreLongTermDoc {
  title: string;
  lastUpdated: string;
  notes?: string[];
  strategies?: {
    maintainMotivation: string;
    overcomePlateaus: string;
    variety: string;
  };
  schedule?: {
    [dayKey: string]: {
      exercises: Array<{
        exerciseName?: string;
        name?: string;
        completed: boolean;
        libraryId?: string;
        coachInstructions?: CoachInstructions;
        specs?: ExerciseSpecs;
      }>;
      duration: string;
      intensity: string;
      focus: string;
      name: string;
      completed: boolean;
      location: string;
      timeFrame: string;
      workoutFrequency: string;
      coreWork: string;
      strengthTraining: string;
      cardio: string;
      flexibility: string;
    };
  };
  workoutReference?: string;
  scheduleId?: string;
}

// ----------------------------------------------------------------
//  Utility Functions
// ----------------------------------------------------------------

function transformLongTermSchedule(
  data: FirestoreLongTermDoc
): LongTermSchedule {
  const phases: Phases = { "Phase 1": {} };

  if (data.schedule) {
    Object.keys(data.schedule).forEach((dayKey) => {
      const dayInfo = data.schedule![dayKey];

      phases["Phase 1"][dayKey] = {
        exercises: (dayInfo.exercises || []).map((ex) => ({
          name: ex.exerciseName || ex.name || "Untitled",
          completed: ex.completed,
          exerciseName: ex.exerciseName || "Untitled",
          libraryId: ex.libraryId || "N/A",
          coachInstructions: ex.coachInstructions || {
            instructions: "",
            mistakes: "",
            modifications: "",
          },
          specs: ex.specs || {
            sets: "0",
            reps: "0",
            weight: "0",
            duration: "N/A",
            intensity: "Moderate",
          },
        })),
        duration: dayInfo.duration,
        intensity: dayInfo.intensity,
        focus: dayInfo.focus,
        name: dayInfo.name,
        completed: dayInfo.completed,
        location: dayInfo.location,
        timeFrame: dayInfo.timeFrame,
        workoutFrequency: dayInfo.workoutFrequency,
        coreWork: dayInfo.coreWork,
        strengthTraining: dayInfo.strengthTraining,
        cardio: dayInfo.cardio,
        flexibility: dayInfo.flexibility,
      };
    });
  }

  return {
    title: data.title,
    lastUpdated: data.lastUpdated,
    notes: data.notes || [],
    strategies: data.strategies || {
      maintainMotivation: "",
      overcomePlateaus: "",
      variety: "",
    },
    phases,
    workoutReference: data.workoutReference,
    scheduleId: data.scheduleId,
  };
}

async function getSelectedPlanName(userEmail: string): Promise<string | null> {
  try {
    const planRefs = {
      kickStarter: doc(db, "IF_users", userEmail, "Profile", "kick_starter"),
      shortTerm: doc(db, "IF_users", userEmail, "Profile", "shortTerm"),
      longTerm: doc(db, "IF_users", userEmail, "Profile", "longTerm"),
      schedule: doc(db, "IF_users", userEmail, "Profile", "schedule"),
    };

    const [kickStarterDoc, shortTermDoc, longTermDoc, scheduleDoc] =
      await Promise.all([
        getDoc(planRefs.kickStarter),
        getDoc(planRefs.shortTerm),
        getDoc(planRefs.longTerm),
        getDoc(planRefs.schedule),
      ]);

    if (kickStarterDoc.exists()) return "kick_starter";
    if (shortTermDoc.exists()) return "shortTerm";
    if (longTermDoc.exists()) return "longTerm";
    if (scheduleDoc.exists()) return "schedule";

    return null;
  } catch (error) {
    console.error("Error fetching selected plan name:", error);
    return null;
  }
}

// ----------------------------------------------------------------
//  Main Component
// ----------------------------------------------------------------

export default function WorkoutDashboard() {
  const router = useRouter(); // Initialize the router
  const [selectedPlanName, setSelectedPlanName] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(true); // Initialize to true
  const [modalData, setModalData] = useState<ModalData>(null);
  const [currentPhase, setCurrentPhase] = useState<number>(1);
  const [phaseProgress, setPhaseProgress] = useState<number>(0);
  const [dataLoaded, setDataLoaded] = useState<boolean>(false); // Add a state to track data loading

  const calculatePhaseProgress = (
    scheduleData: LongTermSchedule,
    phase: number
  ): number => {
    const phaseKey = `Phase ${phase}`;
    if (!scheduleData.phases || !scheduleData.phases[phaseKey]) return 0;

    const phaseExercises = Object.values(scheduleData.phases[phaseKey]).flatMap(
      (day) => day.exercises
    );
    if (phaseExercises.length === 0) return 0;

    const completedExercises = phaseExercises.filter((ex) => ex.completed);
    return (completedExercises.length / phaseExercises.length) * 100;
  };

  const fetchScheduleData = async (
    userEmail: string,
    planName: string
  ): Promise<ModalData> => {
    try {
      const scheduleRef = doc(db, "IF_users", userEmail, "Profile", planName);
      const scheduleSnapshot = await getDoc(scheduleRef);

      if (!scheduleSnapshot.exists()) {
        console.error(`No schedule found for plan: ${planName}`);
        return null;
      }
      const data = scheduleSnapshot.data();

      if (planName === "longTerm") {
        return transformLongTermSchedule(data as FirestoreLongTermDoc);
      }

      return data as ModalData;
    } catch (error) {
      console.error(`Error fetching schedule for ${planName}:`, error);
      return null;
    }
  };

  useEffect(() => {
    const initializeSchedule = async () => {
      const userEmail = auth.currentUser?.email;
      if (!userEmail) return;

      try {
        const planName = await getSelectedPlanName(userEmail);
        setSelectedPlanName(planName);

        if (!planName) {
          console.error("No plan selected in Firestore.");
          setDataLoaded(true); // Mark as loaded even if no plan is found
          return;
        }

        const scheduleData = await fetchScheduleData(userEmail, planName);
        if (!scheduleData) {
          console.error("No schedule data found.");
          setDataLoaded(true); // Mark as loaded even if no data is found
          return;
        }

        if (planName === "longTerm") {
          const longTermData = scheduleData as LongTermSchedule;
          setPhaseProgress(calculatePhaseProgress(longTermData, currentPhase));
        }

        setModalData(scheduleData);
        setDataLoaded(true); // Mark data as loaded
      } catch (error) {
        console.error("Error initializing schedule:", error);
        setDataLoaded(true); // Mark as loaded even if there's an error
      }
    };

    initializeSchedule();
  }, [currentPhase]);

  // Handle modal close with redirect
  const handleModalClose = () => {
    setIsModalOpen(false);
    // Redirect to the dashboard page
    router.push('/dashboard');
  };

  const renderModal = () => {
    // Only render the modal when data is loaded and the modal should be open
    if (!isModalOpen || !selectedPlanName || !modalData || !dataLoaded) return null;

    switch (selectedPlanName) {
      case "longTerm":
        return (
            <FitnessJourneyModal
              isOpen={isModalOpen}
              onClose={handleModalClose}
              data={modalData as ExpectedLongTermSchedule}
              currentPhase={currentPhase}
              setCurrentPhase={setCurrentPhase}
              phaseProgress={phaseProgress}
            />
        );
      case "kick_starter":
        return (
            <KickStarterModal
              isOpen={isModalOpen}
              onClose={handleModalClose}
              data={modalData as OptimizedSchedule}  // Corrected type
            />
        );
      case "shortTerm":
        return (
            <SevenDayModal
              isOpen={isModalOpen}
              onClose={handleModalClose}
              data={modalData as SevenDaySchedule} // Corrected type
            />
        );
      case "schedule":
        return (
            <PersonalTrainerModal
              isOpen={isModalOpen}
              onClose={handleModalClose}
              data={modalData as PersonalTrainerSchedule}  // Corrected type
            />
        );
      default:
        return null;
    }
  };

  // Show loading state if data is not yet loaded
  if (!dataLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <div className="text-white text-xl">Loading your workout plan...</div>
      </div>
    );
  }

  // Show refresh button if data is loaded but modalData is null
  if (!modalData) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black">
        <button
          onClick={() => window.location.reload()}
          className="px-6 py-3 text-xl font-semibold bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors"
        >
          REFRESH
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-grow">
        <div className="relative">
          <Image
            src="/bg-website-c.png?height=1080&width=1920"
            alt="Fitness background"
            width={1920}
            height={1080}
            className="absolute inset-0 w-full h-full object-cover"
          />
          <div className="relative z-10 bg-black bg-opacity-60">
            <section className="text-white py-5">
              <div className="container mx-auto px-4">
                <div className="max-w-4xl mx-auto text-center">
                  <h2 className="text-4xl text-blue-600 font-bold animate-fade-in-slide">
                    Workout Dashboard
                    <button
                      onClick={() => setIsModalOpen(true)}
                      className="ml-4 px-4 py-2 text-sm bg-blue-500 hover:bg-blue-600 rounded-full transition-colors"
                    >
                      View Details
                    </button>
                  </h2>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <footer className="bg-gradient-to-br from-purple-900 via-slate-800 to-slate-900 text-white p-4">
        <div className="container mx-auto">
          © 2025 AI Workout Planner. All rights reserved.
        </div>
      </footer>

      {renderModal()}
    </div>
  );
}