// app/dashboard/page.tsx
"use client";

import { JSX, useEffect, useLayoutEffect, useState } from "react";
import { useRouter } from "next/navigation";
import type { User } from "firebase/auth";
import { doc, getDoc, collection, getDocs } from "firebase/firestore";
import Link from "next/link";

import { Du<PERSON>bell, TrendingUp, Users, FileText } from "lucide-react";
import { motion } from "framer-motion";
import { auth, db } from "@/components/firebase/config";
import { ProfileCompletionProgress } from "@/components/profile-completion-progress";
import { DashboardLayout } from "@/components/dashboard-layout";
import {
  getUserAccount,
  getUserProfile,
  checkWorkoutPlanExists,
  checkWorkoutPlanDocumentExists,
} from "@/utils/firebase";
import type { ProfileData, AccountData } from "@/types/user";
import { ProfileSummaryDialog } from "@/components/ProfileSummaryDialog";
import { useGenerateWorkoutPlanAndSave } from "@/hooks/useGenerateWorkoutPlanAndSave";
import { ProgressMeter } from "@/components/ProgressMeter";
import type { ProgressStep } from "@/types/progress";
import { isErrorStep } from "@/types/progress";
import {
  ExpectedLongTermSchedule,
  FitnessJourneyModal,
} from "@/components/DashboardModal/fitness-journey-modal";
import { KickStarterModal } from "@/components/DashboardModal/kick-starter-modal";
import {
  PersonalTrainerModal,
  PersonalTrainerSchedule,
} from "@/components/DashboardModal/personal-trainer-modal";
import { SevenDayModal } from "@/components/DashboardModal/Seven-day-modal";
import Footer from "@/components/Footer";
import { useWorkoutGeneration } from "@/components/WorkoutGenerationContext";
import { WorkoutGenerationOverlay } from "@/components/WorkoutGenerationOverlay";

interface OptimizedSchedule {
  title: string;
  days: {
    name: string;
    exercises: { name: string; completed: boolean }[];
  }[];
  lastUpdated: string;
  workoutReference?: string;
  scheduleId?: string;
}

interface SevenDaySchedule {
  schedule: {
    [day: string]: {
      focus: string;
      location: string;
      duration: string;
      intensity: string;
      coreWork: string;
      cardio: string;
      exercises: {
        exerciseName: string;
        specs: {
          duration: string;
          weight?: string;
        };
        completed: boolean;
        coachInstructions: {
          instructions: string;
          mistakes: string;
          modifications: string;
        };
      }[];
    };
  };
  weeklyGoals: {
    primary: string;
    secondary: string[];
  };
  recovery: {
    restDays: string[];
    stretchingRoutine: string;
    coolDown: string;
  };
  nutrition: {
    preworkout: string;
    postworkout: string;
    hydration: string;
  };
}

type ModalData =
  | ExpectedLongTermSchedule
  | PersonalTrainerSchedule
  | SevenDaySchedule
  | OptimizedSchedule
  | null;

interface CoachInstructions {
  instructions: string;
  mistakes: string;
  modifications: string;
}

interface ExerciseSpecs {
  sets: string;
  reps: string;
  weight: string;
  duration: string;
  intensity: string;
}

interface Exercise {
  name: string;
  completed: boolean;
  exerciseName: string;
  libraryId: string;
  coachInstructions: CoachInstructions;
  specs: ExerciseSpecs;
}

interface DaySchedule {
  exercises: Exercise[];
  duration: string;
  intensity: string;
  focus: string;
  name: string;
  completed: boolean;
  location: string;
  timeFrame: string;
  workoutFrequency: string;
  coreWork: string;
  strengthTraining: string;
  cardio: string;
  flexibility: string;
}

interface Phase {
  [dayKey: string]: DaySchedule;
}

interface Phases {
  [phaseKey: string]: Phase;
}

interface LongTermScheduleData {
  title: string;
  lastUpdated: string;
  notes?: string[];
  strategies?: {
    maintainMotivation: string;
    overcomePlateaus: string;
    variety: string;
  };
  phases?: Phases;
  workoutReference?: string;
  scheduleId?: string;
}

interface FirestoreLongTermDoc {
  title: string;
  lastUpdated: string;
  notes?: string[];
  strategies?: {
    maintainMotivation: string;
    overcomePlateaus: string;
    variety: string;
  };
  schedule?: {
    [dayKey: string]: {
      exercises: Array<Partial<Exercise>>;
      duration: string;
      intensity: string;
      focus: string;
      name: string;
      completed: boolean;
      location: string;
      timeFrame: string;
      workoutFrequency: string;
      coreWork: string;
      strengthTraining: string;
      cardio: string;
      flexibility: string;
    };
  };
  workoutReference?: string;
  scheduleId?: string;
}

export default function Dashboard() {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [accountData, setAccountData] = useState<AccountData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isMenuOpen, setIsMenuOpen] = useState<boolean>(false);
  const [hasWorkoutPlan, setHasWorkoutPlan] = useState<boolean>(false);
  const [profileCompletion, setProfileCompletion] = useState<number>(0);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [modalData, setModalData] = useState<ModalData>(null);
  const [selectedPlanName, setSelectedPlanName] = useState<string | null>(null);
  const [currentPhase, setCurrentPhase] = useState<number>(1);
  const [phaseProgress, setPhaseProgress] = useState<number>(0);
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState<boolean>(false);
  const [hasWorkoutPlanCollection, setHasWorkoutPlanCollection] = useState<boolean>(false);

  const {
    isGeneratingPlan,
    toastMessage,
    progress,
  } = useGenerateWorkoutPlanAndSave();
  const { generationState, startGeneration } = useWorkoutGeneration();

  const router = useRouter();

  const fetchProfileData = async (user: User): Promise<void> => {
    try {
      if (!user.email) {
        throw new Error("No email associated with user");
      }

      const data = await getUserProfile(user.email);

      const [planExists, planName, workoutPlanDocExists] = await Promise.all([
        checkWorkoutPlanExists(user.email),
        getSelectedPlanName(user.email),
        checkWorkoutPlanDocumentExists(user.email)
      ]);

      setHasWorkoutPlan(planExists);
      setHasWorkoutPlanCollection(workoutPlanDocExists);
      setSelectedPlanName(planName);

      if (!data) {
        const defaultProfile: ProfileData = {
          country: "",
          city: "",
          basic_info: {
            height: { value: "", unit: "ft" },
            weight: { value: "", unit: "kg" },
            age: "",
            gender: "",
          },
          fitness_goals: { primary: "", secondary: "" },
          workout_preferences: { frequency: "", duration: "", preferred_time: "" },
          health_information: { medical_conditions: "", injuries: "" },
          preferences: {
            training_location: "",
            focus_areas: [] as string[],
            sport_specific: "",
          },
          profileComplete: false,
          lastUpdated: new Date().toISOString(),
        };
        setProfileData(defaultProfile);
        setProfileCompletion(0);
      } else {
        setProfileData(data);
        if (data.profileComplete) {
          setProfileCompletion(100);
        } else {
          setProfileCompletion(50);
        }
      }
    } catch (error: unknown) {
      console.error("Error fetching profile data:", error);
      setErrorMessage("Failed to load profile data");
      setProfileData(null);
      setHasWorkoutPlan(false);
      setHasWorkoutPlanCollection(false);
      setSelectedPlanName(null);
      setProfileCompletion(0);
    }
  };

  const fetchAccountData = async (user: User): Promise<void> => {
    try {
      if (!user.email) throw new Error("No email associated with user");
      const data = await getUserAccount(user.email);
      if (!data) {
        const defaultAccount: AccountData = {
          email: user.email,
          firstName: user.displayName?.split(" ")[0] || "",
          surname: user.displayName?.split(" ").slice(1).join(" ") || "",
          city: "",
          country: "",
          createdAt: new Date().toISOString(),
          IF: true,
        };
        setAccountData(defaultAccount);
      } else {
        setAccountData(data);
      }
    } catch (error: unknown) {
      console.error("Error fetching account data:", error);
      setErrorMessage("Failed to load account data");
    }
  };

  const getSelectedPlanName = async (userEmail: string): Promise<string | null> => {
    try {
      const planRefs = {
        kickStarter: doc(db, "IF_users", userEmail, "Profile", "kick_starter"),
        shortTerm: doc(db, "IF_users", userEmail, "Profile", "shortTerm"),
        longTerm: doc(db, "IF_users", userEmail, "Profile", "longTerm"),
        schedule: doc(db, "IF_users", userEmail, "Profile", "schedule"),
      };

      const [kickStarterDoc, shortTermDoc, longTermDoc, scheduleDoc] =
        await Promise.all([
          getDoc(planRefs.kickStarter),
          getDoc(planRefs.shortTerm),
          getDoc(planRefs.longTerm),
          getDoc(planRefs.schedule),
        ]);

      if (kickStarterDoc.exists()) return "kick_starter";
      if (shortTermDoc.exists()) return "shortTerm";
      if (longTermDoc.exists()) return "longTerm";
      if (scheduleDoc.exists()) return "schedule";
      return null;
    } catch (error: unknown) {
      console.error("Error fetching selected plan name:", error);
      return null;
    }
  };

  const transformLongTermSchedule = (data: FirestoreLongTermDoc): LongTermScheduleData => {
    const phases: Phases = { "Phase 1": {} as Phase };
    if (data.schedule) {
      Object.entries(data.schedule).forEach(([dayKey, dayInfo]) => {
        phases["Phase 1"][dayKey] = {
          exercises: (dayInfo.exercises || []).map((ex: Partial<Exercise>): Exercise => ({
            name: ex.exerciseName || ex.name || "Untitled",
            completed: ex.completed ?? false,
            exerciseName: ex.exerciseName || "Untitled",
            libraryId: ex.libraryId || "N/A",
            coachInstructions: ex.coachInstructions || {
              instructions: "",
              mistakes: "",
              modifications: "",
            },
            specs: ex.specs || {
              sets: "0",
              reps: "0",
              weight: "0",
              duration: "N/A",
              intensity: "Moderate",
            },
          })),
          duration: dayInfo.duration,
          intensity: dayInfo.intensity,
          focus: dayInfo.focus,
          name: dayInfo.name,
          completed: dayInfo.completed,
          location: dayInfo.location,
          timeFrame: dayInfo.timeFrame,
          workoutFrequency: dayInfo.workoutFrequency,
          coreWork: dayInfo.coreWork,
          strengthTraining: dayInfo.strengthTraining,
          cardio: dayInfo.cardio,
          flexibility: dayInfo.flexibility,
        };
      });
    }
    return {
      title: data.title,
      lastUpdated: data.lastUpdated,
      notes: data.notes || [],
      strategies: data.strategies || { maintainMotivation: "", overcomePlateaus: "", variety: "" },
      phases,
      workoutReference: data.workoutReference,
      scheduleId: data.scheduleId,
    };
  };

  const fetchScheduleData = async (userEmail: string, planName: string): Promise<ModalData> => {
    try {
      const scheduleRef = doc(db, "IF_users", userEmail, "Profile", planName);
      const scheduleSnapshot = await getDoc(scheduleRef);
      if (!scheduleSnapshot.exists()) {
        console.error(`No schedule found for plan: ${planName}`);
        return null;
      }
      const data = scheduleSnapshot.data() as
        | FirestoreLongTermDoc
        | OptimizedSchedule
        | SevenDaySchedule
        | PersonalTrainerSchedule;
      if (planName === "longTerm") {
        const transformedData = transformLongTermSchedule(data as FirestoreLongTermDoc);
        return transformedData as ExpectedLongTermSchedule;
      }
      return data as ModalData;
    } catch (error: unknown) {
      console.error(`Error fetching schedule for ${planName}:`, error);
      return null;
    }
  };

  const handleGenerateWorkoutPlan = async () => {
    try {
      const user = auth.currentUser;
      if (!user || !user.email) {
        console.error("User not logged in");
        return;
      }

      const profileData = await getUserProfile(user.email);
      if (!profileData) {
        console.error("No profile data found");
        return;
      }

      const profileText = JSON.stringify(profileData);
      await startGeneration(profileText);
    } catch (error) {
      console.error("Error initiating workout plan generation:", error);
    }
  };

  const calculatePhaseProgress = (scheduleData: LongTermScheduleData, phase: number): number => {
    const phaseKey = `Phase ${phase}`;
    if (!scheduleData.phases || !scheduleData.phases[phaseKey]) return 0;
    const phaseExercises = Object.values(scheduleData.phases[phaseKey]).flatMap(
      (day: DaySchedule) => day.exercises
    );
    if (phaseExercises.length === 0) return 0;
    const completedExercises = phaseExercises.filter((ex: Exercise) => ex.completed);
    return (completedExercises.length / phaseExercises.length) * 100;
  };

  const handleAlignedWorkoutPlanAction = async (): Promise<void> => {
    if (profileCompletion < 100) {
      router.push("/profile/complete?step=basic-info");
    } else if (generationState !== 'idle') {
      // Do nothing while generating
    } else if (profileCompletion === 100 && !hasWorkoutPlanCollection) {
      await handleGenerateWorkoutPlan();
    } else if (profileCompletion === 100 && !hasWorkoutPlan) {
      router.push("/workout-selector");
    } else if (selectedPlanName) {
      if (!currentUser?.email) return;

      const planName = await getSelectedPlanName(currentUser.email);
      if (!planName) {
        setSelectedPlanName(null);
        router.push("/workout-selector");
        return;
      }

      setSelectedPlanName(planName);

      const scheduleData = await fetchScheduleData(currentUser.email, planName);
      if (!scheduleData) {
        console.error("No schedule data found");
        return;
      }

      if (planName === "longTerm") {
        const longTermData = scheduleData as LongTermScheduleData;
        const prog = calculatePhaseProgress(longTermData, currentPhase);
        setPhaseProgress(prog);
      }

      setModalData(scheduleData);
      setIsModalOpen(true);
    } else {
      router.push("/workout-selector");
    }
  };

  const getAlignedButtonText = (): string => {
    if (profileCompletion < 100) return "Complete Personal Profile";
    if (generationState !== 'idle') return "Generating...";
    if (profileCompletion === 100 && !hasWorkoutPlanCollection) return "Generate Workout Plan";
    if (profileCompletion === 100 && !hasWorkoutPlan) return "Select Workout Plan";
    if (selectedPlanName) return "My Workout Schedule";
    return "Select Workout Plan";
  };

  const getButtonColor = (): string => {
    if (profileCompletion < 100) return "bg-blue-600 hover:bg-blue-700";
    if (generationState !== 'idle') return "bg-gray-500 hover:bg-gray-600";
    if (profileCompletion === 100 && !hasWorkoutPlanCollection) return "bg-red-400 hover:bg-red-500";
    if (profileCompletion === 100 && !hasWorkoutPlan) return "bg-amber-500 hover:bg-amber-600";
    if (selectedPlanName) return "bg-purple-500 hover:bg-purple-600";
    return "bg-amber-500 hover:bg-amber-600";
  };

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      try {
        if (user) {
          setCurrentUser(user);
          setErrorMessage(null);
        } else {
          router.push("/auth/signin");
        }
      } catch (error: unknown) {
        console.error("Error in auth state change:", error);
        setErrorMessage("Authentication error");
      }
    });
    return () => unsubscribe();
  }, [router]);

  useLayoutEffect(() => {
    if (currentUser) {
      Promise.all([fetchProfileData(currentUser), fetchAccountData(currentUser)]).finally(() => {
        setIsLoading(false);
      });
    }
  }, [currentUser]);

  useEffect(() => {
    const handleFocus = () => {
      if (currentUser) {
        fetchProfileData(currentUser);
      }
    };
    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [currentUser]);

  const renderModal = (): JSX.Element | null => {
    if (!isModalOpen || !selectedPlanName || !modalData) return null;
    switch (selectedPlanName) {
      case "longTerm":
        return (
          <FitnessJourneyModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            data={modalData as ExpectedLongTermSchedule}
            currentPhase={currentPhase}
            setCurrentPhase={setCurrentPhase}
            phaseProgress={phaseProgress}
          />
        );
      case "kick_starter":
        return (
          <KickStarterModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            data={modalData as OptimizedSchedule}
          />
        );
      case "shortTerm":
        return (
          <SevenDayModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            data={modalData as SevenDaySchedule}
          />
        );
      case "schedule":
        return (
          <PersonalTrainerModal
            isOpen={isModalOpen}
            onClose={() => setIsModalOpen(false)}
            data={modalData as PersonalTrainerSchedule}
          />
        );
      default:
        return null;
    }
  };

  const steps: ProgressStep[] = [
    "Initiating",
    "Generating Training Plan",
    "Generating Short Term Plan",
    "Generating Long Term Plan",
    "Engaging form technique coach",
    "Generating Nutritional suggestions",
    "Saving Data",
    "Completed",
  ];

  const isProfileIncomplete = profileCompletion < 100;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-white">Loading...</div>
      </div>
    );
  }

  if (errorMessage) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-red-400">{errorMessage}</div>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg text-white">Please sign in to access your dashboard</div>
      </div>
    );
  }

  return (
    <DashboardLayout>
      <div className="relative min-h-screen">

        <div className="md:hidden">
          <button
            className="p-2 text-white focus:outline-none"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <svg
              className="h-6 w-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 6h16M4 12h16m-7 6h7"
              />
            </svg>
          </button>
          {isMenuOpen && (
            <nav className="fixed inset-0 bg-black/90 backdrop-blur-sm p-4 z-50">
              <button
                className="absolute top-4 right-4 text-white p-2"
                onClick={() => setIsMenuOpen(false)}
              >
                <svg
                  className="h-6 w-6"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
              <ul className="mt-8 space-y-4">
                <li>
                  <Link
                    href="/dashboard"
                    className="flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Dumbbell className="mr-3 h-5 w-5 text-red-400 group-hover:text-white" />
                    Dashboard
                  </Link>
                </li>
                <li>
                  <Link
                    href="/workout-dashboard"
                    className="flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Dumbbell className="mr-3 h-5 w-5 text-red-400 group-hover:text-white" />
                    My Workouts
                  </Link>
                </li>
                <li>
                  <Link
                    href="/progress"
                    className="flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <TrendingUp className="mr-3 h-5 w-5 text-green-400 group-hover:text-white" />
                    Progress
                  </Link>
                </li>
                <li>
                  <Link
                    href="/dashboard/community"
                    className="flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Users className="mr-3 h-5 w-5 text-yellow-400 group-hover:text-white" />
                    Community
                  </Link>
                </li>
                <li>
                  <Link
                    href="/library"
                    className="flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <FileText className="mr-3 h-5 w-5 text-pink-400 group-hover:text-white" />
                    Exercise Library
                  </Link>
                </li>
                <li>
                  <Link
                    href="/profile/complete"
                    className="flex items-center px-4 py-2 text-gray-300 hover:bg-purple-500 hover:text-white rounded-md transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Users className="mr-3 h-5 w-5 text-teal-400 group-hover:text-white" />
                    Edit Profile
                  </Link>
                </li>
              </ul>
            </nav>
          )}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="p-6 relative"
        >
          <h1 className="text-3xl font-bold mb-6 text-white">
            Welcome{" "}
            {accountData?.firstName ||
              currentUser.displayName?.split(" ")[0] || "Fitness Enthusiast"}
            !
          </h1>

          {isGeneratingPlan && (
            <ProgressMeter
              currentStep={progress}
              steps={steps}
              error={isErrorStep(progress) ? toastMessage?.message : undefined}
            />
          )}

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 backdrop-blur-sm shadow-2xl rounded-2xl p-6 border border-purple-500/20 order-last md:order-first"
            >
              <h2 className="text-xl font-semibold mb-4 text-white">Quick Actions</h2>
              <div className="space-y-4 mb-10">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  onClick={() => {
                    if (!isProfileIncomplete) router.push("/profile-summary");
                  }}
                  className={`w-full flex items-center p-3 rounded-xl transition-colors ${
                    isProfileIncomplete
                      ? "bg-gray-700 text-gray-400 cursor-not-allowed"
                      : "bg-orange-500 text-white hover:bg-orange-600"
                  }`}
                >
                  <FileText className="mr-3 h-5 w-5" />
                  My Profile Assessment
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  onClick={handleAlignedWorkoutPlanAction}
                  disabled={generationState !== 'idle'}
                  className={`w-full flex items-center p-3 rounded-xl transition-colors ${getButtonColor()} ${
                    generationState !== 'idle' ? 'opacity-75 cursor-not-allowed' : ''
                  }`}
                >
                  <Dumbbell className="mr-3 h-5 w-5" />
                  {getAlignedButtonText()}
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  onClick={async () => {
                    if (!currentUser?.email) return;

                    try {
                      const recordsRef = collection(db, "IF_users", currentUser.email, "tracking_records");
                      await getDocs(recordsRef);
                      router.push("/progress");
                    } catch (error) {
                      console.error("Error checking tracking records:", error);
                      router.push("/progress");
                    }
                  }}
                  className="w-full flex items-center p-3 rounded-xl transition-colors bg-green-500 text-white hover:bg-green-600"
                >
                  <TrendingUp className="mr-3 h-5 w-5" />
                  View Progress
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  onClick={() => router.push("/community")}
                  className="w-full flex items-center p-3 rounded-xl transition-colors bg-blue-500 text-white hover:bg-blue-600"
                >
                  <Users className="mr-3 h-5 w-5" />
                  Join Community Discussion
                </motion.button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
              className="bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 backdrop-blur-sm shadow-2xl rounded-2xl p-6 border border-purple-500/20 order-first md:order-last"
            >
              <ProfileCompletionProgress
                user={currentUser}
                profileData={profileData}
                onEditProfile={() => router.push("/profile/complete?step=basic-info")}
                isEditingProfile={false}
                onProgressChange={(prog: number) => {
                  console.log("Profile progress updated:", prog);
                  setProfileCompletion(prog);
                }}
                onProfileUpdate={() => {
                  console.log("Profile updated");
                  if (currentUser) fetchProfileData(currentUser);
                }}
              />
              <div className="mt-4 space-y-2">
                {profileCompletion < 100 ? (
                  <p className="text-gray-300 text-sm">
                    You still have a few steps to complete your profile.{" "}
                    <Link
                      href="/profile/complete?step=basic-info"
                      className="underline text-blue-400"
                    >
                      Complete your profile now
                    </Link>
                  </p>
                ) : (
                  <p className="text-green-400 text-sm">
                    Your profile is complete! Ready to achieve your goals.
                  </p>
                )}
              </div>
            </motion.div>
          </div>
          <Footer />
        </motion.div>

        {isModalOpen && renderModal()}

        {isProfileDialogOpen && (
          <ProfileSummaryDialog
            isOpen={isProfileDialogOpen}
            onClose={() => setIsProfileDialogOpen(false)}
            userEmail={currentUser.email || ""}
          />
        )}

        <WorkoutGenerationOverlay />
      </div>
    </DashboardLayout>
  );
}