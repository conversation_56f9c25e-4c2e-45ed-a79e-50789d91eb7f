"use client"

import React, { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import {
  Calendar,
  CheckCircle,
  Clock,
  Dumbbell,
  Battery,
  BarChart3,
  AlertCircle,
  Plus,
  Save,
  Trash2,
  Loader2,
} from "lucide-react"
import { doc, getDoc, setDoc, collection, query, getDocs, Timestamp, updateDoc, deleteDoc } from "firebase/firestore"
import { getAuth, onAuthStateChanged } from "firebase/auth"
import { db } from "@/components/firebase/config"
import { format } from "date-fns"


interface TrackingTabProps {
  userEmail?: string // Make userEmail optional since we'll get it from auth
  activeDay?: number // Day selected in the kick-starter sidebar
}

interface Exercise {
  exerciseId: string
  exerciseName: string
  libraryId: string
  workoutReference: string
  day: number
  category: string // warm_up, resistance_training, cardio_training, cool_down
}

// Define types for workout data structure
interface WorkoutExercise {
  exercise: string
  libraryId: string
  [key: string]: unknown // For other properties that might exist
}

interface WorkoutDay {
  day: number
  warm_up?: WorkoutExercise[]
  resistance_training?: WorkoutExercise[]
  cardio_training?: WorkoutExercise[]
  cool_down?: WorkoutExercise[]
  [key: string]: unknown // For other properties that might exist
}

interface TrackingRecord {
  id: string
  exerciseId: string
  exerciseName: string
  libraryId: string
  workoutReference: string
  completedDate: Timestamp
  difficultyLevel: number // 1-5
  energyLevel: number // 1-5
  timeToComplete: number // in minutes
  sets?: number
  reps?: number
  weight?: number
  notes?: string
  day: number
  category: string
}

interface TrackingFormData {
  difficultyLevel: number
  energyLevel: number
  timeToComplete: number
  sets?: number
  reps?: number
  weight?: number
  notes?: string
}

export default function TrackingTab({ userEmail: propUserEmail, activeDay: propActiveDay }: TrackingTabProps) {
  const [exercises, setExercises] = useState<Exercise[]>([])
  const [trackingRecords, setTrackingRecords] = useState<TrackingRecord[]>([])
  const [selectedExercise, setSelectedExercise] = useState<Exercise | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState<TrackingFormData>({
    difficultyLevel: 3,
    energyLevel: 3,
    timeToComplete: 0,
    sets: undefined,
    reps: undefined,
    weight: undefined,
    notes: "",
  })
  const [editingRecord, setEditingRecord] = useState<TrackingRecord | null>(null)

  // Add state for active day (default to Day 1 or use prop value)
  const [activeDay, setActiveDay] = useState<number>(propActiveDay || 1)

  // Update activeDay when prop changes
  useEffect(() => {
    console.log("propActiveDay changed:", propActiveDay)
    // Always update activeDay when propActiveDay changes, even if it's undefined
    // This ensures we sync with the kick-starter modal
    if (propActiveDay !== undefined) {
      console.log("Setting activeDay to:", propActiveDay)
      setActiveDay(propActiveDay)
    }
  }, [propActiveDay])

  // Debug activeDay changes
  useEffect(() => {
    console.log("Current activeDay:", activeDay)
  }, [activeDay])

  // Add state for authenticated user email
  const [userEmail, setUserEmail] = useState<string | null>(propUserEmail || null)

  // Firebase Authentication listener to get user email
  useEffect(() => {
    console.log("Setting up auth listener, prop userEmail:", propUserEmail)

    // If userEmail was provided as a prop, use it
    if (propUserEmail) {
      console.log("Using provided userEmail prop:", propUserEmail)
      setUserEmail(propUserEmail)
      return
    }

    // Otherwise, get it from Firebase Auth
    const auth = getAuth()

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      const email = user?.email || null
      console.log("Auth state changed, user email:", email)
      setUserEmail(email)
    })

    // Clean up the listener on component unmount
    return () => unsubscribe()
  }, [propUserEmail])

  // Add a timeout to prevent infinite loading during initial load
  useEffect(() => {
    // Only set a timeout if we're loading and not yet initialized
    if (isLoading && !isInitialized) {
      const timeoutId = setTimeout(() => {
        console.warn("Initial loading timeout reached, forcing loading state to false")
        setIsLoading(false)
        setError("Loading took too long. Please try again or refresh the page.")
      }, 15000) // 15 seconds timeout

      return () => clearTimeout(timeoutId)
    }
  }, [isLoading, isInitialized])

  // Function to handle retry
  const handleRetry = () => {
    setError(null)
    setIsLoading(true)
    setIsInitialized(false)
    // This will trigger the useEffect that calls initializeTracking
  }

  // Initialize tracking document and load data
  useEffect(() => {
    const initializeTracking = async () => {
      if (isInitialized) return; // Skip if already initialized

      if (!userEmail) {
        console.log("No userEmail available yet, waiting for authentication...")
        // If we're still waiting for authentication, keep loading
        if (isLoading) return

        setError("User email is missing. Please sign in to track your exercises.")
        setIsLoading(false)
        return
      }

      console.log("Initializing tracking with userEmail:", userEmail)
      setIsLoading(true)
      setError(null)

      try {
        // Check if tracking document exists
        const trackingRef = doc(db, "IF_users", userEmail, "Profile", "tracking")
        console.log("Checking if tracking document exists...")
        const trackingSnap = await getDoc(trackingRef)

        if (!trackingSnap.exists()) {
          console.log("Tracking document doesn't exist, creating it...")
          // Create tracking document if it doesn't exist
          const created = await createTrackingDocument(userEmail)
          if (!created) {
            console.error("Failed to create tracking document")
            setError("Failed to initialize tracking. Please try again.")
            setIsLoading(false)
            return
          }
          console.log("Tracking document created successfully")
        } else {
          console.log("Tracking document already exists")
          // Verify the document has the required fields
          const data = trackingSnap.data()
          console.log("Tracking document data:", data)

          if (!data.workoutReference) {
            console.warn("Tracking document exists but has no workoutReference")
          }
        }

        // Load exercises from kick_starter document
        console.log("Loading exercises from kick_starter...")
        await loadExercises(userEmail)
        console.log("Exercises loaded successfully")

        // Load existing tracking records
        console.log("Loading tracking records...")
        await loadTrackingRecords(userEmail)
        console.log("Tracking records loaded successfully")

        // Mark as initialized to prevent re-initialization
        setIsInitialized(true)
      } catch (err) {
        console.error("Error initializing tracking:", err)
        setError(`Failed to initialize tracking: ${err instanceof Error ? err.message : "Unknown error"}`)
      } finally {
        setIsLoading(false)
      }
    }

    if (userEmail && !isInitialized) {
      initializeTracking()
    }
  }, [userEmail, isInitialized, isLoading])

  // Create tracking document
  const createTrackingDocument = async (email: string): Promise<boolean> => {
    try {
      console.log("Creating tracking document for user:", email)
      const trackingRef = doc(db, "IF_users", email, "Profile", "tracking")

      // Get workoutReference from kick_starter document
      const kickStarterRef = doc(db, "IF_users", email, "Profile", "kick_starter")
      console.log("Fetching kick_starter document...")
      const kickStarterSnap = await getDoc(kickStarterRef)

      if (!kickStarterSnap.exists()) {
        console.error("Kick starter document not found")
        setError("Kick starter document not found. Please set up your workout plan first.")
        return false
      }

      const kickStarterData = kickStarterSnap.data()
      console.log("Kick starter data:", kickStarterData)
      const workoutReference = kickStarterData.workoutReference || ""

      if (!workoutReference) {
        console.warn("No workoutReference found in kick_starter document")
      }

      // Create tracking document with initial data
      console.log("Setting tracking document with workoutReference:", workoutReference)
      await setDoc(trackingRef, {
        workoutReference,
        createdAt: Timestamp.now(),
        lastUpdated: Timestamp.now(),
        records: [],
      })

      console.log("Tracking document created successfully")
      return true
    } catch (err) {
      console.error("Error creating tracking document:", err)
      setError(`Failed to create tracking document: ${err instanceof Error ? err.message : "Unknown error"}`)
      return false
    }
  }

  // Load exercises from kick_starter document
  const loadExercises = async (email: string) => {
    try {
      console.log("Loading exercises for user:", email)
      const kickStarterRef = doc(db, "IF_users", email, "Profile", "kick_starter")
      const kickStarterSnap = await getDoc(kickStarterRef)

      if (!kickStarterSnap.exists()) {
        console.error("Kick starter document not found")
        setExercises([])
        setIsLoading(false)
        setError("Workout plan not found. Please set up your kick starter plan first.")
        return
      }

      const kickStarterData = kickStarterSnap.data()
      console.log("Kick starter data keys:", Object.keys(kickStarterData))

      const workoutReference = kickStarterData.workoutReference || ""
      const schedule = kickStarterData.schedule || {}

      if (!schedule.days || !Array.isArray(schedule.days) || schedule.days.length === 0) {
        console.error("No days array found in schedule or days array is empty", schedule)
        setExercises([])
        setIsLoading(false)
        setError("Your workout plan doesn't have any exercises. Please set up your workout schedule first.")
        return
      }

      const days = schedule.days || []
      console.log(`Found ${days.length} days in schedule`)

      const extractedExercises: Exercise[] = []

      // Extract exercises from each day
      days.forEach((day: WorkoutDay, index: number) => {
        if (!day) {
          console.warn(`Day at index ${index} is null or undefined`)
          return
        }

        const dayNumber = day.day || index + 1
        console.log(`Processing day ${dayNumber}`)

        // Process warm-up exercises
        if (day.warm_up && Array.isArray(day.warm_up)) {
          day.warm_up.forEach((exercise: WorkoutExercise, index: number) => {
            if (exercise && exercise.exercise && exercise.libraryId) {
              extractedExercises.push({
                exerciseId: `${dayNumber}-warm_up-${exercise.libraryId}-${index}`,
                exerciseName: exercise.exercise,
                libraryId: exercise.libraryId,
                workoutReference,
                day: dayNumber,
                category: "warm_up",
              })
            } else {
              console.warn("Skipping warm-up exercise with missing data:", exercise)
            }
          })
        }

        // Process resistance training exercises
        if (day.resistance_training && Array.isArray(day.resistance_training)) {
          day.resistance_training.forEach((exercise: WorkoutExercise, index: number) => {
            if (exercise && exercise.exercise && exercise.libraryId) {
              extractedExercises.push({
                exerciseId: `${dayNumber}-resistance_training-${exercise.libraryId}-${index}`,
                exerciseName: exercise.exercise,
                libraryId: exercise.libraryId,
                workoutReference,
                day: dayNumber,
                category: "resistance_training",
              })
            } else {
              console.warn("Skipping resistance training exercise with missing data:", exercise)
            }
          })
        }

        // Process cardio training exercises
        if (day.cardio_training && Array.isArray(day.cardio_training)) {
          day.cardio_training.forEach((exercise: WorkoutExercise, index: number) => {
            if (exercise && exercise.exercise && exercise.libraryId) {
              extractedExercises.push({
                exerciseId: `${dayNumber}-cardio_training-${exercise.libraryId}-${index}`,
                exerciseName: exercise.exercise,
                libraryId: exercise.libraryId,
                workoutReference,
                day: dayNumber,
                category: "cardio_training",
              })
            } else {
              console.warn("Skipping cardio training exercise with missing data:", exercise)
            }
          })
        }

        // Process cool down exercises
        if (day.cool_down && Array.isArray(day.cool_down)) {
          day.cool_down.forEach((exercise: WorkoutExercise, index: number) => {
            if (exercise && exercise.exercise && exercise.libraryId) {
              extractedExercises.push({
                exerciseId: `${dayNumber}-cool_down-${exercise.libraryId}-${index}`,
                exerciseName: exercise.exercise,
                libraryId: exercise.libraryId,
                workoutReference,
                day: dayNumber,
                category: "cool_down",
              })
            } else {
              console.warn("Skipping cool down exercise with missing data:", exercise)
            }
          })
        }
      })

      console.log(`Extracted ${extractedExercises.length} exercises from kick_starter`)

      if (extractedExercises.length === 0) {
        console.warn("No exercises extracted from kick_starter")
        setError("No exercises found in your workout plan. Please set up your workout schedule first.")
        setIsLoading(false)
        return
      }

      setExercises(extractedExercises)
    } catch (err) {
      console.error("Error loading exercises:", err)
      setError(`Failed to load exercises: ${err instanceof Error ? err.message : "Unknown error"}`)
      setIsLoading(false)
    }
  }

  // Load tracking records
  const loadTrackingRecords = async (email: string) => {
    try {
      console.log("Loading tracking records for user:", email)
      const recordsRef = collection(db, "IF_users", email, "tracking_records")
      const q = query(recordsRef)
      const querySnapshot = await getDocs(q)

      const records: TrackingRecord[] = []

      querySnapshot.forEach((doc) => {
        records.push({ id: doc.id, ...doc.data() } as TrackingRecord)
      })

      setTrackingRecords(records)
      console.log(`Loaded ${records.length} tracking records`)
    } catch (err) {
      console.error("Error loading tracking records:", err)
      // Don't throw error, just log it and continue
      // This is not a critical error, user can still see exercises
      console.warn("Will continue without tracking records")
    }
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!selectedExercise || !userEmail) return

    setIsSubmitting(true)
    setError(null)
    setSuccess(null)

    try {
      // Create a clean object with only defined values for Firestore
      // Firestore doesn't accept undefined values
      const cleanFormData: {
        difficultyLevel: number;
        energyLevel: number;
        timeToComplete: number;
        notes: string;
        sets?: number;
        reps?: number;
        weight?: number;
      } = {
        difficultyLevel: formData.difficultyLevel,
        energyLevel: formData.energyLevel,
        timeToComplete: formData.timeToComplete,
        notes: formData.notes || "",
      }

      // Only add optional fields if they have values
      if (formData.sets !== undefined && formData.sets !== null) {
        cleanFormData.sets = formData.sets
      }

      if (formData.reps !== undefined && formData.reps !== null) {
        cleanFormData.reps = formData.reps
      }

      if (formData.weight !== undefined && formData.weight !== null) {
        cleanFormData.weight = formData.weight
      }

      const recordData = {
        exerciseId: selectedExercise.exerciseId,
        exerciseName: selectedExercise.exerciseName,
        libraryId: selectedExercise.libraryId,
        workoutReference: selectedExercise.workoutReference,
        completedDate: Timestamp.now(),
        day: selectedExercise.day,
        category: selectedExercise.category,
        ...cleanFormData
      }

      if (editingRecord) {
        // Update existing record
        const recordRef = doc(db, "IF_users", userEmail, "tracking_records", editingRecord.id)
        await updateDoc(recordRef, recordData)

        // Update local state
        setTrackingRecords((prev) =>
          prev.map((record) =>
            record.id === editingRecord.id ? ({ ...recordData, id: editingRecord.id } as TrackingRecord) : record,
          ),
        )

        setSuccess("Exercise tracking updated successfully!")
      } else {
        // Create new record
        const recordRef = doc(collection(db, "IF_users", userEmail, "tracking_records"))
        await setDoc(recordRef, recordData)

        // Update local state
        setTrackingRecords((prev) => [...prev, { ...recordData, id: recordRef.id } as TrackingRecord])

        setSuccess("Exercise tracking saved successfully!")
      }

      // Update tracking document's lastUpdated field
      const trackingRef = doc(db, "IF_users", userEmail, "Profile", "tracking")
      await updateDoc(trackingRef, {
        lastUpdated: Timestamp.now(),
      })

      // Generate and store tracking summary after updating tracking data
      await generateTrackingSummary(userEmail)

      // Reset form
      setFormData({
        difficultyLevel: 3,
        energyLevel: 3,
        timeToComplete: 0,
        sets: undefined,
        reps: undefined,
        weight: undefined,
        notes: "",
      })
      setSelectedExercise(null)
      setEditingRecord(null)
      setShowForm(false)
    } catch (err) {
      console.error("Error saving tracking record:", err)
      setError("Failed to save tracking record. Please try again.")
    } finally {
      setIsSubmitting(false)
    }
  }

  // Generate and store tracking summary in the tracking_summaries collection
  const generateTrackingSummary = async (email: string) => {
    try {
      console.log("Generating tracking summary for user:", email)

      // 1. Fetch all tracking records
      const recordsRef = collection(db, "IF_users", email, "tracking_records")
      const q = query(recordsRef)
      const querySnapshot = await getDocs(q)

      const records: TrackingRecord[] = []
      querySnapshot.forEach((doc) => {
        records.push({ id: doc.id, ...doc.data() } as TrackingRecord)
      })

      if (records.length === 0) {
        console.log("No tracking records found, skipping summary generation")
        return
      }

      // 2. Get workout reference from tracking document
      const trackingRef = doc(db, "IF_users", email, "Profile", "tracking")
      const trackingSnap = await getDoc(trackingRef)

      if (!trackingSnap.exists()) {
        console.error("Tracking document not found")
        return
      }

      const trackingData = trackingSnap.data()
      const workoutReference = trackingData.workoutReference || ""

      // 3. Prepare tracking data for the LLM
      const trackingJSON = JSON.stringify(records, null, 2)

      // 4. Generate summary using deepSeek model via groq-ai
      // Determine the work type based on the categories in the records
      const workTypes = new Set<string>()
      records.forEach(record => {
        workTypes.add(record.category)
      })
      const workType = Array.from(workTypes).join(", ")

      // Create the prompt for the LLM
      const prompt = `Analyze the provided workout data ${trackingJSON} and generate a high-level summary that captures the overall trends and key insights. Avoid listing every individual exercise or session verbatim. Instead, focus on aspects such as overall workout frequency, general improvements (e.g., strength, endurance), changes in intensity levels, and any noticeable trends over time. The goal is to provide the client with an insightful overview of their fitness progress, highlighting patterns and areas of improvement rather than a detailed breakdown of every workout.`

      // Call the deepSeek model via groq-ai
      // Since we don't have direct access to the groq-ai module, we'll make a fetch request to an API endpoint
      const response = await fetch("/api/generate-summary", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          userEmail: email,
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to generate summary: ${response.statusText}`)
      }

      const data = await response.json()
      const summary = data.summary

      // 5. Store the summary in Firebase as a new document in a collection
      // Create a new document with auto-generated ID in the tracking_summaries collection
      const summariesCollectionRef = collection(db, "IF_users", email, "tracking_summaries")
      await setDoc(doc(summariesCollectionRef), {
        "date-time": Timestamp.now(),
        work_type: workType,
        workoutReference,
        summary,
      })

      console.log("Tracking summary generated and stored successfully")
    } catch (err) {
      console.error("Error generating tracking summary:", err)
      // Don't throw error, just log it and continue
      // This is not a critical error, user can still track exercises
    }
  }

  // Handle deleting a record
  const handleDeleteRecord = async (record: TrackingRecord) => {
    if (!userEmail) return
    if (!confirm("Are you sure you want to delete this tracking record?")) return

    try {
      const recordRef = doc(db, "IF_users", userEmail, "tracking_records", record.id)
      await deleteDoc(recordRef)

      // Update local state
      setTrackingRecords((prev) => prev.filter((r) => r.id !== record.id))

      // Update tracking document's lastUpdated field
      const trackingRef = doc(db, "IF_users", userEmail, "Profile", "tracking")
      await updateDoc(trackingRef, {
        lastUpdated: Timestamp.now(),
      })

      // Generate and store tracking summary after deleting tracking data
      await generateTrackingSummary(userEmail)

      setSuccess("Tracking record deleted successfully!")
    } catch (err) {
      console.error("Error deleting tracking record:", err)
      setError("Failed to delete tracking record. Please try again.")
    }
  }

  // Handle editing a record
  const handleEditRecord = (record: TrackingRecord) => {
    const exercise = exercises.find((ex) => ex.exerciseId === record.exerciseId)

    if (!exercise) {
      setError("Exercise not found. Cannot edit this record.")
      return
    }

    setSelectedExercise(exercise)
    setEditingRecord(record)
    setFormData({
      difficultyLevel: record.difficultyLevel,
      energyLevel: record.energyLevel,
      timeToComplete: record.timeToComplete,
      sets: record.sets,
      reps: record.reps,
      weight: record.weight,
      notes: record.notes || "",
    })
    setShowForm(true)
  }

  // Get category display name
  const getCategoryName = (category: string) => {
    switch (category) {
      case "warm_up":
        return "Warm-up"
      case "resistance_training":
        return "Resistance Training"
      case "cardio_training":
        return "Cardio Training"
      case "cool_down":
        return "Cool Down"
      default:
        return category
    }
  }

  // Group exercises by day
  const exercisesByDay = exercises.reduce(
    (acc, exercise) => {
      if (!acc[exercise.day]) {
        acc[exercise.day] = []
      }
      acc[exercise.day].push(exercise)
      return acc
    },
    {} as Record<number, Exercise[]>,
  )

  // Group tracking records by day - commented out as it's not currently used
  /*
  const recordsByDay = trackingRecords.reduce(
    (acc, record) => {
      if (!acc[record.day]) {
        acc[record.day] = []
      }
      acc[record.day].push(record)
      return acc
    },
    {} as Record<number, TrackingRecord[]>,
  )
  */

  // Uncomment to debug if needed
  // console.log('Tracking records:', trackingRecords)

  // Check if an exercise has been tracked
  const isExerciseTracked = (exerciseId: string) => {
    return trackingRecords.some((record) => record.exerciseId === exerciseId)
  }

  // Get the most recent tracking record for an exercise
  const getLatestRecord = (exerciseId: string) => {
    const records = trackingRecords.filter((record) => record.exerciseId === exerciseId)
    if (records.length === 0) return null

    return records.reduce((latest, record) => {
      return latest.completedDate.toMillis() > record.completedDate.toMillis() ? latest : record
    }, records[0])
  }

  // Add a timeout to prevent infinite loading (second timeout as a backup)
  useEffect(() => {
    // Only set a backup timeout if we're loading and already initialized
    // This prevents duplicate timeouts with the first loading effect
    if (isLoading && isInitialized) {
      const timeout = setTimeout(() => {
        console.error("Backup loading timeout reached")
        setIsLoading(false)
        setError("Loading timed out. Please try again or refresh the page.")
      }, 20000) // 20 seconds timeout (longer than the first one)

      return () => clearTimeout(timeout)
    }
  }, [isLoading, isInitialized])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-purple-500 mx-auto mb-4" />
          <p className="text-gray-300">Loading your tracking data...</p>
          <p className="text-xs text-gray-500 mt-2">This may take a moment if this is your first time using the tracking feature.</p>
        </div>
      </div>
    )
  }

  // If no user email is available, show a sign-in prompt
  if (!userEmail) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center bg-slate-800/50 p-8 rounded-xl max-w-md">
          <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Authentication Required</h3>
          <p className="text-gray-300 mb-4">
            Please sign in to track your exercises. Your progress will be saved to your account.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Success/Error Messages */}
      <AnimatePresence>
        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-red-500/20 border border-red-500/30 text-red-200 px-4 py-3 rounded-lg mb-4 flex items-center flex-wrap"
          >
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <p className="flex-grow">{error}</p>
            <div className="flex items-center mt-2 sm:mt-0 ml-auto">
              <button
                onClick={handleRetry}
                className="text-xs bg-red-500/30 hover:bg-red-500/50 px-2 py-1 rounded mr-2"
              >
                Try Again
              </button>
              <button
                onClick={() => setError(null)}
                className="text-red-200 hover:text-white"
              >
                &times;
              </button>
            </div>
          </motion.div>
        )}

        {success && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="bg-green-500/20 border border-green-500/30 text-green-200 px-4 py-3 rounded-lg mb-4 flex items-center"
          >
            <CheckCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <p>{success}</p>
            <button
              onClick={() => setSuccess(null)}
              className="ml-auto text-green-200 hover:text-white"
            >
              &times;
            </button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Form for adding/editing tracking */}
      <AnimatePresence>
        {showForm && selectedExercise && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-6 overflow-hidden"
          >
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl border border-purple-500/20 p-4 max-h-[calc(100vh-250px)] overflow-y-auto md:max-h-none scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-white">
                  {editingRecord ? "Edit Tracking" : "Add Tracking"}: {selectedExercise.exerciseName}
                </h3>
                <button
                  onClick={() => {
                    setShowForm(false)
                    setSelectedExercise(null)
                    setEditingRecord(null)
                  }}
                  className="text-gray-400 hover:text-white"
                >
                  &times;
                </button>
              </div>

              <form onSubmit={handleSubmit}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {/* Difficulty Level */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Difficulty Level
                    </label>
                    <div className="flex items-center">
                      {[1, 2, 3, 4, 5].map((level) => (
                        <button
                          key={level}
                          type="button"
                          onClick={() => setFormData({ ...formData, difficultyLevel: level })}
                          className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 transition-colors ${
                            formData.difficultyLevel >= level
                              ? "bg-purple-600 text-white"
                              : "bg-slate-700 text-gray-400"
                          }`}
                        >
                          {level}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Energy Level */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Energy Level
                    </label>
                    <div className="flex items-center">
                      {[1, 2, 3, 4, 5].map((level) => (
                        <button
                          key={level}
                          type="button"
                          onClick={() => setFormData({ ...formData, energyLevel: level })}
                          className={`w-8 h-8 rounded-full flex items-center justify-center mr-2 transition-colors ${
                            formData.energyLevel >= level
                              ? "bg-green-600 text-white"
                              : "bg-slate-700 text-gray-400"
                          }`}
                        >
                          {level}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Time to Complete */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-1">
                      Time to Complete (minutes)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={formData.timeToComplete}
                      onChange={(e) => setFormData({ ...formData, timeToComplete: Number.parseInt(e.target.value) || 0 })}
                      className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white"
                    />
                  </div>

                  {/* Sets (only for resistance training) */}
                  {selectedExercise.category === "resistance_training" && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Sets
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={formData.sets || ""}
                        onChange={(e) => setFormData({ ...formData, sets: Number.parseInt(e.target.value) || undefined })}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white"
                      />
                    </div>
                  )}

                  {/* Reps (only for resistance training) */}
                  {selectedExercise.category === "resistance_training" && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Reps
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={formData.reps || ""}
                        onChange={(e) => setFormData({ ...formData, reps: Number.parseInt(e.target.value) || undefined })}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white"
                      />
                    </div>
                  )}

                  {/* Weight (only for resistance training) */}
                  {selectedExercise.category === "resistance_training" && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-1">
                        Weight (kg)
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.5"
                        value={formData.weight || ""}
                        onChange={(e) => setFormData({ ...formData, weight: Number.parseFloat(e.target.value) || undefined })}
                        className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white"
                      />
                    </div>
                  )}
                </div>

                {/* Notes */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Notes
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    className="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white h-20 resize-none"
                    placeholder="Add any notes about this exercise session..."
                  />
                </div>

                <div className="flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => {
                      setShowForm(false)
                      setSelectedExercise(null)
                      setEditingRecord(null)
                    }}
                    className="px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? (
                      <React.Fragment key="saving">
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Saving...
                      </React.Fragment>
                    ) : (
                      <React.Fragment key="save">
                        <Save className="w-4 h-4" />
                        {editingRecord ? "Update" : "Save"}
                      </React.Fragment>
                    )}
                  </button>
                </div>
              </form>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Day Selector - Only show when not in kick-starter modal */}
      {!propActiveDay && (
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">Select Day:</label>
          <select
            value={activeDay}
            onChange={(e) => setActiveDay(Number(e.target.value))}
            className="w-full px-4 py-2 rounded-lg bg-slate-800 text-white border border-slate-600 focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            {Object.keys(exercisesByDay)
              .sort((a, b) => Number.parseInt(a) - Number.parseInt(b))
              .map((day) => (
                <option key={day} value={day}>
                  Day {day}
                </option>
              ))}
          </select>
        </div>
      )}

      {/* Exercise List */}
      <div className="flex-1 overflow-y-auto pr-2 scrollbar-thin scrollbar-track-white/5 scrollbar-thumb-purple-500/20 max-h-[calc(100vh-250px)] md:max-h-none">
        {Object.keys(exercisesByDay).length > 0 ? (
          <div className="space-y-6">
            {Object.entries(exercisesByDay)
              .filter(([day]) => {
                console.log(`Filtering day ${day}, activeDay is ${activeDay}, comparison: ${Number(day) === activeDay}`);
                return Number(day) === activeDay;
              })
              .map(([day, dayExercises]) => (
                <div key={day} className="bg-slate-800/30 backdrop-blur-sm rounded-xl border border-white/5 p-4">
                  <h3 className="text-lg font-semibold text-white mb-4">Day {day}</h3>

                  <div className="space-y-4">
                    {/* Group exercises by category */}
                    {["warm_up", "resistance_training", "cardio_training", "cool_down"].map((category) => {
                      const categoryExercises = dayExercises.filter(ex => ex.category === category)
                      if (categoryExercises.length === 0) return <React.Fragment key={`empty-${category}`}></React.Fragment>

                      return (
                        <div key={category} className="space-y-2">
                          <h4 className="text-sm font-medium text-purple-400">{getCategoryName(category)}</h4>

                          <div className="space-y-2">
                            {categoryExercises.map((exercise) => {
                              const isTracked = isExerciseTracked(exercise.exerciseId)
                              const latestRecord = getLatestRecord(exercise.exerciseId)

                              return (
                                <div
                                  key={exercise.exerciseId}
                                  className={`p-3 rounded-lg transition-colors ${
                                    isTracked
                                      ? "bg-green-900/20 border border-green-500/30"
                                      : "bg-slate-700/30 border border-slate-600/30 hover:bg-slate-700/50"
                                  } overflow-hidden break-words`}
                                >
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <h5 className="font-medium text-white flex items-center">
                                        {exercise.exerciseName}
                                        {isTracked && (
                                          <CheckCircle className="w-4 h-4 ml-2 text-green-400" />
                                        )}
                                      </h5>

                                      {latestRecord && (
                                        <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs text-gray-400">
                                          <div key={`date-${latestRecord.id}`} className="flex items-center">
                                            <Calendar className="w-3 h-3 mr-1 text-blue-400" />
                                            {format(latestRecord.completedDate.toDate(), "MMM d, yyyy")}
                                          </div>

                                          <div key={`time-${latestRecord.id}`} className="flex items-center">
                                            <Clock className="w-3 h-3 mr-1 text-blue-400" />
                                            {latestRecord.timeToComplete} min
                                          </div>

                                          <div key={`difficulty-${latestRecord.id}`} className="flex items-center">
                                            <BarChart3 className="w-3 h-3 mr-1 text-purple-400" />
                                            Difficulty: {latestRecord.difficultyLevel}/5
                                          </div>

                                          <div key={`energy-${latestRecord.id}`} className="flex items-center">
                                            <Battery className="w-3 h-3 mr-1 text-green-400" />
                                            Energy: {latestRecord.energyLevel}/5
                                          </div>

                                          {latestRecord.category === "resistance_training" && (
                                            <div key={`resistance-stats-${latestRecord.id}`}>
                                              {latestRecord.sets && (
                                                <div key={`sets-${latestRecord.id}`} className="flex items-center">
                                                  <Dumbbell className="w-3 h-3 mr-1 text-yellow-400" />
                                                  Sets: {latestRecord.sets}
                                                </div>
                                              )}

                                              {latestRecord.reps && (
                                                <div key={`reps-${latestRecord.id}`} className="flex items-center">
                                                  <Dumbbell className="w-3 h-3 mr-1 text-yellow-400" />
                                                  Reps: {latestRecord.reps}
                                                </div>
                                              )}

                                              {latestRecord.weight && (
                                                <div key={`weight-${latestRecord.id}`} className="flex items-center">
                                                  <Dumbbell className="w-3 h-3 mr-1 text-yellow-400" />
                                                  Weight: {latestRecord.weight} kg
                                                </div>
                                              )}
                                            </div>
                                          )}
                                        </div>
                                      )}

                                      {latestRecord && latestRecord.notes && (
                                        <p className="mt-2 text-xs text-gray-400 italic">
                                          &ldquo;{latestRecord.notes}&rdquo;
                                        </p>
                                      )}
                                    </div>

                                    <div className="flex gap-2">
                                      {latestRecord ? (
                                        <React.Fragment key={`actions-${latestRecord.id}`}>
                                          <button
                                            onClick={() => handleEditRecord(latestRecord)}
                                            className="p-1.5 bg-blue-600/30 hover:bg-blue-600/50 text-blue-200 rounded-lg transition-colors"
                                            title="Edit tracking"
                                          >
                                            <Plus className="w-4 h-4" />
                                          </button>
                                          <button
                                            onClick={() => handleDeleteRecord(latestRecord)}
                                            className="p-1.5 bg-red-600/30 hover:bg-red-600/50 text-red-200 rounded-lg transition-colors"
                                            title="Delete tracking"
                                          >
                                            <Trash2 className="w-4 h-4" />
                                          </button>
                                        </React.Fragment>
                                      ) : (
                                        <button
                                          onClick={() => {
                                            setSelectedExercise(exercise)
                                            setEditingRecord(null)
                                            setFormData({
                                              difficultyLevel: 3,
                                              energyLevel: 3,
                                              timeToComplete: 0,
                                              sets: undefined,
                                              reps: undefined,
                                              weight: undefined,
                                              notes: ""
                                            })
                                            setShowForm(true)
                                          }}
                                          className="p-1.5 bg-purple-600/30 hover:bg-purple-600/50 text-purple-200 rounded-lg transition-colors"
                                          title="Add tracking"
                                        >
                                          <Plus className="w-4 h-4" />
                                        </button>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              ))}
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <Dumbbell className="w-8 h-8 text-purple-400" />
              </div>
              <h3 className="text-lg font-medium text-white mb-2">No exercises found</h3>
              <p className="text-gray-400 max-w-md">
                We couldn&apos;t find any exercises in your workout plan. Make sure you have a kick starter plan set up.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

