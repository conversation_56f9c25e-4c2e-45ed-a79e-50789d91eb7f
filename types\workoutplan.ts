// types/workoutplan.ts
export interface ConsolidatedPlan {
    userProfileSummary: string;
    workoutPlan: {
      shortTermPlan: string;
      longTermPlan: string;
      exerciseProgram: string;
      formTechniqueGuidance: string;
    };
    nutritionAdvice: string;
  }

export interface Phase {
  name: string;
  timeFrame: string;
  focus: string;
  progress: number;
}

export const PLAN_TYPES = {
  KICK_STARTER: "Kick Starter",
  SEVEN_DAY: "7-Day Workout Plan",
  FITNESS_JOURNEY: "Fitness Journey Plan",
  PERSONAL_TRAINER: "Personal Trainer"
} as const;

//export type PlanType = typeof PLAN_TYPES[keyof typeof PLAN_TYPES];


export interface Exercise {
  exerciseName: string;
  specs: {
    duration: number;
    sets?: number;
    reps?: number;
  };
  coachInstructions: {
    instructions: string;
    mistakes: string;
    modifications: string;
  };
}

export interface FitnessJourneyData {
  schedule?: {
    [key: string]: {
      exercises: Exercise[];
    };
  };
  notes?: string[];
  strategies?: {
    maintainMotivation: string;
    overcomePlateaus: string;
    variety: string;
  };
}