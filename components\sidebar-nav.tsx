import Link from "next/link"
import { UserIcon } from "@heroicons/react/24/solid"

function Sidebar() {
  return (
    <aside className="bg-gray-100 w-64 p-4">
      <h2 className="text-xl font-bold mb-4">Navigation</h2>
      <nav>
        <Link href="/dashboard" className="flex items-center space-x-2 p-2 hover:bg-gray-200 rounded-lg">
          <span>Dashboard</span>
        </Link>
        <Link href="/profile/complete" className="flex items-center space-x-2 p-2 hover:bg-gray-100 rounded-lg">
          <UserIcon className="w-5 h-5" />
          <span>Edit Profile</span>
        </Link>
        {/* Add more navigation links here */}
      </nav>
    </aside>
  )
}

export default Sidebar

