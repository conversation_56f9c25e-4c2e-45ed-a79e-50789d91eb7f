// ChatHistory.tsx
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { X, ChevronDown, ChevronRight } from "lucide-react";

// Chat history item interface
interface ChatHistoryItem {
  id: string;
  title: string;
  createdAt: string;
}

// Interface for grouped chat history
interface GroupedChatHistory {
  [key: string]: ChatHistoryItem[];
}

interface ChatHistoryProps {
  isVisible: boolean;
  onClose: () => void;
  chatHistory: Array<ChatHistoryItem>;
  currentChatId: string | null;
  onChatSelect: (chatId: string) => void;
}

// Date utility functions
const isToday = (date: Date): boolean => {
  const today = new Date();
  return (
    date.getDate() === today.getDate() &&
    date.getMonth() === today.getMonth() &&
    date.getFullYear() === today.getFullYear()
  );
};

const isYesterday = (date: Date): boolean => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return (
    date.getDate() === yesterday.getDate() &&
    date.getMonth() === yesterday.getMonth() &&
    date.getFullYear() === yesterday.getFullYear()
  );
};

const isWithinInterval = (date: Date, interval: { start: Date; end: Date }): boolean => {
  return date >= interval.start && date <= interval.end;
};

const subDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() - days);
  return result;
};

// Format date based on the group
const formatChatDate = (dateString: string, group: string): string => {
  const date = new Date(dateString);
  
  // Format time part
  const timeOptions: Intl.DateTimeFormatOptions = { 
    hour: '2-digit', 
    minute: '2-digit' 
  };
  const timeStr = date.toLocaleTimeString([], timeOptions);
  
  // For Today and Yesterday, just show the time
  if (group === 'Today' || group === 'Yesterday') {
    return timeStr;
  }
  
  // For older dates, show full date and time
  const dateOptions: Intl.DateTimeFormatOptions = { 
    day: '2-digit', 
    month: '2-digit', 
    year: 'numeric'
  };
  const dateStr = date.toLocaleDateString([], dateOptions);
  return `${dateStr} ${timeStr}`;
};

export function ChatHistory({
  isVisible,
  onClose,
  chatHistory,
  currentChatId,
  onChatSelect,
}: ChatHistoryProps) {
  // State for grouped chat history
  const [groupedChatHistory, setGroupedChatHistory] = useState<GroupedChatHistory>({});
  
  // State to track which groups are expanded - all collapsed by default
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Group chat history by date
  useEffect(() => {
    const grouped = chatHistory.reduce((acc: GroupedChatHistory, chat) => {
      const date = new Date(chat.createdAt);
      let group = '';

      if (isToday(date)) {
        group = 'Today';
      } else if (isYesterday(date)) {
        group = 'Yesterday';
      } else if (isWithinInterval(date, { start: subDays(new Date(), 7), end: subDays(new Date(), 2) })) {
        group = 'Last 7 days';
      } else if (isWithinInterval(date, { start: subDays(new Date(), 30), end: subDays(new Date(), 8) })) {
        group = 'Previous 30 days';
      } else {
        group = 'Over a month';
      }

      if (!acc[group]) {
        acc[group] = [];
      }
      acc[group].push(chat);
      return acc;
    }, {});

    // Sort chats within each group in descending order by date
    Object.keys(grouped).forEach(group => {
      grouped[group].sort((a, b) => {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
    });

    // Ensure groups are displayed in the correct order
    const orderedGroups = ['Today', 'Yesterday', 'Last 7 days', 'Previous 30 days', 'Over a month'];
    const orderedGroupedHistory = orderedGroups.reduce((acc, key) => {
      if (grouped[key]) {
        acc[key] = grouped[key];
      }
      return acc;
    }, {} as GroupedChatHistory);

    setGroupedChatHistory(orderedGroupedHistory);
    
    // Initialize all groups as collapsed if expandedGroups is empty
    if (Object.keys(expandedGroups).length === 0) {
      setExpandedGroups(orderedGroups.reduce((acc, key) => ({ ...acc, [key]: false }), {}));
    }
  }, [chatHistory]);

  // Toggle group expansion
  const toggleGroup = (group: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [group]: !prev[group]
    }));
  };

  return (
    <motion.div
      initial={{ x: "-100%" }}
      animate={{ x: isVisible ? 0 : "-100%" }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="absolute md:relative z-10 w-full md:w-1/4 bg-black/95 md:bg-purple-500/20 backdrop-blur-md border-r border-white/10 h-full overflow-y-auto rounded-r-2xl"
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-base font-bold text-green-500">Chat History</h3>
          <button
            onClick={onClose}
            className="md:hidden p-1 text-gray-400 hover:text-white hover:bg-white/10 rounded-full transition-all duration-200"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {Object.keys(groupedChatHistory).length > 0 ? (
          <div className="space-y-3">
            {Object.entries(groupedChatHistory).map(([group, chats]) => (
              <div key={group} className="overflow-hidden rounded-lg">
                <button
                  onClick={() => toggleGroup(group)}
                  className="w-full flex items-center justify-between py-2 px-3 bg-white/5 hover:bg-white/10 transition-colors rounded-lg border border-white/10"
                >
                  <span className="text-sm font-medium text-gray-200 ">{group}</span>
                  {expandedGroups[group] ? (
                    <ChevronDown className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                  )}
                </button>
                
                {expandedGroups[group] && (
                  <ul className="mt-1 space-y-1 pl-2">
                    {chats.map((chat) => (
                      <li 
                        key={chat.id}
                        onClick={() => onChatSelect(chat.id)}
                        className={`py-2 px-3 rounded-lg text-sm cursor-pointer transition-colors ${
                          currentChatId === chat.id ? 'bg-white/20 text-white font-medium' : 'text-gray-300 hover:bg-white/10'
                        }`}
                      >
                        <div className="flex flex-col">
                          {/* <span className="mb-1">{chat.title}</span> */}
                          <span className="text-xs text-gray-400">{formatChatDate(chat.createdAt, group)}</span>
                        </div>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="py-2 px-3 text-gray-400 text-sm">No chat history</div>
        )}
      </div>
    </motion.div>
  );
}

export default ChatHistory;