"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { onAuthStateChanged, User } from "firebase/auth";
import { auth, db } from "@/components/firebase/config";
import { doc, getDoc } from "firebase/firestore";
import { checkWorkoutPlanExists } from "@/utils/firebase";
import { Loader2 } from "lucide-react";

interface PageState {
  isLoading: boolean;
  loadingMessage: string;
  error: string | null;
}

interface AuthLayoutProps {
  children: React.ReactNode;
}

export default function WorkoutDashboardLayout({ children }: AuthLayoutProps) {
  const [pageState, setPageState] = useState<PageState>({
    isLoading: true,
    loadingMessage: "Checking access...",
    error: null,
  });
  const router = useRouter();

  /**
   * Checks if the specified plan's document exists in Firestore.
   * Each plan is stored under the user's Profile collection.
   */
  const checkSelectedPlan = async (userEmail: string, planType: string): Promise<boolean> => {
    try {
      let docRef;
      if (planType === "Kick Starter") {
        docRef = doc(db, "IF_users", userEmail, "Profile", "kick_starter");
      } else if (planType === "7-Day Workout Plan") {
        docRef = doc(db, "IF_users", userEmail, "Profile", "shortTerm");
      } else if (planType === "Fitness Journey Plan") {
        docRef = doc(db, "IF_users", userEmail, "Profile", "longTerm");
      } else if (planType === "Personal Trainer") {
        docRef = doc(db, "IF_users", userEmail, "Profile", "schedule");
      }

      if (!docRef) return false;

      const docSnap = await getDoc(docRef);
      return docSnap.exists();
    } catch (error) {
      console.error("Error checking selected plan:", error);
      return false;
    }
  };

  useEffect(() => {
    let isSubscribed = true;

    const checkAccess = async (user: User | null) => {
      try {
        if (!user?.email) {
          if (isSubscribed) {
            router.replace("/auth/signin");
          }
          return;
        }

        // Update the loading message to reflect that the workout plan is being verified.
        if (isSubscribed) {
          setPageState((prev) => ({
            ...prev,
            loadingMessage: "Verifying workout plan..."
          }));
        }

        // Execute all checks in parallel and destructure all five results.
        const [
          hasWorkoutPlanExists,
          hasKickStarter,
          hasShortTerm,
          hasLongTerm,
          hasPersonTrainer
        ] = await Promise.all([
          checkWorkoutPlanExists(user.email),
          checkSelectedPlan(user.email, "Kick Starter"),
          checkSelectedPlan(user.email, "7-Day Workout Plan"),
          checkSelectedPlan(user.email, "Fitness Journey Plan"),
          checkSelectedPlan(user.email, "Personal Trainer")
        ]);

        // If no workout plan has been generated at all, notify the user.
        if (!hasWorkoutPlanExists) {
          if (isSubscribed) {
            setPageState({
              isLoading: false,
              loadingMessage: "",
              error: "No workout plan found. Please generate a plan first.",
            });
          }
          setTimeout(() => router.replace("/dashboard"), 2000);
          return;
        }

        // If none of the selected plan documents exist, notify the user to select one.
        if (!hasKickStarter && !hasShortTerm && !hasLongTerm && !hasPersonTrainer) {
          if (isSubscribed) {
            setPageState({
              isLoading: false,
              loadingMessage: "",
              error: "No selected plan found. Please select a plan first.",
            });
          }
          setTimeout(() => router.replace("/workout-selector"), 2000);
          return;
        }

        // If at least one valid plan exists, proceed without error.
        if (isSubscribed) {
          setPageState({
            isLoading: false,
            loadingMessage: "",
            error: null,
          });
        }
      } catch (error) {
        if (isSubscribed) {
          const errorMessage =
            error instanceof Error ? error.message : "An error occurred checking access.";
          setPageState({
            isLoading: false,
            loadingMessage: "",
            error: errorMessage,
          });
          setTimeout(() => router.replace("/dashboard"), 2000);
        }
      }
    };

    // Listen for authentication state changes.
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (!user) {
        router.replace("/auth/signin");
        return;
      }
      checkAccess(user);
    });

    return () => {
      isSubscribed = false;
      unsubscribe();
    };
  }, [router]);

  // Render a loading indicator while verifying access.
  if (pageState.isLoading) {
    return (
      <div className="fixed inset-0 flex flex-col items-center justify-center bg-white">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-4" />
        <div className="text-lg text-gray-600">{pageState.loadingMessage}</div>
      </div>
    );
  }

  // Render an error message if there was an issue.
  if (pageState.error) {
    return (
      <div className="fixed inset-0 flex flex-col items-center justify-center bg-white">
        <div className="text-lg text-red-600 mb-2">{pageState.error}</div>
        <div className="text-sm text-gray-500">Redirecting...</div>
      </div>
    );
  }

  // When all checks pass, render the children components.
  return <>{children}</>;
}
