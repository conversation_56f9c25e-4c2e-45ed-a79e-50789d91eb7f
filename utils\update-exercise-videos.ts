// scripts/update-exercise-videos.ts
import { db } from "@/components/firebase/config";
import { collection, getDocs, doc,  writeBatch, query, limit } from "firebase/firestore";
import { YouTubeSearchTool } from "../components/tools/YouTubeSearchTool";


// Define the Exercise interface
interface Exercise {
  name: string;
  focusArea: string;
  description: string;
  level: string;
  muscleGroups: string[];
  equipment: string;
  variations: string[];
  image?: string;
  video?: string;
  urlVideo?: string;
  id: string;
}

interface UpdateProgress {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
  skipped: number;
}

/**
 * Main function to update exercise videos
 * This will fetch exercises from Firestore, search for YouTube videos,
 * and update the Firestore documents with video URLs
 */
export async function updateExerciseVideos(
  progressCallback?: (progress: UpdateProgress) => void,
  batchSize: number = 5,
  testMode: boolean = false
): Promise<{
  success: boolean;
  message: string;
  stats: UpdateProgress;
}> {
  // Initialize tools and progress tracking
  const youtubeSearchTool = new YouTubeSearchTool({
    maxResults: 3,
    preferOfficial: true,
    useMock: testMode, // Use mock data in test mode
  });

  const progress: UpdateProgress = {
    total: 0,
    processed: 0,
    succeeded: 0,
    failed: 0,
    skipped: 0
  };

  try {
    console.log("Starting exercise video update process...");

    // Fetch exercises from Firestore
    let exerciseQuery;
    if (testMode) {
      // In test mode, only process a few exercises
      exerciseQuery = query(collection(db, "Fitness Library"), limit(3));
      console.log("TEST MODE: Only processing 3 exercises");
    } else {
      exerciseQuery = collection(db, "Fitness Library");
    }

    const querySnapshot = await getDocs(exerciseQuery);
    const exercises: Exercise[] = [];

    querySnapshot.forEach((doc) => {
      const exercise = { ...doc.data(), id: doc.id } as Exercise;
      exercises.push(exercise);
    });

    progress.total = exercises.length;
    if (progressCallback) progressCallback(progress);

    console.log(`Found ${exercises.length} exercises to process`);

    // Process exercises in batches to avoid API rate limits
    for (let i = 0; i < exercises.length; i += batchSize) {
      const batch = exercises.slice(i, i + batchSize);
      const batchWriteRef = writeBatch(db);

      console.log(`Processing batch ${Math.floor(i / batchSize) + 1} of ${Math.ceil(exercises.length / batchSize)}`);

      // Process each exercise in the batch
      const batchPromises = batch.map(async (exercise) => {
        try {
          // Skip if already has a video URL
          if (exercise.urlVideo) {
            progress.skipped++;
            return {
              exerciseId: exercise.id,
              status: 'skipped',
              message: 'Exercise already has a video URL'
            };
          }

          // Generate a detailed search query based on exercise details
          const searchQuery = generateSearchQuery(exercise);
          console.log(`Searching for video for: ${exercise.name} (${exercise.id})`);
          // Search for videos using the public 'search' method
          const searchResult = await youtubeSearchTool.search(searchQuery);

          if (!searchResult.success || !searchResult.results || searchResult.results.length === 0) {
            progress.failed++;
            return {
              exerciseId: exercise.id,
              status: 'failed',
              message: 'No videos found'
            };
          }

          // Get the best match
          const bestMatch = searchResult.results[0];
          const videoUrl = YouTubeSearchTool.getVideoUrl(bestMatch.videoId);
          const embedUrl = YouTubeSearchTool.getEmbedUrl(bestMatch.videoId);

          console.log(`Found video: "${bestMatch.title}" (${bestMatch.videoId}) for ${exercise.name}`);

          // Update the document in the batch
          const exerciseRef = doc(db, "Fitness Library", exercise.id);

          if (!testMode) {
            batchWriteRef.update(exerciseRef, {
              urlVideo: videoUrl,
              embedVideo: embedUrl,
              videoId: bestMatch.videoId,
              videoTitle: bestMatch.title,
              videoChannel: bestMatch.channelTitle,
              videoThumbnail: bestMatch.thumbnailUrl,
              updatedAt: new Date()
            });
          }

          progress.succeeded++;
          return {
            exerciseId: exercise.id,
            status: 'success',
            videoUrl,
            embedUrl,
            videoId: bestMatch.videoId,
            videoTitle: bestMatch.title
          };

        } catch (error) {
          console.error(`Error processing exercise ${exercise.id}:`, error);
          progress.failed++;
          return {
            exerciseId: exercise.id,
            status: 'error',
            message: error instanceof Error ? error.message : 'Unknown error'
          };
        } finally {
          progress.processed++;
          if (progressCallback) progressCallback({ ...progress });
        }
      });

      // Wait for all exercises in the batch to be processed
      const batchResults = await Promise.all(batchPromises);
      console.log(`Batch results:`, batchResults);

      // Commit the batch write
      if (!testMode) {
        await batchWriteRef.commit();
        console.log(`Committed batch updates to Firestore`);
      } else {
        console.log(`TEST MODE: Skipping Firestore batch commit`);
      }

      // Add a delay between batches to avoid rate limiting
      if (i + batchSize < exercises.length) {
        console.log(`Waiting for 1 second before processing next batch...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`Exercise video update complete. Stats:`, progress);

    return {
      success: true,
      message: `Updated ${progress.succeeded} exercises with videos  (${progress.skipped} skipped, ${progress.failed} failed)`,
      stats: progress
    };

  } catch (error) {
    console.error("Error in updateExerciseVideos:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      stats: progress
    };
  }
}

/**
 * Generate a detailed search query based on exercise details
 */
function generateSearchQuery(exercise: Exercise): string {
  const parts = [
    exercise.name,
    exercise.focusArea,
    exercise.equipment
  ];

  // Add muscle groups if available (limit to 2 to avoid too specific queries)
  if (exercise.muscleGroups && exercise.muscleGroups.length > 0) {
    parts.push(exercise.muscleGroups.slice(0, 2).join(' '));
  }

  // Add level for better targeting
  if (exercise.level) {
    parts.push(exercise.level);
  }

  // Combine and add fitness-specific terms
  return `${parts.filter(Boolean).join(' ')} exercise proper form technique`;
}

/**
 * Entry point for running the script directly (e.g., from CLI)
 */
if (require.main === module) {
  (async () => {
    // Track progress in console
    const logProgress = (progress: UpdateProgress) => {
      const percent = Math.floor((progress.processed / progress.total) * 100);
      console.log(`Progress: ${progress.processed}/${progress.total} (${percent}%) - Success: ${progress.succeeded}, Failed: ${progress.failed}, Skipped: ${progress.skipped}`);
    };

    // Run in test mode first
    console.log("Running in TEST MODE first...");
    const testResult = await updateExerciseVideos(logProgress, 3, true);
    console.log("Test result:", testResult);

    // Ask for confirmation to proceed with real update
    // const readline = require().createInterface({
    //   input: process.stdin,
    //   output: process.stdout
    // });

   // readline.question('Proceed with updating all exercises? (yes/no): ', async (answer: string) => {
    //  if (answer.toLowerCase() === 'yes') {
        const result = await updateExerciseVideos(logProgress, 5, false);
        console.log("Result:", result);
     // } else {
        console.log("Update canceled.");
   //   }
  //    readline.close();
      process.exit(0);
    });

}