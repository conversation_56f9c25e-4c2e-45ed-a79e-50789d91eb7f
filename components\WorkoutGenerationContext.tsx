// contexts/WorkoutGenerationContext.tsx
"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { auth } from '@/components/firebase/config';

type GenerationStep = 
  | 'idle'
  | 'initiating'
  | 'generating-profile'
  | 'generating-short term workouts'
  | 'generating-long term workouts'
  | 'saving'
  | 'completed'
  | 'error';

interface WorkoutGenerationContextType {
  generationState: GenerationStep;
  errorMessage: string | null;
  startGeneration: (profileText: string) => Promise<void>;
  resetGeneration: () => void;
  progress: number; // 0-100
}

const WorkoutGenerationContext = createContext<WorkoutGenerationContextType | null>(null);

export const WorkoutGenerationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [generationState, setGenerationState] = useState<GenerationStep>('idle');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const router = useRouter();

  const resetGeneration = () => {
    setGenerationState('idle');
    setErrorMessage(null);
    setProgress(0);
  };

  const startGeneration = async (profileText: string) => {
    try {
      const currentUser = auth.currentUser;
      if (!currentUser || !currentUser.email) {
        setErrorMessage('You must be logged in to generate a workout plan');
        setGenerationState('error');
        return;
      }

      // Set initial state
      setGenerationState('initiating');
      setProgress(10);

      // Simulate step progression to provide feedback
      // In a real implementation, you could use server-sent events or WebSockets
      // to get real-time progress updates from the agent
      const progressSteps = [
        { state: 'generating-profile', progress: 25 },
        { state: 'generating-short term workouts', progress: 50 },
        { state: 'generating-long term workouts', progress: 75 },
        { state: 'saving', progress: 90 }
      ];

      // Set up a sequence of timed updates to simulate progress
      for (const step of progressSteps) {
        await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
        setGenerationState(step.state as GenerationStep);
        setProgress(step.progress);
      }

      // Make the actual API call
      const response = await fetch('/api/generateworkoutplan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userEmail: currentUser.email,
          profileText: profileText,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate workout plan');
      }

      // Successfully completed
      setGenerationState('completed');
      setProgress(100);

      // Redirect to dashboard after a brief delay to show completion
      setTimeout(() => {
        router.push('/dashboard');
        resetGeneration();
      }, 1500);

    } catch (error) {
      console.error('Error generating workout plan:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Unexpected error occurred');
      setGenerationState('error');
    }
  };

  // Reset generation state when component unmounts
  useEffect(() => {
    return () => {
      resetGeneration();
    };
  }, []);

  return (
    <WorkoutGenerationContext.Provider 
      value={{ 
        generationState, 
        errorMessage, 
        startGeneration,
        resetGeneration,
        progress 
      }}
    >
      {children}
    </WorkoutGenerationContext.Provider>
  );
};

export const useWorkoutGeneration = () => {
  const context = useContext(WorkoutGenerationContext);
  if (!context) {
    throw new Error('useWorkoutGeneration must be used within a WorkoutGenerationProvider');
  }
  return context;
};