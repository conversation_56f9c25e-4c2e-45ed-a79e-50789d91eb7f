"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Utensils, Calendar, Check, Loader2, AlertCircle } from "lucide-react"
import { getAuth } from "firebase/auth"
import { getFirestore, doc, getDoc } from "firebase/firestore"
import ScrollableMealPlan from "./ScrollableMealPlan"
import { associateMealPlanWithDay, applyMealPlanToAllDays, getMealPlanForDay } from "@/lib/tools/mealPlanDayAssociation"

interface MealPlanGeneratorProps {
  userEmail?: string
  currentDay?: string
  availableDays?: string[]
}

export default function MealPlanGenerator({
  userEmail: propUserEmail,
  currentDay = "Day 1",
  availableDays = ["Day 1", "Day 3", "Day 5", "Day 7"]
}: MealPlanGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [mealPlan, setMealPlan] = useState<string | null>(null)
  const [dayCount, setDayCount] = useState<number>(1)
  const [includeSnacks, setIncludeSnacks] = useState<boolean>(true)
  const [analyzeDishes, setAnalyzeDishes] = useState<boolean>(true)
  const [mealPlanId, setMealPlanId] = useState<string | null>(null)
  const [selectedWorkoutDay, setSelectedWorkoutDay] = useState<string>(currentDay)
  const [applyToAllDays, setApplyToAllDays] = useState<boolean>(false)
  const [isLoadingExistingPlan, setIsLoadingExistingPlan] = useState<boolean>(false)

  interface AnalysisResult {
    success: boolean;
    extractedDishCount: number;
    processedDishCount: number;
    savedDishCount: number;
    mealPlanId?: string;
    errors: string[];
  }

  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null)

  // Update selectedWorkoutDay when currentDay prop changes
  useEffect(() => {
    if (currentDay) {
      setSelectedWorkoutDay(currentDay)
      loadMealPlanForDay(currentDay)
    }
  }, [currentDay])

  // Load meal plan for the selected day
  const loadMealPlanForDay = async (day: string) => {
    if (!propUserEmail) return

    try {
      setIsLoadingExistingPlan(true)
      setError(null)

      // Get the meal plan ID associated with this day
      const result = await getMealPlanForDay(propUserEmail, day)

      if (result.success && result.mealPlanId) {
        // Fetch the meal plan content from Firestore
        const db = getFirestore()
        const mealPlanRef = doc(db, "foodKhare_users", propUserEmail, "MealPlans", result.mealPlanId)
        const mealPlanSnap = await getDoc(mealPlanRef)

        if (mealPlanSnap.exists()) {
          const mealPlanData = mealPlanSnap.data()
          setMealPlan(mealPlanData.content)
          setMealPlanId(result.mealPlanId)
          console.log(`Loaded meal plan ${result.mealPlanId} for ${day}`)
        } else {
          console.log(`Meal plan ${result.mealPlanId} not found in Firestore`)
          setMealPlan(null)
          setMealPlanId(null)
        }
      } else {
        // No meal plan associated with this day
        console.log(`No meal plan associated with ${day}`)
        setMealPlan(null)
        setMealPlanId(null)
      }
    } catch (error) {
      console.error(`Error loading meal plan for ${day}:`, error)
      setError(`Failed to load meal plan for ${day}`)
    } finally {
      setIsLoadingExistingPlan(false)
    }
  }

  const generateMealPlan = async () => {
    setIsGenerating(true)
    setError(null)
    setMealPlan(null)
    setAnalysisResult(null)
    setMealPlanId(null)

    try {
      const auth = getAuth()
      const user = auth.currentUser
      const email = propUserEmail || user?.email

      if (!email) {
        setError("User not logged in. Please log in to generate a meal plan.")
        setIsGenerating(false)
        return
      }

      // Get Firebase auth token if available
      let authHeader = {};
      if (user) {
        try {
          const token = await user.getIdToken();
          authHeader = { "Authorization": `Bearer ${token}` };
        } catch (tokenError) {
          console.error("Error getting auth token:", tokenError);
        }
      }

      // Call the API to generate the meal plan
      const response = await fetch("/api/generateMealPlan", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...authHeader
        },
        body: JSON.stringify({
          dayCount,
          includeSnacks,
          analyzeDishes,
          userEmail: email
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to generate meal plan")
      }

      const data = await response.json()
      setMealPlan(data.mealPlan)

      // Store the meal plan ID if available
      if (data.mealPlanId) {
        console.log(`Meal plan generated with ID: ${data.mealPlanId}`)
        setMealPlanId(data.mealPlanId)
      }

      if (data.analysis) {
        // If the analysis result contains a meal plan ID, store it
        if (data.analysis.mealPlanId) {
          const newMealPlanId = data.analysis.mealPlanId;
          console.log(`Meal plan ID from analysis: ${newMealPlanId}`)
          setMealPlanId(newMealPlanId)

          // Associate the meal plan with the selected workout day
          if (email) {
            try {
              if (applyToAllDays) {
                // Apply the same meal plan to all available days
                await applyMealPlanToAllDays(email, newMealPlanId, availableDays);
                console.log(`Applied meal plan ${newMealPlanId} to all days`);
              } else {
                // Associate only with the selected day
                await associateMealPlanWithDay(email, selectedWorkoutDay, newMealPlanId);
                console.log(`Associated meal plan ${newMealPlanId} with ${selectedWorkoutDay}`);
              }
            } catch (associationError) {
              console.error("Error associating meal plan with day:", associationError);
              // Don't set an error here as the meal plan was still generated successfully
            }
          }
        }
        setAnalysisResult(data.analysis)
      }
    } catch (err) {
      console.error("Error generating meal plan:", err)
      setError(err instanceof Error ? err.message : "An unknown error occurred")
    } finally {
      setIsGenerating(false)
    }
  }

  // This function is no longer needed as we're handling analysis in the generateMealPlan function
  // Keeping this comment for reference

  return (
    <div className="space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-black/20 backdrop-blur-sm rounded-lg p-4 border border-white/5 hover:border-purple-500/20 transition-all duration-200 hover:shadow-lg hover:shadow-purple-500/10"
      >
        <h3 className="text-base sm:text-lg font-semibold text-white mb-4 flex items-center">
          <Calendar className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-blue-500" />
          Meal Plan Generator
        </h3>

        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Number of Days in Meal Plan
              </label>
              <select
                value={dayCount}
                onChange={(e) => setDayCount(parseInt(e.target.value))}
                className="w-full bg-black/30 border border-gray-700 rounded-md px-3 py-2 text-sm text-white"
                disabled={isGenerating || isLoadingExistingPlan}
              >
                <option value={1}>1 Day</option>
                <option value={3}>3 Days</option>
                <option value={7}>7 Days</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Workout Day
              </label>
              <select
                value={selectedWorkoutDay}
                onChange={(e) => {
                  setSelectedWorkoutDay(e.target.value)
                  loadMealPlanForDay(e.target.value)
                }}
                className="w-full bg-black/30 border border-gray-700 rounded-md px-3 py-2 text-sm text-white"
                disabled={isGenerating || isLoadingExistingPlan}
              >
                {availableDays.map((day) => (
                  <option key={day} value={day}>{day}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="includeSnacks"
              checked={includeSnacks}
              onChange={(e) => setIncludeSnacks(e.target.checked)}
              className="h-4 w-4 rounded border-gray-700 bg-black/30 text-purple-500 focus:ring-purple-500"
              disabled={isGenerating || isLoadingExistingPlan}
            />
            <label htmlFor="includeSnacks" className="ml-2 block text-sm text-gray-300">
              Include Snacks
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="analyzeDishes"
              checked={analyzeDishes}
              onChange={(e) => setAnalyzeDishes(e.target.checked)}
              className="h-4 w-4 rounded border-gray-700 bg-black/30 text-purple-500 focus:ring-purple-500"
              disabled={isGenerating || isLoadingExistingPlan}
            />
            <label htmlFor="analyzeDishes" className="ml-2 block text-sm text-gray-300">
              Analyze Dishes & Save to Recipes
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="applyToAllDays"
              checked={applyToAllDays}
              onChange={(e) => setApplyToAllDays(e.target.checked)}
              className="h-4 w-4 rounded border-gray-700 bg-black/30 text-purple-500 focus:ring-purple-500"
              disabled={isGenerating || isLoadingExistingPlan}
            />
            <label htmlFor="applyToAllDays" className="ml-2 block text-sm text-gray-300">
              Apply this meal plan to all workout days
            </label>
          </div>

          <button
            onClick={generateMealPlan}
            disabled={isGenerating || isLoadingExistingPlan}
            className="w-full flex items-center justify-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : isLoadingExistingPlan ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Loading meal plan...
              </>
            ) : (
              <>
                <Utensils className="w-4 h-4 mr-2" />
                Generate Meal Plan
              </>
            )}
          </button>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-500/20 text-red-300 rounded-md flex items-start">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
            <p className="text-sm">{error}</p>
          </div>
        )}

        {mealPlan && (
          <div className="mt-6">
            <div className="mb-2 flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-2 text-purple-400" />
                <span className="text-sm text-purple-300">
                  Meal Plan for {selectedWorkoutDay}
                  {applyToAllDays && " (Applied to all days)"}
                </span>
              </div>
              {mealPlanId && (
                <div className="text-xs text-gray-400">
                  ID: {mealPlanId.substring(0, 8)}...
                </div>
              )}
            </div>
            <ScrollableMealPlan
              mealPlan={mealPlan}
              userEmail={propUserEmail || (getAuth().currentUser?.email || "")}
              workoutDay={selectedWorkoutDay}
            />
          </div>
        )}

        {analysisResult && (
          <div className="mt-6">
            <h4 className="text-md font-medium text-white mb-3">Analysis Results</h4>
            <div className="bg-black/30 rounded-md p-4 max-h-[300px] overflow-y-auto scrollbar-slick scrollbar-on-hover">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs text-gray-400">Extracted Dishes</p>
                  <p className="text-sm text-white">{analysisResult.extractedDishCount}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-400">Processed Dishes</p>
                  <p className="text-sm text-white">{analysisResult.processedDishCount}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-400">Saved to Recipes</p>
                  <p className="text-sm text-white">{analysisResult.savedDishCount}</p>
                </div>
                <div>
                  <p className="text-xs text-gray-400">Status</p>
                  <p className="text-sm flex items-center">
                    {analysisResult.success ? (
                      <span className="text-green-400 flex items-center">
                        <Check className="w-3 h-3 mr-1" /> Success
                      </span>
                    ) : (
                      <span className="text-red-400 flex items-center">
                        <AlertCircle className="w-3 h-3 mr-1" /> Failed
                      </span>
                    )}
                  </p>
                </div>
              </div>

              {analysisResult.errors && analysisResult.errors.length > 0 && (
                <div className="mt-3">
                  <p className="text-xs text-gray-400 mb-1">Errors</p>
                  <ul className="text-xs text-red-300 list-disc list-inside">
                    {analysisResult.errors.map((error: string, index: number) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}
      </motion.div>
    </div>
  )
}
