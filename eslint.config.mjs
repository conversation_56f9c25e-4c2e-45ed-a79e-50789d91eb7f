import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Handle unused variables more gracefully
      "@typescript-eslint/no-unused-vars": ["error", {
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
        destructuredArrayIgnorePattern: "^_"
      }],
      
      // Specify stricter typing requirements
      "@typescript-eslint/no-explicit-any": "error",
      
      // Enforce const when variables aren't reassigned
      "prefer-const": "error",
      
      // React Hooks dependency rules
      "react-hooks/exhaustive-deps": "warn",
      
      // Add any other custom rules you need
    },
    
    // Optional: specify files this config applies to
    files: ["**/*.ts", "**/*.tsx"]
  }
];

export default eslintConfig;