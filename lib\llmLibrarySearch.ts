// llmLibrarySearch.ts
import { db } from "@/components/firebase/config";
import { collection, getDocs } from "firebase/firestore";
import { createSingleExercise } from "@/utils/upload-exercises";
import { 
  BaseExercise,
  LibraryExercise,
  ProcessedExercise,
  WorkoutDay,
  ProcessedWorkoutDay,
  LibraryExerciseSchema,
  ProcessedWorkoutDaySchema,
} from "@/app/schemas/workout";

interface LibraryEntry {
  id: string;
  exercise: LibraryExercise;
}

interface ExerciseMatch {
  libraryId: string;
  exercise: LibraryExercise;
  confidence: number;
  explanation: string;
}

/**
 * Utility function to normalize text by removing punctuation and converting to lowercase
 */
function normalizeText(text: string): string {
  return text.toLowerCase().replace(/[^\w\s]/gi, '');
}

/**
 * Find the best matching exercise from the library based on name similarity
 * or create a new one if no match is found
 */
async function matchExerciseToLibrary(
  exercise: BaseExercise,
  libraryExercises: LibraryEntry[]
): Promise<ExerciseMatch | null> {
  try {
    if (!libraryExercises.length) {
      console.log('No library exercises available for matching');
      // Try to create new exercise
      const result = await createSingleExercise(exercise.name);
      if (result.success && result.libraryId) {
        // Re-fetch to get the complete exercise data
        const updatedLibrary = await getFitnessLibrary();
        const newExercise = updatedLibrary.find(entry => entry.id === result.libraryId);
        
        if (newExercise) {
          return {
            libraryId: result.libraryId,
            exercise: newExercise.exercise,
            confidence: 1.0, // Perfect match since we just created it
            explanation: "Created new exercise entry"
          };
        }
      }
      return null;
    }

    const normalizedExerciseName = normalizeText(exercise.name);
    let bestMatch: LibraryEntry | null = null;
    let maxSharedWords = 0;
    let sharedWordsString = "";

    for (const libraryEntry of libraryExercises) {
      const normalizedLibraryName = normalizeText(libraryEntry.exercise.name);
      const exerciseWords = normalizedExerciseName.split(/\s+/);
      const libraryWords = normalizedLibraryName.split(/\s+/);
      const sharedWords = exerciseWords.filter(word => libraryWords.includes(word));

      if (sharedWords.length > maxSharedWords) {
        maxSharedWords = sharedWords.length;
        bestMatch = libraryEntry;
        sharedWordsString = sharedWords.join(" ");
      }
    }

    if (!bestMatch) {
      console.log(`No match found for exercise: "${exercise.name}". Creating new entry...`);
      // Try to create new exercise
      const result = await createSingleExercise(exercise.name);
      if (result.success && result.libraryId) {
        console.log ("New exercise added :", exercise.name)
        // Re-fetch to get the complete exercise data
        const updatedLibrary = await getFitnessLibrary();
        const newExercise = updatedLibrary.find(entry => entry.id === result.libraryId);
        
        if (newExercise) {
          return {
            libraryId: result.libraryId,
            exercise: newExercise.exercise,
            confidence: 1.0, // Perfect match since we just created it
            explanation: "Created new exercise entry"
          };
        }
      }
      return null;
    }

    // Calculate confidence based on word match ratio
    const confidence = maxSharedWords / normalizedExerciseName.split(/\s+/).length;

    return {
      libraryId: bestMatch.id,
      exercise: bestMatch.exercise,
      confidence: confidence,
      explanation: `Matched on words: ${sharedWordsString}`
    };

  } catch (error) {
    console.error('Error matching exercise to library:', error);
    return null;
  }
}

/**
 * Process the workout plan by matching exercises to the library
 */
export async function processWorkoutPlan(
  workoutDays: WorkoutDay[]
): Promise<ProcessedWorkoutDay[]> {
  try {
    const libraryExercises = await getFitnessLibrary();
    const processedDays: ProcessedWorkoutDay[] = [];

    for (const day of workoutDays) {
      // Handle rest days or days without exercises
      if (!day.exercises?.length) {
        const restDay = ProcessedWorkoutDaySchema.parse({
          day: day.day,
          exercises: []
        });
        processedDays.push(restDay);
        continue;
      }

      const processedExercises: ProcessedExercise[] = [];

      // Process exercises in batches of 3 to avoid rate limiting
      for (let i = 0; i < day.exercises.length; i += 3) {
        const batch = day.exercises.slice(i, i + 3);
        const matchPromises = batch.map(exercise => 
          matchExerciseToLibrary(exercise, libraryExercises)
        );

        const matches = await Promise.all(matchPromises);

        batch.forEach((exercise, index) => {
          const match = matches[index];
          const processedExercise: ProcessedExercise = {
            ...exercise,
            libraryId: match?.libraryId || null,
            libraryMatch: match?.exercise || null,
            matchConfidence: match?.confidence || 0,
            matchExplanation: match?.explanation || null
          };
          processedExercises.push(processedExercise);
        });

        // Add delay between batches if not at the end
        if (i + 3 < day.exercises.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      const processedDay = ProcessedWorkoutDaySchema.parse({
        day: day.day,
        exercises: processedExercises
      });
      processedDays.push(processedDay);
    }

    return processedDays;
  } catch (error) {
    console.error('Error processing workout plan:', error);
    throw new Error('Failed to process workout plan: ' + 
      (error instanceof Error ? error.message : String(error)));
  }
}

/**
 * Retrieve and validate exercises from the fitness library
 */
export async function getFitnessLibrary(): Promise<LibraryEntry[]> {
  try {
    const exercises: LibraryEntry[] = [];
    const querySnapshot = await getDocs(collection(db, "Fitness Library"));

    for (const doc of querySnapshot.docs) {
      const data = doc.data();
      // Use safeParse instead of parse for more graceful error handling
      const result = LibraryExerciseSchema.safeParse(data);
      
      if (result.success) {
        exercises.push({
          id: doc.id,
          exercise: result.data
        });
      } else {
        // Log the validation error but don't fail completely
        console.warn(`Validation failed for exercise ${doc.id}:`, result.error);
        // Include the exercise with default values
        exercises.push({
          id: doc.id,
          exercise: {
            name: data.name || 'Unknown Exercise',
            focusArea: data.focusArea || '',
            description: data.description || '',
            level: data.level || 'beginner',
            muscleGroups: Array.isArray(data.muscleGroups) ? data.muscleGroups : [],
            equipment: data.equipment || '',
            variations: Array.isArray(data.variations) ? data.variations : []
          }
        });
      }
    }

    if (exercises.length === 0) {
      console.warn('No exercises found in fitness library');
    }

    return exercises;
  } catch (error) {
    console.error('Error fetching fitness library:', error);
    // Return empty array instead of throwing
    return [];
  }
}