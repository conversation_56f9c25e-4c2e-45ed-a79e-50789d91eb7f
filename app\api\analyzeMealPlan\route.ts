import { NextRequest, NextResponse } from "next/server";
import { analyzeMealPlan } from "@/lib/tools/macroNutrientAnalyser";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/authOptions";
import { getAuth } from "firebase-admin/auth";
import { adminApp } from "@/components/firebase-admin";

interface RequestBody {
  mealPlan: string;
  userEmail?: string;
  dayCount?: number;
}

/**
 * API endpoint to analyze a meal plan and save recipes to Firestore
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse the request body first to get any provided userEmail
    const body = await request.json() as RequestBody;
    const { mealPlan, userEmail: bodyUserEmail, dayCount = 1 } = body;

    if (!mealPlan) {
      return NextResponse.json(
        { success: false, error: "Missing required parameter: mealPlan" },
        { status: 400 }
      );
    }

    // Try to get user email from different auth methods
    let userEmail: string | null = null;

    // Method 1: Check NextAuth session
    const session = await getServerSession(authOptions);
    if (session?.user?.email) {
      userEmail = session.user.email;
      console.log("User authenticated via NextAuth:", userEmail);
    }

    // Method 2: Check Firebase auth token in header
    if (!userEmail) {
      const authHeader = request.headers.get("authorization");
      if (authHeader?.startsWith("Bearer ")) {
        const token = authHeader.split("Bearer ")[1];
        try {
          const adminAuth = getAuth(adminApp);
          const decodedToken = await adminAuth.verifyIdToken(token);
          userEmail = decodedToken.email || null;
          console.log("User authenticated via Firebase:", userEmail);
        } catch (authError) {
          console.error("Firebase auth error:", authError);
        }
      }
    }

    // Method 3: Use provided userEmail from request body (least secure, but needed for client-side auth)
    if (!userEmail && bodyUserEmail) {
      userEmail = bodyUserEmail;
      console.log("Using user email from request body:", userEmail);
    }

    // If no user email found, return unauthorized
    if (!userEmail) {
      return NextResponse.json(
        { success: false, error: "Unauthorized - No valid session or authentication" },
        { status: 401 }
      );
    }

    // Analyze the meal plan and save recipes to Firestore
    console.log(`Analyzing meal plan for user: ${userEmail}`);
    const analysisResult = await analyzeMealPlan(
      userEmail,
      mealPlan,
      dayCount
    );

    // Return the analysis result including the meal plan ID if available
    return NextResponse.json({
      success: analysisResult.success,
      extractedDishCount: analysisResult.extractedDishCount,
      processedDishCount: analysisResult.processedDishCount,
      savedCount: analysisResult.savedDishCount,
      mealPlanId: analysisResult.mealPlanId,
      errors: analysisResult.errors
    });
  } catch (error) {
    console.error("Error analyzing meal plan:", error);
    return NextResponse.json(
      {
        success: false,
        savedCount: 0,
        errors: [error instanceof Error ? error.message : "Unknown error occurred"]
      },
      { status: 500 }
    );
  }
}
