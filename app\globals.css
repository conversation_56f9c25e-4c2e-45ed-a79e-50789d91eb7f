@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
}


.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow-y: auto;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.tabList {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 1rem;
}

.tabButton {
  padding: 0.5rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  font-weight: 600;
  color: #4a5568;
}

.tabButton.active {
  color: #3182ce;
  border-bottom: 2px solid #3182ce;
}

.card {
  background-color: #f7fafc;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.badge {
  background-color: #e2e8f0;
  color: #4a5568;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
}

.progressBar {
  height: 0.5rem;
  background-color: #e2e8f0;
  border-radius: 9999px;
  overflow: hidden;
}

.progressBarFill {
  height: 100%;
  background-color: #3182ce;
  transition: width 0.3s ease-in-out;
}

.exerciseList {
  margin-top: 1rem;
}

.exercise {
  background-color: #edf2f7;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
}

.exerciseHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.exerciseDetails {
  font-size: 0.875rem;
  color: #4a5568;
}

@keyframes pulse-glow {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 0.75;
    transform: scale(1.05);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@layer utilities {
  /* Original scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thumb-purple-500\/20::-webkit-scrollbar-thumb {
    background-color: rgba(168, 85, 247, 0.2);
    border-radius: 9999px;
  }

  .scrollbar-track-white\/5::-webkit-scrollbar-track {
    background-color: rgba(255, 255, 255, 0.05);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  /* New slick scrollbar styles */
  .scrollbar-slick {
    scrollbar-width: thin;
  }

  .scrollbar-slick::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-slick::-webkit-scrollbar-track {
    background-color: transparent;
    border-radius: 9999px;
    margin: 4px;
  }

  .scrollbar-slick::-webkit-scrollbar-thumb {
    background-color: rgba(139, 92, 246, 0.3);
    border-radius: 9999px;
    border: 2px solid transparent;
    background-clip: padding-box;
    transition: all 0.3s ease;
  }

  .scrollbar-slick::-webkit-scrollbar-thumb:hover {
    background-color: rgba(139, 92, 246, 0.5);
  }

  .scrollbar-slick::-webkit-scrollbar-corner {
    background-color: transparent;
  }

  /* Hover effect - only show scrollbar on hover */
  .scrollbar-on-hover::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  .scrollbar-on-hover:hover::-webkit-scrollbar-thumb {
    background-color: rgba(139, 92, 246, 0.3);
  }

  .scrollbar-on-hover:hover::-webkit-scrollbar-thumb:hover {
    background-color: rgba(139, 92, 246, 0.5);
  }
}

/* globals.css */
.dark {
  @apply bg-gray-900 text-white;
}
.light {
  @apply bg-gray-50 text-black;
}


@keyframes gradient-pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(74, 222, 128, 0.5); }
  50% { box-shadow: 0 0 15px rgba(74, 222, 128, 0.7); }
  100% { box-shadow: 0 0 5px rgba(74, 222, 128, 0.5); }
}

.animate-gradient-pulse {
  animation: gradient-pulse 2s infinite;
}

.glowing-border {
  animation: glow 1.5s infinite;
}