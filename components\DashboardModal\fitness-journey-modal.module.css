.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start; /* Change to flex-start to align from top */
  justify-content: center;
  z-index: 1000; /* Keep the higher z-index */
  padding-top: 5rem; /* Add padding to push content below header */
}

.modalContent {
  background-color: white;
  padding: 2rem;
  border-radius: 0.5rem;
  width: 90%;
  max-width: 800px;
  max-height: calc(90vh - 5rem); /* Adjust max-height to account for header */
  overflow-y: auto;
  position: relative;
}

/* Rest of the CSS remains unchanged */
.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

.tabList {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  margin-bottom: 1rem;
}

.tabButton {
  padding: 0.5rem 1rem;
  border: none;
  background: none;
  cursor: pointer;
  font-weight: 600;
  color: #4a5568;
}

.tabButton.active {
  color: #3182ce;
  border-bottom: 2px solid #3182ce;
}

.card {
  background-color: #f7fafc;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.badge {
  background-color: #e2e8f0;
  color: #4a5568;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
}

.progressBar {
  height: 0.5rem;
  background-color: #e2e8f0;
  border-radius: 9999px;
  overflow: hidden;
}

.progressBarFill {
  height: 100%;
  background-color: #3182ce;
  transition: width 0.3s ease-in-out;
}

.exerciseList {
  margin-top: 1rem;
}

.exercise {
  background-color: #edf2f7;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 0.5rem;
}

.exerciseHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.exerciseDetails {
  font-size: 0.875rem;
  color: #4a5568;
}

