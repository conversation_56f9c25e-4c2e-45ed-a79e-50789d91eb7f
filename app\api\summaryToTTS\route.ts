import { NextRequest, NextResponse } from "next/server";
import OpenAI from "openai";
import { headers } from "next/headers";
import { callGroqAi } from "@/components/groqClient";
import { createGroqClient } from "@/lib/llms/groq";
import { getAuth } from "firebase-admin/auth";
import { adminApp } from "@/components/firebase-admin";
import { db } from "@/components/firebase/config";
import { storage } from "@/components/firebase/config";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { v4 as uuidv4 } from "uuid";
import { Readable, PassThrough } from "stream";

// Type definitions
interface RequestBody {
  userEmail: string;
}

interface AssessmentData {
  status: string;
  summary: string;
  lastUpdated: string;
  error: string | null;
  lastError?: string;
  audioPath?: string;
}

interface GroqResponse {
  choices: Array<{ message: { content: string } }> | { message: { content: string } };
}

interface OpenAIAudioResponse {
  body: Readable; // Node.js Readable stream
}

// Service initializations
const adminAuth = getAuth(adminApp);

if (!process.env.OPENAI_API_KEY) {
  throw new Error("OPENAI_API_KEY not configured");
}
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Authentication function
async function validateAuth(authHeader: string): Promise<string> {
  if (!authHeader?.startsWith("Bearer ")) {
    throw new Error("Invalid authentication header");
  }
  const token = authHeader.split("Bearer ")[1];
  try {
    const decodedToken = await adminAuth.verifyIdToken(token);
    return decodedToken.uid;
  } catch (error) {
    console.error("Auth Error:", error);
    throw new Error(`Authentication failed: ${error instanceof Error ? error.message : "Unknown error"}`);
  }
}

// Text transformation function
async function transformProfileText(profileText: string, userEmail: string): Promise<string> {
  const groq = createGroqClient({ userEmail });
  if (!groq.apiKey) {
    throw new Error("AI_SERVICE_UNAVAILABLE");
  }
  const response = await callGroqAi({
    messages: [
      {
        role: "system",
        content: `
          You are a highly experienced and professional personal trainer, trusted fitness coach, and friend at Intelligentfitness.ai. 
          You have a deep understanding of various fitness profiles and extensive experience in creating personalized training 
          schedules that help clients achieve their ultimate goals. Your role is to transform a provided profile summary into an 
          informal, friendly, and conversational response that offers actionable advice about the client's fitness status.

          In your response, please:

          Directly Address the Client: Use straightforward language like "Your fitness is…" to clearly communicate your insights.
          Summarize the Client's Fitness Profile: Briefly outline where they currently stand, noting key strengths and areas for improvement.
          Recommend a Tailored Training Schedule: Suggest a direction you will consider when formulating a 
          training plan based on the client's current profile, 
          including suggestions on workouts, frequency, and intensity that align with their unique needs.
          Highlight Ultimate Goals: Reference the client's ultimate fitness goals and explain how the recommended schedule and 
          tips will help them get there.
          Anticipate and Address Concerns: Recognize likely questions or concerns the client may have—such as progress tracking, recovery 
          strategies, or modifications for specific limitations—and offer clear, reassuring advice.
          Keep It Accessible: While your recommendations should be grounded in professional expertise, avoid overly technical 
          language so the advice remains practical, motivating, and easy to understand without going into workout specifics.
          Your tone should combine professional insight with a friendly, supportive approach to empower the client on their fitness journey.
        `.trim(),
      },
      { role: "user", content: profileText, name: userEmail },
    ],
    temperature: 0.9,
    modelName: process.env.GROQ_MODEL || "llama-3.3-70b-versatile",
    maxTokens: 2000,
  }) as GroqResponse;

  const transformedText = (() => {
    if (Array.isArray(response.choices)) {
      return response.choices[0]?.message?.content;
    } else if (response.choices && typeof response.choices === "object" && "message" in response.choices) {
      return (response.choices as { message: { content: string } }).message.content;
    }
    throw new Error("Unexpected Groq response format");
  })();

  if (!transformedText) {
    throw new Error("Failed to transform summary using Groq");
  }
  return transformedText;
}

// Helper function to buffer stream data
async function bufferStream(stream: Readable): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Buffer[] = [];
    stream.on("data", (chunk) => chunks.push(Buffer.from(chunk)));
    stream.on("end", () => resolve(Buffer.concat(chunks)));
    stream.on("error", (err) => reject(err));
  });
}

export async function POST(request: NextRequest) {
  console.log("Starting TTS process");

  try {
    // 1. Authentication
    const headerList = await headers();
    const authHeader = headerList.get("authorization");
    if (!authHeader) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }
    const userId = await validateAuth(authHeader);
    console.log(`Authenticated user ID: ${userId}`);

    // 2. Parse request
    let body: RequestBody;
    try {
      body = await request.json() as RequestBody;
    } catch (error) {
      console.error("JSON Parsing Error:", error);
      return NextResponse.json({ error: "Invalid request: Malformed JSON body" }, { status: 400 });
    }
    const { userEmail } = body;
    if (!userEmail) {
      throw new Error("Invalid request: Missing userEmail");
    }

    // 3. Fetch summary from Firestore
    const assessmentRef = doc(db, "IF_users", userEmail, "Profile", "assessment");
    const assessmentDoc = await getDoc(assessmentRef);
    if (!assessmentDoc.exists()) {
      throw new Error("Assessment not found for user");
    }
    const assessmentData = assessmentDoc.data() as AssessmentData;
    const profileText = assessmentData.summary;
    if (!profileText) {
      throw new Error("No summary available for audio generation");
    }

    // 4. Detect user agent
    const userAgent = headerList.get("user-agent") || "";
    const isSafari = /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent);
    console.log(`User-Agent: ${userAgent}, Detected as Safari: ${isSafari}`);

    // 5. Handle existing audio
    if (assessmentData.audioPath) {
      const storageRef = ref(storage, assessmentData.audioPath);
      try {
        const audioUrl = await getDownloadURL(storageRef);
        const response = await fetch(audioUrl);
        if (!response.ok) {
          throw new Error("Failed to fetch audio file from Storage");
        }

        if (isSafari) {
          // Buffer for Safari
          const audioBuffer = Buffer.from(await response.arrayBuffer());
          return new NextResponse(audioBuffer, {
            headers: {
              "Content-Type": "audio/mpeg",
              "Content-Length": audioBuffer.length.toString(),
              "Cache-Control": "no-cache, no-store, must-revalidate",
            },
          });
        } else {
          // Stream for non-Safari browsers
          if (!response.body) {
            throw new Error("Response body is null");
          }
          const readableStream = new ReadableStream({
            async start(controller) {
              const reader = response.body?.getReader();
              if (!reader) {
                controller.error(new Error("Response body is null"));
                return;
              }
              try {
                while (true) {
                  const { done, value } = await reader.read();
                  if (done) {
                    controller.close();
                    break;
                  }
                  controller.enqueue(value);
                }
              } catch (error) {
                controller.error(error);
              } finally {
                reader.releaseLock();
              }
            },
          });
          return new NextResponse(readableStream, {
            headers: {
              "Content-Type": "audio/mpeg",
              "Transfer-Encoding": "chunked",
              "Cache-Control": "no-cache, no-store, must-revalidate",
            },
          });
        }
      } catch (error) {
        console.error("Error handling existing audio:", error);
        throw new Error(`Failed to handle existing audio: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }

    // 6. Transform text
    console.log("Transforming profile text...");
    const transformedText = await transformProfileText(profileText, userEmail);

    // 7. Generate audio
    console.log("Generating audio...");
    const audioStream = await openai.audio.speech.create({
      model: "tts-1-hd",
      voice: "alloy",
      input: transformedText,
      response_format: "mp3",
    }) as unknown as OpenAIAudioResponse;

    if (!audioStream.body) {
      throw new Error("Audio stream body is null");
    }

    // 8. Handle response based on browser
    if (isSafari) {
      // Buffer for Safari
      const audioBuffer = await bufferStream(audioStream.body);
      console.log("Audio buffered for Safari, size:", audioBuffer.length);

      // Asynchronously upload to Firebase Storage
      (async () => {
        try {
          const fileName = `summary_${new Date().toISOString()}.mp3`;
          const storageRef = ref(storage, `audio/${userEmail}/fitness/${fileName}`);
          await uploadBytes(storageRef, audioBuffer);
          console.log(`Audio uploaded to ${storageRef.fullPath}`);

          await setDoc(
            assessmentRef,
            {
              audioPath: `audio/${userEmail}/fitness/${fileName}`,
            },
            { merge: true }
          );
          console.log("Audio path updated in Firestore assessment");

          const fileId = uuidv4();
          const filesRef = doc(db, `users/${userEmail}/files/${fileId}`);
          await setDoc(
            filesRef,
            {
              category: "fitnessintelligentai",
              type: "audio",
              name: fileName,
              createdAt: new Date().toISOString(),
              path: `audio/${userEmail}/fitness/${fileName}`,
            },
            { merge: true }
          );
          console.log("Audio metadata logged in Firestore");
        } catch (uploadError) {
          console.error("Failed to upload audio to storage:", uploadError);
        }
      })();

      return new NextResponse(audioBuffer, {
        headers: {
          "Content-Type": "audio/mpeg",
          "Content-Length": audioBuffer.length.toString(),
          "Cache-Control": "no-cache, no-store, must-revalidate",
        },
      });
    } else {
      // Stream for non-Safari browsers
      const passThrough = new PassThrough();
      const bufferStreamInstance = new PassThrough();
      audioStream.body.pipe(passThrough); // For streaming to client
      audioStream.body.pipe(bufferStreamInstance); // For buffering and storage

      const readableStream = new ReadableStream({
        start(controller) {
          passThrough.on("data", (chunk) => controller.enqueue(chunk));
          passThrough.on("end", () => controller.close());
          passThrough.on("error", (err) => controller.error(err));
        },
      });

      const response = new NextResponse(readableStream, {
        headers: {
          "Content-Type": "audio/mpeg",
          "Transfer-Encoding": "chunked",
          "Cache-Control": "no-cache, no-store, must-revalidate",
        },
      });

      // Asynchronously buffer and upload to Firebase Storage
      (async () => {
        try {
          const audioBuffer = await bufferStream(bufferStreamInstance);
          console.log("Audio buffered for storage, size:", audioBuffer.length);

          const fileName = `summary_${new Date().toISOString()}.mp3`;
          const storageRef = ref(storage, `audio/${userEmail}/fitness/${fileName}`);
          await uploadBytes(storageRef, audioBuffer);
          console.log(`Audio uploaded to ${storageRef.fullPath}`);

          await setDoc(
            assessmentRef,
            {
              audioPath: `audio/${userEmail}/fitness/${fileName}`,
            },
            { merge: true }
          );
          console.log("Audio path updated in Firestore assessment");

          const fileId = uuidv4();
          const filesRef = doc(db, `users/${userEmail}/files/${fileId}`);
          await setDoc(
            filesRef,
            {
              category: "fitnessintelligentai",
              type: "audio",
              name: fileName,
              createdAt: new Date().toISOString(),
              path: `audio/${userEmail}/fitness/${fileName}`,
            },
            { merge: true }
          );
          console.log("Audio metadata logged in Firestore");
        } catch (uploadError) {
          console.error("Failed to upload audio to storage:", uploadError);
        }
      })();

      return response;
    }
  } catch (error) {
    console.error("TTS Generation Error:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    const statusCode =
      errorMessage.includes("Authentication") ? 401 :
      errorMessage.includes("AI_SERVICE_UNAVAILABLE") ? 503 :
      errorMessage.includes("Invalid") ? 400 : 500;

    return NextResponse.json({
      error: "Failed to generate audio. Try again, click regenerate ",
      details: errorMessage,
      timestamp: new Date().toISOString(),
    }, { status: statusCode });
  }
}